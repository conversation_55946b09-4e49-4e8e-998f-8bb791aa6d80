package ai.exam.auth;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Bean
    public MgtJwtRequestInterceptor mgtJwtRequestInterceptor() {
        return new MgtJwtRequestInterceptor();
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 对所有的API生效
                .allowedOrigins("http://localhost:3000", "https://chans.mynatapp.cc",
                        "https://ai-exam-web.vercel.app", "http://localhost:8081", "http://localhost:8080",
                        "http://localhost:5173", "http://localhost:5174", "https://flash.mynatapp.cc",
                        "https://ai-exam-mgt.vercel.app",
                        "http://examjoyai.com", "https://examjoyai.com",
                        "https://www.examjoyai.com", "http://www.examjoyai.com",
                        "http://admin.examjoyai.com", "https://admin.examjoyai.com")
                .allowedMethods("*")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600); // 1小时内不需要再预检（OPTIONS请求）
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 1. 添加管理后台 JWT 拦截器
        registry.addInterceptor(mgtJwtRequestInterceptor())
                .addPathPatterns("/mgt/**")  // 拦截管理后台的所有请求
                .excludePathPatterns(
                        "/mgt/user/loginByPassword",      // 排除密码登录接口
                        "/mgt/user/loginByPhoneNumber",   // 排除手机登录接口
                        "/mgt/user/sendVerificationCode"  // 排除发送验证码接口
                );

        registry.addInterceptor(new JwtRequestInterceptor())
                .addPathPatterns("/**") // 指定拦截所有请求
                .excludePathPatterns(
                        "/**/api-docs",
                        "/api/home/<USER>",
                        "/swagger-ui/**",
                        "/swagger-ui.html",
                        "/v3/api-docs/**",
                        "/**/register",
                        "/**/banners",
                        "/**/callback",
                        "/**/twitter/generateTwitterOAuthToken",
                        "/MVuTrSF5MW.txt",

                        // 排除管理后台的所有请求
                        "/mgt/**",

                        // 登录相关
                        "/**/admin/**",
                        "/**/login",
                        "/**/sendVerificationCode",

                        // 课程
                        "/api/web/course/menu",
                        "/api/web/course/get_teachers_by_product_id",

                        // 推荐
                        "/api/web/recommend/home/<USER>",

                        // 资讯
                        "/api/web/information/**",

                        // 评论
                        "/api/web/comment/**",

                        // banner
                        "/api/web/banner/**",

                        // 证书
                        "/**/certificates",
                        "/**/certificate/*",

                        // 题库
                        "/api/web/question/questionBankType/list",

                        // 产品
                        "/api/web/product/**",

                        "/api/web/exam_countdown/**"

                );

        registry.addInterceptor(new WeakJwtRequestInterceptor())
                .addPathPatterns(
                        "/api/web/product/course_detail",
                        "/api/web/course/get_course_detail"
                );
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new AccountIdArgumentResolver());
    }
}
