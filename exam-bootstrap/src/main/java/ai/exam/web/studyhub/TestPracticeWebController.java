package ai.exam.web.studyhub;

import ai.exam.common.enums.ExamModeEnum;
import ai.exam.pages.common.vo.BooleanResultVO;
import ai.exam.pages.knowledgeslicing.vo.ExamQuestionInfoVO;
import ai.exam.pages.knowledgeslicing.vo.SubmitAnswerResultVO;
import ai.exam.pages.practice.TestPracticePageApplication;
import ai.exam.pages.practice.vo.*;
import ai.exam.web.studyhub.vo.ExamRecordVO;
import ai.exam.web.studyhub.vo.LocationInfoVO;
import ai.exam.web.studyhub.vo.TestPracticeInfoVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/web/testPractice")
public class TestPracticeWebController {

    @Resource
    private TestPracticePageApplication testPracticePageApplication;

    @GetMapping("/list")
    public List<TestPracticeInfoVO> getTestPracticeList(Long accountId, @RequestParam String certificateCode, @RequestParam String subjectCode) {
        List<ai.exam.pages.practice.vo.TestPracticeInfoVO> from = testPracticePageApplication.getTestPracticeList(accountId, certificateCode, subjectCode);
        return from.stream().map(this::convert).collect(Collectors.toList());
    }

    private TestPracticeInfoVO convert(ai.exam.pages.practice.vo.TestPracticeInfoVO from) {
        TestPracticeInfoVO vo = new TestPracticeInfoVO();

        vo.setQuestionSetId(from.getQuestionSetId());
        vo.setYear(from.getYear());
        vo.setName(from.getName());
        vo.setTotalQuestionCount(from.getTotalQuestionCount());
        vo.setCompletedQuestionCount(from.getCompletedQuestionCount());
        vo.setCorrectQuestionCount(from.getCorrectQuestionCount());
        vo.setExamCount(from.getExamCount());
        vo.setMaxScore(from.getMaxScore());
        vo.setGradePass(from.getGradePass());
        vo.setTotalScore(from.getTotalScore());
        vo.setExaminationDuration(from.getExaminationDuration());
        vo.setIsExam(from.getIsExam());
        return vo;
    }

    // 真题演练-练习模式，加载试题
    @GetMapping("/questions")
    public ExamQuestionInfoVO getPracticeQuestions(Long accountId, @RequestParam String subjectCode, @RequestParam Long questionSetId) {
        return testPracticePageApplication.getPracticeQuestions(accountId, subjectCode, questionSetId);
    }

    // 真题演练-练习模式，提交答案
    @PostMapping("/submitAnswer")
    public SubmitAnswerResultVO submitAnswer(HttpServletRequest request, @RequestBody SubmitTestAnswerVO requestDTO) {
        Long accountId = (Long) request.getAttribute("accountId");
        return testPracticePageApplication.submitAnswer(accountId, requestDTO);
    }

    // 真题演练-考试模式，试题列表
    @GetMapping("/examModeQuestions")
    public ExamModeQuestionAndFavoriteVO getExamModeQuestions(Long accountId, @RequestParam String subjectCode, @RequestParam Long questionSetId) {
        return testPracticePageApplication.getExamModeFavoriteAndQuestions(accountId, subjectCode, questionSetId);
    }

    // 真题演练-考试模式，交卷
    @PostMapping("/submitExamAnswer")
    public SubmitExamAnswerResultVO submitExamAnswer(HttpServletRequest request, @RequestBody SubmitExamAnswerRequestVO submitExamAnswerRequestVO) {
        Long accountId = (Long) request.getAttribute("accountId");
        return testPracticePageApplication.submitExamAnswer(accountId, submitExamAnswerRequestVO);
    }

    // 真题演练-考试模式，考试详情列表
    @GetMapping("/examDetail")
    public ExamDetailVO getExamDetail(Long accountId, @RequestParam Long examRecordId) {
        return testPracticePageApplication.getExamDetail(accountId, examRecordId);
    }

    // 练习模式，清空答题记录
    @PutMapping("/clearRecords")
    public BooleanResultVO clearExerciseRecords(Long accountId, @RequestParam String subjectCode, @RequestParam(required = false) String sectionCode, @RequestParam(required = false) String chapterCode, @RequestParam(required = false) Long questionSetId, @RequestParam String examMode) {
        return testPracticePageApplication.clearExerciseRecords(accountId, subjectCode, sectionCode, chapterCode, questionSetId, examMode);
    }

    // 上次做题记录
    @GetMapping("/lastLocation")
    public LocationInfoVO getLastLocation(Long accountId, @RequestParam String exerciseMode, @RequestParam String subjectCode) {
        return testPracticePageApplication.getLastLocation(accountId, exerciseMode, subjectCode);
    }

    // 考试记录列表
    @GetMapping("/examRecords")
    public List<ExamRecordVO> getExamRecordList(Long accountId, @RequestParam Long questionSetId) {
        return testPracticePageApplication.getExamRecordList(accountId, questionSetId);
    }

    @GetMapping("/detail")
    public TestPracticeInfoVO getTestPracticeDetail(@RequestParam Long questionSetId) {
        return this.convert(testPracticePageApplication.getTestPracticeDetail(questionSetId));
    }

}
