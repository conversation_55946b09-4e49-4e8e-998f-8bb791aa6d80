package ai.exam.web.studyhub.vo;

import ai.exam.pages.knowledgeslicing.vo.AccountExamQuestionVO;
import ai.exam.pages.knowledgeslicing.vo.ExamQuestionVO;
import ai.exam.pages.knowledgeslicing.vo.QuestionFavoriteRecordVO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class ExamQuestionInfoVO {
    private List<ExamQuestionVO> allQuestions;
    private List<AccountExamQuestionVO> completedQuestions;
    private List<QuestionFavoriteRecordVO> favoriteRecordVOS;
}