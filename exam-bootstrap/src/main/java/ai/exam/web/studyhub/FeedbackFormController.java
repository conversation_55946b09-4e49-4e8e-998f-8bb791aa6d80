package ai.exam.web.studyhub;

import ai.exam.pages.common.vo.BooleanResultVO;
import ai.exam.web.studyhub.vo.FeedbackFormRequestVO;
import ai.exam.web.studyhub.vo.FeedbackFormVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/web/feedback")
public class FeedbackFormController {

    @Resource
    private FeedbackFormWebApplication feedbackFormWebApplication;

    /**
     * 纠错保存
     */
    @PostMapping("/save")
    public BooleanResultVO saveFeedback(HttpServletRequest request, @RequestBody FeedbackFormRequestVO feedbackFormRequest) {
        Long accountId = (Long) request.getAttribute("accountId");
        return feedbackFormWebApplication.saveFeedback(accountId, feedbackFormRequest);
    }

    /**
     * 纠错列表
     */
    @GetMapping("/list")
    public List<FeedbackFormVO> list(Long accountId) {
        return feedbackFormWebApplication.list(accountId);
    }
}
