package ai.exam.web.product;

import ai.exam.domain.certificate.CertificateDomain;
import ai.exam.domain.certificate.MajorDO;
import ai.exam.domain.certificate.SubjectDO;
import ai.exam.domain.product.*;
import ai.exam.domain.utils.PriceConverter;
import ai.exam.web.product.vo.ProductDetailRequest;
import ai.exam.web.product.vo.ProductListRequest;
import ai.exam.web.product.vo.ProductQuestionRelatedQuestionsVO;
import ai.exam.web.product.vo.ProductQuestionVO;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ProductQuestionApplication {

    @Resource
    private ProductDomain productDomain;

    @Resource
    private CertificateDomain certificateDomain;

    @Resource
    private ProductDynamicPropertiesRecordDomain productDynamicPropertiesRecordDomain;

    private static final List<String> QUESTION_SET_TYPE_LIST = List.of("AI智练", "真题演练", "模拟考试", "章节题库");

    public List<ProductQuestionVO> getQuestionList(ProductListRequest request) {
        // 1. 调用 ProductDomain.getProductList 查出题库类别的商品列表
        List<ProductDO> productDOList = productDomain.getProductList(
                request.getCertificateCode(),
                request.getMajorCode(),
                request.getSortField(),
                ProductTypeEnum.QUESTION_SET
        );

        List<ProductQuestionVO> result = new ArrayList<>();

        for (ProductDO productDO : productDOList) {
            ProductQuestionVO vo = convertToProductQuestionVOWithAllFields(productDO);

            vo.setQuestionSetTypeList(QUESTION_SET_TYPE_LIST);
            setSubjectsCount(vo);

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取题库商品详情
     *
     * @param request 商品详情请求参数
     * @return 题库商品详情
     */
    public ProductQuestionVO getQuestionDetail(ProductDetailRequest request) {
        // 调用领域层方法获取商品详情
        ProductDO productDO = productDomain.getProductDetail(request.getProductId());

        // 将 ProductDO 转换为 ProductQuestionVO
        ProductQuestionVO vo = convertToProductQuestionVOWithAllFields(productDO);

        vo.setQuestionSetTypeList(QUESTION_SET_TYPE_LIST);

        // 异步记录浏览人数
        productDynamicPropertiesRecordDomain.asyncRecordViewCount(request.getProductId());

        return vo;
    }

    /**
     * 批量获取题库商品详情
     *
     * @param productIds 商品ID列表
     * @return 题库商品详情列表
     */
    public List<ProductQuestionVO> batchGetQuestionDetails(List<Long> productIds) {
        // 调用领域层方法批量获取商品详情
        List<ProductDO> productDOs = productDomain.batchGetProductDetails(productIds);

        Map<String, String> subjectNameMap = getSubjectNameMap(productDOs);

        Map<String, String> majorNameMap = getMajorNameMap(productDOs);

        // 将 ProductDO 列表转换为 ProductQuestionVO 列表，并填充 subjectName 和 majorName
        return productDOs.stream()
                .map(productDO -> {
                    ProductQuestionVO vo = convertToProductQuestionVOWithAllFields(productDO);
                    vo.setSubjectName(subjectNameMap.get(productDO.getSubjectCode()));
                    vo.setMajorName(majorNameMap.get(productDO.getMajorCode()));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    public ProductQuestionRelatedQuestionsVO getRelatedQuestions(Long productId) {
        // 1. 根据 productId 查询当前商品信息
        ProductDO currentProduct = productDomain.getProductDetail(productId);

        if (currentProduct == null) {
            throw new RuntimeException("Product not found with id: " + productId);
        }


        // 2.1 查询相关的题库商品
        String certificateCode = currentProduct.getCertificateCode();
        List<ProductDO> relatedProducts = productDomain.getProductList(certificateCode, null, null, ProductTypeEnum.QUESTION_SET);

        // 2.2 查询证书相关参数
//        Map<String, String> subjectMap = getSubjectsByCertificateCode(certificateCode);
//        Map<String, String> majorMap = getMajorsByCertificateCode(certificateCode);

        // 3. 将相关商品分类为全科和单科
        List<ProductQuestionVO> quanKeProducts = relatedProducts.stream()
                .filter(p -> ProductSecondCategoryEnum.ZHUAN_YE_QUAN_KE.getValue().equals(p.getSecondCategory()))
                .map(this::convertToProductQuestionVOWithAllFields)
                .collect(Collectors.toList());

        List<ProductQuestionVO> danKeProducts = relatedProducts.stream()
                .filter(p -> ProductSecondCategoryEnum.GONG_GONG_DAN_KE.getValue().equals(p.getSecondCategory()) ||
                        ProductSecondCategoryEnum.ZHUAN_YE_DAN_KE.getValue().equals(p.getSecondCategory()))
                .map(this::convertToProductQuestionVOWithAllFields)
                .collect(Collectors.toList());

        // fabu test
        // 4. 组装返回结果
        ProductQuestionRelatedQuestionsVO result = new ProductQuestionRelatedQuestionsVO();
        result.setQuanKe(quanKeProducts);
        result.setDanKe(danKeProducts);

        return result;
    }

    private void setSubjectsCount(ProductQuestionVO vo) {
        List<ProductContentDO> productContents = productDomain.getProductContents(vo.getProductId());
        long count = productContents.stream().filter(p -> p.getProductType().equals(ProductTypeEnum.PRODUCT.getCode())).count();
        if (count == 0) {
            vo.setSubjectsCount(1);
            return;
        }
        vo.setSubjectsCount((int) count);
    }

    @NotNull
    private Map<String, String> getSubjectsByCertificateCode(String certificateCode) {
        List<SubjectDO> subjects = certificateDomain.findSubjectsByCertificateCode(certificateCode, false);
        return subjects.stream().collect(Collectors.toMap(SubjectDO::getSubjectCode, SubjectDO::getSubjectName));
    }

    @NotNull
    private Map<String, String> getMajorsByCertificateCode(String certificateCode) {
        List<MajorDO> majors = certificateDomain.findMajorsByCertificateCode(certificateCode);
        return majors.stream().collect(Collectors.toMap(MajorDO::getMajorCode, MajorDO::getMajorName));
    }

    @NotNull
    private Map<String, String> getSubjectNameMap(List<ProductDO> productDOs) {
        // 获取所有的 subjectCode
        List<String> subjectCodes = productDOs.stream()
                .map(ProductDO::getSubjectCode)
                .distinct()
                .collect(Collectors.toList());


        // 批量获取科目信息
        List<SubjectDO> subjectDOs = certificateDomain.findSubjectsByCodes(subjectCodes);

        // 创建 subjectCode 到 subjectName 的映射
        return subjectDOs.stream().collect(Collectors.toMap(SubjectDO::getSubjectCode, SubjectDO::getSubjectName));
    }

    @NotNull
    private Map<String, String> getMajorNameMap(List<ProductDO> productDOs) {
        // 获取所有的 majorCode
        List<String> majorCodes = productDOs.stream()
                .map(ProductDO::getMajorCode)
                .distinct()
                .collect(Collectors.toList());

        // 批量获取专业信息
        List<MajorDO> majorDOs = certificateDomain.findMajorsByCodes(majorCodes);

        // 创建 majorCode 到 majorName 的映射
        return majorDOs.stream().collect(Collectors.toMap(MajorDO::getMajorCode, MajorDO::getMajorName));
    }

    /**
     * 将 ProductDO 转换为 ProductQuestionVO
     *
     * @param productDO ProductDO 对象
     * @return ProductQuestionVO 对象
     */
    private ProductQuestionVO convertToProductQuestionVOWithAllFields(ProductDO productDO) {
        ProductQuestionVO vo = new ProductQuestionVO();

        vo.setProductId(productDO.getId());
        vo.setName(productDO.getName());
        vo.setPrice(productDO.getPrice().toString());
        vo.setLearnCount(productDO.getLearnCount());
        vo.setCoverImage(productDO.getCoverImage());
        vo.setListPageBackgroundImage(productDO.getListPageBackgroundImage());
        vo.setCoverCornerBadge(productDO.getPremium() ? "精品" : productDO.getPopular() ? "热门" : null);
        vo.setProductType(productDO.getProductType());
        vo.setSecondCategory(productDO.getSecondCategory());
        vo.setFreeTrial(productDO.getFreeTrial());
        vo.setDescription(productDO.getDescription());
        vo.setTags(JSON.parseArray(productDO.getTags(), String.class));

        // 添加详情页所需的额外字段
        vo.setCertificateCode(productDO.getCertificateCode());
        vo.setMajorCode(productDO.getMajorCode());
        vo.setSubjectCode(productDO.getSubjectCode());
        vo.setDescription(productDO.getDescription());
        vo.setValidityPeriod(productDO.getValidityPeriod());
        vo.setOriginalPrice(PriceConverter.convertPriceToString(productDO.getOriginalPrice()));
        vo.setDetailImages(JSON.parseArray(productDO.getDetailImages(), String.class));
        vo.setPurchaseInstructions(productDO.getPurchaseInstructions());
        vo.setViewCount(productDO.getViewCount());

        return vo;
    }
}