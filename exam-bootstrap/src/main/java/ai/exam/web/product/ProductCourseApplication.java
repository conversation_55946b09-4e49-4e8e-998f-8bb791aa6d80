package ai.exam.web.product;

import ai.exam.domain.product.ProductDO;
import ai.exam.domain.product.ProductDomain;
import ai.exam.domain.product.ProductDynamicPropertiesRecordDomain;
import ai.exam.domain.product.ProductTypeEnum;
import ai.exam.domain.utils.PriceConverter;
import ai.exam.domain.utils.TimeConverter;
import ai.exam.web.product.vo.ProductCourseVO;
import ai.exam.web.product.vo.ProductDetailRequest;
import ai.exam.web.product.vo.ProductHomeSelectedCourseListRequest;
import ai.exam.web.product.vo.ProductListRequest;
import ai.exam.web.user.UserPermissionsApplication;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class ProductCourseApplication {

    @Resource
    private ProductDomain productDomain;

    @Resource
    private UserPermissionsApplication userPermissionsApplication;

    @Resource
    private ProductDynamicPropertiesRecordDomain productDynamicPropertiesRecordDomain;

    /**
     * 获取课程列表
     *
     * @param request 课程列表请求参数
     * @return 课程列表
     */
    public List<ProductCourseVO> getCourseList(ProductListRequest request) {
        // 调用领域层方法获取课程列表
        List<ProductDO> productDOList = productDomain.getProductList(
                request.getCertificateCode(),
                request.getMajorCode(),
                request.getSortField(),
                ProductTypeEnum.COURSE_PACKAGE,
                true
        );

        // 将 ProductDO 转换为 ProductVO
        return productDOList.stream()
                .map(this::convertToProductCourseVOForSimpleFields)
                .collect(Collectors.toList());
    }

    /**
     * 获取课程详情
     *
     * @param request 课程详情请求参数
     * @return 课程详情
     */
    public ProductCourseVO getCourseDetail(Long accountId, ProductDetailRequest request) {
        // 调用领域层方法获取课程详情
        ProductDO productDO = productDomain.getProductDetail(request.getProductId());

        // 将 ProductDO 转换为 ProductCourseVO
        ProductCourseVO productCourseVO = convertToProductCourseVO(productDO);

        // 检查权限
        accessCheck(accountId, productCourseVO);

        // 异步记录浏览人数
        productDynamicPropertiesRecordDomain.asyncRecordViewCount(request.getProductId());

        return productCourseVO;
    }

    /**
     * 获取相关联的课程商品
     *
     * @param productId 商品ID
     * @return 相关联的课程商品列表
     */
    public List<ProductCourseVO> getRelatedCourses(Long productId) {
        List<ProductDO> relatedProducts = productDomain.getRelatedCourses(productId, ProductTypeEnum.COURSE_PACKAGE);
        return relatedProducts.stream()
                .map(this::convertToProductCourseVOForSimpleFields)
                .toList();
    }

    /**
     * 获取首页精选课程列表
     *
     * @param request 首页精选课程列表请求参数
     * @return 精选课程列表
     */
    public List<ProductCourseVO> getSelectedCourseList(ProductHomeSelectedCourseListRequest request) {
        // 调用领域层方法获取精选课程列表
        List<ProductDO> productDOList = productDomain.getSelectedProductList(
                request.getCertificateCode(),
                request.isPremium(),
                request.isPopular(),
                ProductTypeEnum.COURSE_PACKAGE
        );

        // 将 ProductDO 转换为 ProductCourseVO
        return productDOList.stream()
                .map(this::convertToProductCourseVOForSimpleFields)
                .toList();
    }

    private void accessCheck(Long accountId, ProductCourseVO productCourseVO) {
        if (accountId == null) {
            return;
        }

        boolean access = userPermissionsApplication.queryPermissions(accountId, ProductTypeEnum.COURSE_PACKAGE.getCode(), productCourseVO.getProductId());
        productCourseVO.setAccess(access);
    }

    /**
     * 将 ProductDO 转换为 ProductCourseVO
     *
     * @param productDO ProductDO 对象
     * @return ProductCourseVO 对象
     */
    private ProductCourseVO convertToProductCourseVO(ProductDO productDO) {
        ProductCourseVO vo = convertToProductCourseVOForSimpleFields(productDO);

        // 添加详情页所需的额外字段
        vo.setCertificateCode(productDO.getCertificateCode());
        vo.setMajorCode(productDO.getMajorCode());
        vo.setSubjectCode(productDO.getSubjectCode());
        vo.setDescription(productDO.getDescription());
        vo.setTags(productDO.getTags());
        vo.setDifficulty(productDO.getDifficulty());
        vo.setLearningFormat(productDO.getLearningFormat());
        vo.setValidityPeriod(productDO.getValidityPeriod());
        vo.setOriginalPrice(PriceConverter.convertPriceToString(productDO.getOriginalPrice()));
        vo.setDetailImages(JSON.parseArray(productDO.getDetailImages(), String.class));
        vo.setPurchaseInstructions(productDO.getPurchaseInstructions());

        return vo;
    }

    /**
     * 将 ProductDO 转换为 ProductVO
     *
     * @param productDO ProductDO 对象
     * @return ProductVO 对象
     */
    private ProductCourseVO convertToProductCourseVOForSimpleFields(ProductDO productDO) {
        ProductCourseVO vo = new ProductCourseVO();

        vo.setProductId(productDO.getId());
        vo.setName(productDO.getName());
        vo.setVideoDuration(TimeConverter.convertSecondsToHoursAndMinutes(productDO.getVideoDuration()));
        vo.setPrice(PriceConverter.convertPriceToString(productDO.getPrice()));
        vo.setLearnCount(productDO.getLearnCount());
        vo.setCoverImage(productDO.getCoverImage());
        vo.setCoverCornerBadge(productDO.getPremium() ? "精品" : productDO.getPopular() ? "热门" : null);
        vo.setProductType(productDO.getProductType());
        vo.setSecondCategory(productDO.getSecondCategory());
        vo.setViewCount(productDO.getViewCount());
        vo.setFreeTrial(productDO.getFreeTrial());

        return vo;
    }

}