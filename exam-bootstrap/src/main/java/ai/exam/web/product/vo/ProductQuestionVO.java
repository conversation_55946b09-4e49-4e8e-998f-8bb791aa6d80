package ai.exam.web.product.vo;

import lombok.Data;

import java.util.List;

@Data
public class ProductQuestionVO {

    /**
     * 商品 ID
     */
    private Long productId;
    /**
     * 课程名称
     */
    private String name;
    /**
     * 价格
     */
    private String price;
    /**
     * 学习人数
     */
    private Long learnCount;
    /**
     * 封面图片
     */
    private String coverImage;
    /**
     * 列表页背景图片
     */
    private String listPageBackgroundImage;
    /**
     * 封面角标，用于标识标签，如“精品”、“热门”等
     */
    private String coverCornerBadge;
    /**
     * 商品分类，区分课程商品、题库商品
     */
    private String productType;
    /**
     * 二级分类，在课程商品上，表示专业全科、公共全科、专业单科
     */
    private String secondCategory;
    /**
     * 是否允许免费试用
     */
    private boolean freeTrial;

    /**
     * 题库类型列表
     * eg：AI智练、真题演练、模拟考试、章节题库
     */
    private List<String> questionSetTypeList;

    /**
     * 共 n 科
     */
    private Integer subjectsCount;

    // 以上字段为题库列表页所需
    // =======================================================================
    // 以下字段为题库详情页所需、购买页所需

    private String certificateCode;
    private String majorCode;
    private String majorName;
    private String subjectCode;
    private String subjectName;

    /**
     * 描述
     */
    private String description;
    /**
     * 人工标签
     */
    private List<String> tags;
    /**
     * 有效期
     */
    private Integer validityPeriod;
    /**
     * 原价
     */
    private String originalPrice;
    /**
     * 详情图片
     */
    private List<String> detailImages;
    /**
     * 购买说明
     */
    private String purchaseInstructions;
    /**
     * 浏览人数
     */
    private Long viewCount;

}
