package ai.exam.web.product;

import ai.exam.web.product.vo.*;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/web/product")
public class ProductWebController {

    @Resource
    private ProductCourseApplication productCourseApplication;

    @Resource
    private ProductQuestionApplication productQuestionApplication;

    @PostMapping("/course_list")
    public List<ProductCourseVO> courseList(@RequestBody ProductListRequest request) {
        return productCourseApplication.getCourseList(request);
    }

    @PostMapping("/course_detail")
    public ProductCourseVO courseDetail(Long accountId, @RequestBody ProductDetailRequest request) {
        return productCourseApplication.getCourseDetail(accountId, request);
    }

    /**
     * 获取相关联的课程商品
     *
     * @param request 课程详情请求参数
     * @return 相关联的课程商品列表
     */
    @PostMapping("/related_courses")
    public List<ProductCourseVO> getRelatedCourses(@RequestBody ProductDetailRequest request) {
        return productCourseApplication.getRelatedCourses(request.getProductId());
    }

    /**
     * 获取首页精选课程列表
     *
     * @param request 首页精选课程列表请求参数
     * @return 精选课程列表
     */
    @PostMapping("/home/<USER>")
    public List<ProductCourseVO> homeSelectedCourseList(@RequestBody ProductHomeSelectedCourseListRequest request) {
        return productCourseApplication.getSelectedCourseList(request);
    }

    @PostMapping("/question_list")
    public List<ProductQuestionVO> questionList(@RequestBody ProductListRequest request) {
        return productQuestionApplication.getQuestionList(request);
    }

    /**
     * 获取题库商品详情
     *
     * @param request 商品详情请求参数
     * @return 题库商品详情
     */
    @PostMapping("/question_detail")
    public ProductQuestionVO questionDetail(@RequestBody ProductDetailRequest request) {
        return productQuestionApplication.getQuestionDetail(request);
    }

    /**
     * 批量获取题库商品详情
     *
     * @param request 包含商品ID列表的请求对象
     * @return 题库商品详情列表
     */
    @PostMapping("/batch_question_details")
    public List<ProductQuestionVO> batchQuestionDetails(@RequestBody BatchProductQuestionDetailsRequest request) {
        return productQuestionApplication.batchGetQuestionDetails(request.getProductIds());
    }

    @PostMapping("/related_questions")
    public ProductQuestionRelatedQuestionsVO getRelatedQuestions(@RequestBody ProductDetailRequest request) {
        return productQuestionApplication.getRelatedQuestions(request.getProductId());
    }

}
