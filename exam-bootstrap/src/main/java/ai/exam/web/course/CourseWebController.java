package ai.exam.web.course;

import ai.exam.web.course.vo.*;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/web/course")
public class CourseWebController {

    @Resource
    private CourseApplication courseApplication;

    /**
     * 无登录态 menu
     */
    @PostMapping("/menu")
    public List<CourseMenuVO> menu(@RequestBody CourseMenuRequest request) {
        return courseApplication.getCourseMenu(null, request.getProductId());
    }

    /**
     * 有登录态 menu
     */
    @PostMapping("/menu_with_token")
    public List<CourseMenuVO> menuWithToken(Long accountId, @RequestBody CourseMenuRequest request) {
        return courseApplication.getCourseMenu(accountId, request.getProductId());
    }

    /**
     * 根据商品ID查询教师信息
     *
     * @param request 包含商品ID的请求对象
     * @return 教师信息列表
     */
    @PostMapping("/get_teachers_by_product_id")
    public List<TeacherVO> getTeachersByProductId(@RequestBody CourseMenuRequest request) {
        return courseApplication.getTeachersByProductId(request.getProductId());
    }

    /**
     * 根据课程ID查询课程详情
     *
     * @param request 包含课程ID的请求对象
     * @return 课程详情
     */
    @PostMapping("/get_course_detail")
    public CourseVO getCourseDetail(Long accountId, @RequestBody CourseDetailRequest request) {
        return courseApplication.getCourseDetail(accountId, request.getProductId(), request.getCourseId());
    }

}