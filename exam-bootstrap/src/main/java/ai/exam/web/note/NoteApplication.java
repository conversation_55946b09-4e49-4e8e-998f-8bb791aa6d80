package ai.exam.web.note;

import ai.exam.common.enums.ExamModeEnum;
import ai.exam.domain.certificate.CertificateDomain;
import ai.exam.domain.certificate.SubjectDO;
import ai.exam.domain.course.CourseDomain;
import ai.exam.domain.course.NoteDO;
import ai.exam.domain.course.NoteDomain;
import ai.exam.domain.product.ProductDomain;
import ai.exam.mapper.NoteDOVOMapper;
import ai.exam.web.note.vo.NoteVO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class NoteApplication {

    @Resource
    private CertificateDomain certificateDomain;

    @Resource
    private NoteDomain noteDomain;

    @Resource
    private CourseDomain courseDomain;

    @Resource
    private ProductDomain productDomain;

    @Resource
    private NoteDOVOMapper noteDOVOMapper;

    /**
     * 添加笔记
     *
     * @param userId 用户ID
     * @return 成功返回true，失败返回false
     */
    public boolean takeNotes(Long userId, NoteVO noteVO) {
        noteVO.setUserId(userId);
        noteVO.setSummary(generateSummary(noteVO.getNote()));
        return noteDomain.takeNotes(noteDOVOMapper.toNoteDO(noteVO));
    }

    /**
     * 获取笔记详情
     *
     * @param noteId 笔记ID
     * @return 笔记详情VO
     */
    public NoteVO getNoteDetail(Long noteId) {
        NoteDO noteDO = noteDomain.getNoteById(noteId);
        if (noteDO == null) {
            return null;
        }

        NoteVO noteVO = noteDOVOMapper.toNoteVO(noteDO);

        // 课程笔记
        if (noteDO.getNoteSource().equals("course")) {
            // 获取产品名称
            String productName = productDomain.getProductNameById(noteDO.getProductId());
            noteVO.setProductName(productName);

            // 获取课程名称
            String courseName = courseDomain.getCourseNameById(noteDO.getCourseId());
            noteVO.setCourseName(courseName);
        }

        // 题库笔记
        if (noteDO.getNoteSource().equals("question")) {
            SubjectDO subjectDO = certificateDomain.findSubjectsByCodes(List.of(noteDO.getSubjectCode())).get(0);
            noteVO.setSubjectName(subjectDO.getSubjectName());
        }

        return noteVO;
    }

    /**
     * 获取笔记列表
     *
     * @param userId     用户ID
     * @param noteSource 笔记来源
     * @param productId  商品ID
     * @return 笔记列表VO
     */
    public List<NoteVO> getNoteList(Long userId, String noteSource, Long productId) {
        List<NoteDO> noteDOList;
        if (productId != null) {
            // 如果 productId 不为空，则根据 userId、noteSource 和 productId 查询笔记列表
            noteDOList = noteDomain.getNoteListByUserIdAndNoteSourceAndProductId(userId, noteSource, productId);
        } else {
            // 如果 productId 为空，则根据 userId 和 noteSource 查询笔记列表
            noteDOList = noteDomain.getNoteListByUserIdAndNoteSource(userId, noteSource);
        }

        List<NoteVO> noteVOList = convertToVO(noteDOList);

        fillCourseInfo(noteSource, noteVOList);

        fillQuestionInfo(noteSource, noteVOList);

        return noteVOList;
    }

    /**
     * 删除笔记
     *
     * @param userId 用户ID
     * @param noteId 笔记ID
     * @return 删除成功返回true，失败返回false
     */
    public boolean deleteNoteById(Long userId, Long noteId) {
        return noteDomain.deleteNoteByUserIdAndNoteId(userId, noteId);
    }

    private void fillQuestionInfo(String noteSource, List<NoteVO> noteVOList) {
        if (noteSource.equals("question")) {
            // 批量获取科目名称
            List<String> subjectCodes = noteVOList.stream()
                    .map(NoteVO::getSubjectCode)
                    .distinct()
                    .collect(Collectors.toList());
            List<SubjectDO> subjects = certificateDomain.findSubjectsByCodes(subjectCodes);
            Map<String, String> subjectNames = subjects.stream()
                    .collect(Collectors.toMap(SubjectDO::getSubjectCode, SubjectDO::getSubjectName));

            // 填充额外信息
            for (NoteVO noteVO : noteVOList) {
                noteVO.setSubjectName(subjectNames.get(noteVO.getSubjectCode()));
                if (StringUtils.isNotBlank(noteVO.getQuestionSetType())) {
                    noteVO.setQuestionSetTypeDescription(ExamModeEnum.getByCode(noteVO.getQuestionSetType()).getDescription());
                }
            }
        }
    }

    private void fillCourseInfo(String noteSource, List<NoteVO> noteVOList) {
        if (noteSource.equals("course")) {
            // 批量获取产品名称
            List<Long> productIds = noteVOList.stream()
                    .map(NoteVO::getProductId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, String> productNames = productDomain.getProductNamesByIds(productIds);

            // 批量获取课程名称
            List<Long> courseIds = noteVOList.stream()
                    .map(NoteVO::getCourseId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, String> courseNames = courseDomain.getCourseNamesByIds(courseIds);

            // 填充额外信息
            for (NoteVO noteVO : noteVOList) {
                noteVO.setProductName(productNames.get(noteVO.getProductId()));
                noteVO.setCourseName(courseNames.get(noteVO.getCourseId()));
            }
        }
    }

    private List<NoteVO> convertToVO(List<NoteDO> noteDOList) {
        return noteDOList.stream()
                .map(noteDO -> {
                    NoteVO vo = noteDOVOMapper.toNoteVO(noteDO);
                    vo.setUpdateTimeStr(vo.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 生成笔记摘要
     *
     * @param note 笔记内容
     * @return 摘要
     */
    private String generateSummary(String note) {
        if (note == null || note.length() <= 10) {
            return note;
        }
        return note.substring(0, 10) + "...";
    }
}