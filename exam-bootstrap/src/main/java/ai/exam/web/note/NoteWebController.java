package ai.exam.web.note;

import ai.exam.web.note.vo.DeleteNoteRequest;
import ai.exam.web.note.vo.NoteDetailRequest;
import ai.exam.web.note.vo.NoteListRequest;
import ai.exam.web.note.vo.NoteVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/web/note")
public class NoteWebController {

    @Resource
    private NoteApplication noteApplication;

    /**
     * 记笔记
     *
     * @param noteVO 笔记请求对象
     * @return 成功返回true，失败返回false
     */
    @PostMapping("/take_notes")
    public boolean takeNotes(Long accountId, @RequestBody NoteVO noteVO) {
        return noteApplication.takeNotes(accountId, noteVO);
    }

    /**
     * 根据笔记ID和用户ID查询笔记内容
     *
     * @param noteRequest 笔记请求对象
     * @return 笔记详情
     */
    @PostMapping("/get_note_detail")
    public NoteVO getNoteDetail(@RequestBody NoteDetailRequest noteRequest) {
        return noteApplication.getNoteDetail(noteRequest.getNoteId());
    }

    /**
     * 根据noteSource、productId和userId查询笔记列表
     *
     * @param noteListRequest 包含noteSource和productId的请求对象
     * @return 笔记列表
     */
    @PostMapping("/get_note_list")
    public List<NoteVO> getNoteList(Long accountId, @RequestBody NoteListRequest noteListRequest) {
        return noteApplication.getNoteList(accountId, noteListRequest.getNoteSource(), noteListRequest.getProductId());
    }

    /**
     * 根据笔记ID删除笔记
     *
     * @param deleteNoteRequest 删除笔记的请求对象
     * @return 成功返回true，失败返回false
     */
    @PostMapping("/delete_note_by_id")
    public boolean deleteNoteById(Long accountId, @RequestBody DeleteNoteRequest deleteNoteRequest) {
        return noteApplication.deleteNoteById(accountId, deleteNoteRequest.getNoteId());
    }

}