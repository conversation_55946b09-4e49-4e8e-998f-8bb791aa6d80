package ai.exam.web.certificate;

import ai.exam.web.certificate.vo.CertificateWithMajorsVO;
import ai.exam.web.certificate.vo.ChapterInfoVO;
import ai.exam.web.certificate.vo.SubjectWebVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/web/certificate")
public class CertificateWebController {
    @Resource
    private CertificateApplication certificateApplication;

    @GetMapping("/list")
    public List<CertificateWithMajorsVO> list() {
        return certificateApplication.getCertificatesWithMajors();
    }

    @GetMapping("/fetchSubjectsByCertificateCode")
    public List<SubjectWebVO> fetchSubjectsByCertificateCode(@RequestParam String certificateCode) {
        return certificateApplication.getSubjectsByCertificateCode(certificateCode);
    }

    @GetMapping("/getChapterBySubjectCode")
    public List<ChapterInfoVO> getChapterBySubjectCode(@RequestParam String subjectCode) {
        return certificateApplication.getChaptersBySubject(subjectCode);
    }

}
