package ai.exam.web.account;

import ai.exam.web.account.vo.AccountVO;
import ai.exam.web.account.vo.SendVerificationCodeVO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

@Validated
@RestController
@RequestMapping("/api/web/account")
public class AccountWebController {

    @Resource
    private AccountWebApplication accountWebApplication;

    @GetMapping("/sendVerificationCode")
    public SendVerificationCodeVO sendVerificationCode(@RequestParam String phone) {
        return accountWebApplication.sendVerificationCode(phone);
    }

    @GetMapping("/login")
    public AccountVO login(@RequestParam @NotBlank String phone, @RequestParam @NotBlank String verificationCode) {
        return accountWebApplication.login(phone, verificationCode);
    }

    @GetMapping("/getAccount")
    public AccountVO getAccount(Long accountId) {
        return accountWebApplication.getAccount(accountId);
    }

    /**
     * 更新账户信息
     *
     * @param accountId 账户ID
     * @param accountVO 更新请求
     * @return 更新后的账户信息
     */
    @PostMapping("/updateAccount")
    public AccountVO updateAccount(Long accountId, @RequestBody AccountVO accountVO) {
        return accountWebApplication.updateAccount(accountId, accountVO);
    }
}