package ai.exam.web.account;

import ai.exam.auth.JwtUtil;
import ai.exam.common.exception.VerificationCodeErrorException;
import ai.exam.domain.account.AccountDO;
import ai.exam.domain.account.AccountDomain;
import ai.exam.domain.account.PhoneVerificationCodeDomain;
import ai.exam.mapper.AccountVODOWebMapper;
import ai.exam.web.account.vo.AccountVO;
import ai.exam.web.account.vo.SendVerificationCodeVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class AccountWebApplication {

    @Resource
    private AccountDomain accountDomain;

    @Resource
    private AccountVODOWebMapper accountVODOWebMapper;

    @Resource
    private PhoneVerificationCodeDomain phoneVerificationCodeDomain;

    public SendVerificationCodeVO sendVerificationCode(String phone) {
        // 生成验证码
        String verificationCode = SMSService.generateRandomVerificationCode();

        // 入库
        phoneVerificationCodeDomain.saveVerificationCode(phone, verificationCode);

        // 发验证码
        SMSService.sendVerificationCode(phone, verificationCode);

        return SendVerificationCodeVO.success();
    }

    public AccountVO login(String phone, String smsCode) {
        boolean access = phoneVerificationCodeDomain.checkVerificationCode(phone, smsCode);
        if (!access) {
            throw new VerificationCodeErrorException();
        }

        AccountDO accountDO = accountDomain.getAccountByPhoneOrCreateOne(phone);

        AccountVO accountVO = accountVODOWebMapper.toAccountVO(accountDO);
        accountVO.setToken(JwtUtil.generateToken(accountDO.getId()));
        return accountVO;
    }

    public AccountVO getAccount(Long accountId) {
        AccountDO accountDO = accountDomain.getById(accountId).get();
        AccountVO accountVO = accountVODOWebMapper.toAccountVO(accountDO);
        accountVO.setToken(JwtUtil.generateToken(accountDO.getId()));
        return accountVO;
    }

    /**
     * 更新账户信息
     *
     * @param accountId 账户ID
     * @return 更新后的账户信息
     */
    public AccountVO updateAccount(Long accountId, AccountVO accountVO) {
        AccountDO updatedAccount = accountDomain.updateAccount(
                accountId,
                accountVO.getNickname(),
                accountVO.getAvatarUrl(),
                accountVO.getGender()
        );

        AccountVO updatedAccountVO = accountVODOWebMapper.toAccountVO(updatedAccount);
        updatedAccountVO.setToken(JwtUtil.generateToken(updatedAccount.getId()));
        return updatedAccountVO;
    }
}