package ai.exam.web.comment;

import ai.exam.web.comment.vo.CommentVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/web/comment")
public class CommentWebController {

    @GetMapping("/home/<USER>")
    public List<CommentVO> homeList() {
        return List.of(
                new CommentVO("南京小茹",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%2011%404x-21-.png",
                        """
                                最近学习的感觉真是太棒了，从来没这么轻松过。以前那些条文和规范可复杂了，学起来特别费劲，每次备考都压力很大。
                                不过现在好了，它们都变成了简明的要点，一下子就好理解多了。我能很快抓住关键信息，学起来效率高了不少，备考的时候也没那么大压力了。这种感觉真的很不错。
                                """),
                new CommentVO("深圳杰克",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%2010%404x-21-.png",
                        """
                                我真的很感谢这个系统，它能很好地配合我的日程。在工作日里，时间都是零零碎碎的，不过我可以用这个系统有效地利用这些时间。就像在通勤的路上，或者午休的一小会儿，我都能学点儿东西。随着时间的积累，我每天都能有新的进步。
                                """),
                new CommentVO("天津Tony",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%207%404x-21-.png",
                        """
                                每次练习后，立刻就能得到反馈，我可以即时了解自己的强项和弱点。这种及时的指导让我每天都充满了前进的动力。
                                """),
                new CommentVO("沈阳小蓉",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%209%404x-21-.png",
                        """
                                有了AI助教，我感觉自己像是有了一个随时在身边的导师。无论何时遇到难题，我都能快速获得帮助，学习变得更加自信和高效。
                                """),
                new CommentVO("上海艾米",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%206%404x-21-.png",
                        """
                                刚登录这个系统的时候，有几个测评问题。这些问题简洁又直接，一下子就帮我找准了学习的起点，还让我知道哪些内容得重点加强。这样一来，我备考安排就能规划得更有效啦。
                                """),
                new CommentVO("广州夏洛",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%205%404x-21-.png",
                        """
                                这个系统的智能推题功能真的太赞了，就像是为我量身定制的。它会仔细考量我的学习历程，清楚地了解我学过什么、哪里花的时间多、哪里还比较生疏。而且，它还能依据我对知识的掌握情况来调整题目难度。要是我某个知识点掌握得不好，它就会出些简单点的题目，让我先把基础夯实。等我掌握得差不多了，它就会适当增加难度，给我新挑战，让我持续进步。通过这样的调整，它能保证我一直处在最佳学习区间，学习起来轻松又高效。
                                """),
                new CommentVO("天津小安",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%204%404x-21-.png",
                        """
                                我太喜欢这个平台的互动学习模式了。上课的时候有清楚的视频讲解，把建筑知识讲得活灵活现。同时还能边上课边做题，这些题跟刚学的内容联系紧密，做完就知道自己的学习情况。过程中还有讲义能看，重点知识都梳理得明明白白。
                                """),
                new CommentVO("杭州薇拉",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%202%404x-21-.png",
                        """
                                这个平台的个性化题库太让我惊喜啦！每次做练习，它都能照着我的弱点来调整，经过这样练习，每个考点我都能掌握得扎扎实实。
                                """),
                new CommentVO("北京小杰",
                        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/%E8%B5%84%E6%BA%90%201%404x-21-.png",
                        """
                                这个学情仪表盘真的很实用。每次打开它，我都能轻松查看自己的学习进度和成效。它就像一个认真负责的记录员，把我的学习情况完整地呈现出来。它会把我已经掌握的知识点罗列出来。同时，它也清晰地为我指出未来的学习方向，就像一盏明灯，在学习的路上为我指引。有了它，我学习更有目标了，效率有所提升，学习的动力也更强了。
                                """)
        );
    }
}
