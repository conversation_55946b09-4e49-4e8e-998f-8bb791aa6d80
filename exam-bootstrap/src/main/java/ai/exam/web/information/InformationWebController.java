package ai.exam.web.information;

import ai.exam.web.information.vo.InformationVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/web/information")
public class InformationWebController {

    @GetMapping("/home/<USER>")
    public List<InformationVO> homeList() {
        return List.of(
                new InformationVO("这里是标题这里是标题","这里是相关介绍文字这里是相关这里是相关介绍文字这里","https://cdn.builder.io/api/v1/image/assets/TEMP/31445e46e266fd4228ed44da240697c817043d6939609353ff960906564e23c9?placeholderIfAbsent=true&apiKey=1768a45516594f91af08d1bb32140cef", "https://www.pdq365.com/news/nid_62/3873.html"),
                new InformationVO("这里是标题这里是标题","这里是相关介绍文字这里是相关这里是相关介绍文字这里","https://cdn.builder.io/api/v1/image/assets/TEMP/31445e46e266fd4228ed44da240697c817043d6939609353ff960906564e23c9?placeholderIfAbsent=true&apiKey=1768a45516594f91af08d1bb32140cef", "https://www.pdq365.com/news/nid_62/3873.html"),
                new InformationVO("这里是标题这里是标题","这里是相关介绍文字这里是相关这里是相关介绍文字这里","https://cdn.builder.io/api/v1/image/assets/TEMP/31445e46e266fd4228ed44da240697c817043d6939609353ff960906564e23c9?placeholderIfAbsent=true&apiKey=1768a45516594f91af08d1bb32140cef", "https://www.pdq365.com/news/nid_62/3873.html"),
                new InformationVO("这里是标题这里是标题","这里是相关介绍文字这里是相关这里是相关介绍文字这里","https://cdn.builder.io/api/v1/image/assets/TEMP/31445e46e266fd4228ed44da240697c817043d6939609353ff960906564e23c9?placeholderIfAbsent=true&apiKey=1768a45516594f91af08d1bb32140cef", "https://www.pdq365.com/news/nid_62/3873.html"));
    }
}
