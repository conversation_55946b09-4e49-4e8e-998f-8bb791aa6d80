package ai.exam.web.question.vo;

import ai.exam.enums.QuestionBankTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuestionBankTypeVO {

    private String questBankTypeName;
    private String questionBankType;

    public static QuestionBankTypeVO from(QuestionBankTypeEnum questionBankTypeEnum) {
        return new QuestionBankTypeVO(questionBankTypeEnum.getName(), questionBankTypeEnum.getValue());
    }

}
