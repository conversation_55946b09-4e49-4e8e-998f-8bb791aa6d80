package ai.exam.web.question.mapper;

import ai.exam.domain.question.QuestionDO;
import ai.exam.web.question.vo.QuestionInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface QuestionMapper {

    @Mapping(target = "questionId", source = "id")
    @Mapping(target = "questionContent", source = "content")
    @Mapping(target = "questionType", source = "type")
    @Mapping(target = "rightAnswer", source = "answer")
    QuestionInfoVO toVO(QuestionDO questionDO);

}