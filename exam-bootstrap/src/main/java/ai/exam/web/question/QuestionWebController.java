package ai.exam.web.question;

import ai.exam.enums.QuestionBankTypeEnum;
import ai.exam.web.question.vo.QuestionBankTypeVO;
import ai.exam.web.question.vo.QuestionInfoVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/web/question")
public class QuestionWebController {

    @Resource
    private QuestionWebApplication questionWebApplication;

    @GetMapping("/questionBankType/list")
    public List<QuestionBankTypeVO> questionBankTypeList() {
        return Arrays.stream(QuestionBankTypeEnum.values()).map(QuestionBankTypeVO::from).collect(Collectors.toList());
    }

    @GetMapping("/getQuestionInfo")
    public QuestionInfoVO getQuestionInfo(@RequestParam Long questionId) {
        return questionWebApplication.getQuestionInfo(questionId);
    }

}
