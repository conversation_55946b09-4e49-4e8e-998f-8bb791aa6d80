package ai.exam.autots;

import com.blueveery.springrest2ts.Rest2tsGenerator;
import com.blueveery.springrest2ts.converters.*;
import com.blueveery.springrest2ts.filters.ContainsSubStringJavaTypeFilter;
import com.blueveery.springrest2ts.implgens.ImplementationGenerator;
import com.blueveery.springrest2ts.tsmodel.TSArray;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import jakarta.annotation.PostConstruct;
import org.springframework.util.FileSystemUtils;

import java.io.IOException;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

//@Configuration
public class TsCodeGenerations {

    protected static final Path OUTPUT_DIR_PATH = Paths.get("ai-exam-fe/src/http");
    protected static final String IMPORT_STATEMENT = "import axiosInstance from '../lib/axiosInstance';\n";


    protected Rest2tsGenerator tsGenerator;
    protected Set<String> javaPackageSet;
    protected ModelClassesAbstractConverter modelClassesConverter;
    protected SpringRestToTsConverter restClassesConverter;


    @PostConstruct
    public void run() throws Exception {
        setUp();
        customTypeMapping();
    }

    public void setUp() {
        FileSystemUtils.deleteRecursively(OUTPUT_DIR_PATH.resolve("OUTPUT_DIR_PATH").toFile());

        tsGenerator = new Rest2tsGenerator();

        TsModuleCreatorConverter moduleConverter = new TsModuleCreatorConverter(2);
        tsGenerator.setJavaPackageToTsModuleConverter(moduleConverter);


        //set java type filters
//        tsGenerator.setModelClassesCondition(new RejectJavaTypeFilter());
        tsGenerator.setModelClassesCondition(new ContainsSubStringJavaTypeFilter("VO"));
        tsGenerator.setRestClassesCondition(new ContainsSubStringJavaTypeFilter("Controller"));

        //set model class converter
        JacksonObjectMapper jacksonObjectMapper = new JacksonObjectMapper();
        jacksonObjectMapper.setFieldsVisibility(JsonAutoDetect.Visibility.ANY);
        modelClassesConverter = new ModelClassesToTsInterfacesConverter(jacksonObjectMapper);
        tsGenerator.setModelClassesConverter(modelClassesConverter);

        //set rest class converter
        ImplementationGenerator implementationGenerator = new AxiosImplementationGenerator();
//        ImplementationGenerator implementationGenerator = new Angular4ImplementationGenerator();
//        ImplementationGenerator implementationGenerator = new FetchBasedImplementationGenerator();
        restClassesConverter = new SpringRestToTsConverter(implementationGenerator);

        tsGenerator.setRestClassesConverter(restClassesConverter);

        //set java root packages from which class scanning will start
        javaPackageSet = Collections.singleton("ai.exam.pages");
    }

    public void customTypeMapping() throws IOException {
        tsGenerator.getCustomTypeMapping().put(UUID.class, TypeMapper.tsString);
        tsGenerator.getCustomTypeMapping().put(BigInteger.class, TypeMapper.tsNumber);
        tsGenerator.getCustomTypeMapping().put(LocalDateTime.class, TypeMapper.tsNumber);
        tsGenerator.getCustomTypeMapping().put(LocalDate.class, new TSArray(TypeMapper.tsNumber));
        tsGenerator.generate(javaPackageSet, OUTPUT_DIR_PATH);

        addImportStatementToFiles(OUTPUT_DIR_PATH);

    }


    private static void addImportStatementToFiles(Path dirPath) throws IOException {
        try (Stream<Path> paths = Files.walk(dirPath, 1)) {
            List<Path> filePaths = paths.filter(Files::isRegularFile).collect(Collectors.toList());
            for (Path filePath : filePaths) {
                List<String> lines = Files.readAllLines(filePath);
                if (!lines.isEmpty() && !lines.get(0).contains(IMPORT_STATEMENT.trim())) {
                    lines.add(0, IMPORT_STATEMENT);
                    Files.write(filePath, lines);
                }
            }
        }
    }
}
