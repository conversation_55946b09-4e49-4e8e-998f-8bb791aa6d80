package ai.exam.autots;

import com.blueveery.springrest2ts.converters.TypeMapper;
import com.blueveery.springrest2ts.implgens.BaseImplementationGenerator;
import com.blueveery.springrest2ts.tsmodel.*;
import com.blueveery.springrest2ts.tsmodel.generics.TSClassReference;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.BufferedWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.blueveery.springrest2ts.spring.RequestMappingUtility.getRequestMapping;


public class AxiosImplementationGenerator extends BaseImplementationGenerator {
    protected TSClass promiseClass;

    public AxiosImplementationGenerator() {
        promiseClass = new TSClass("Promise", null, this);
    }

    @Override
    protected String[] getImplementationSpecificFieldNames() {
        return new String[]{};
    }

    @Override
    public void changeMethodBeforeImplementationGeneration(TSMethod tsMethod) {
        if (isRestClass(tsMethod.getOwner()) && !tsMethod.isConstructor()) {
            tsMethod.setAsync(true);
        }
    }


    @Override
    public void write(BufferedWriter writer, TSMethod method) throws IOException {
        TSClass tsClass = (TSClass) method.getOwner();
        if (!method.isConstructor()) {
            RequestMapping methodRequestMapping = getRequestMapping(method.getAnnotationList());
            RequestMapping classRequestMapping = getRequestMapping(tsClass.getAnnotationList());

            String tsPath = getEndpointPath(methodRequestMapping, classRequestMapping);
//            tsPath = useUrlService ? "this." + FIELD_NAME_URL_SERVICE + ".getBackendUrl() + '" + tsPath : "'" + tsPath;

            String httpMethod = methodRequestMapping.method()[0].toString();

            String requestHeadersVar = "headers";
            String requestParamsVar = "params";

            StringBuilder pathStringBuilder = new StringBuilder(tsPath);
            StringBuilder requestBodyBuilder = new StringBuilder();
            StringBuilder requestParamsBuilder = new StringBuilder();

            assignMethodParameters(method, requestParamsVar, pathStringBuilder, requestBodyBuilder, requestParamsBuilder);

            boolean isRequestBodyDefined = !isStringBuilderEmpty(requestBodyBuilder);
            boolean isRequestParamDefined = !isStringBuilderEmpty(requestParamsBuilder);
            writer.write(requestParamsBuilder.toString());

            boolean isJsonParsingRequired = isJsonTransformationRequired(method.getType());
            String contentTypeHeader = getContentTypeHeaderFromRequestMapping(httpMethod, methodRequestMapping, isRequestBodyDefined);
            boolean isRequestHeaderDefined = !contentTypeHeader.isEmpty();
            writeRequestOption(writer, requestHeadersVar, contentTypeHeader, isRequestHeaderDefined);

            String requestOptions = "";
            String requestBody = requestBodyBuilder.toString();

            List<TSParameter> requestBodyParams = findRequestBodyParam(method);
            Optional<TSParameter> requestBodyParam = requestBodyParams.stream().findFirst();
            boolean isJsonSerializationRequired = requestBodyParam.isPresent() && isJsonTransformationRequired(requestBodyParam.get().getType());
            requestOptions = composeRequestBody(requestBody, isRequestBodyDefined, requestOptions, httpMethod, isJsonSerializationRequired, methodRequestMapping.consumes(), requestBodyParam);
            requestOptions = composeRequestOptions(requestHeadersVar, requestParamsVar, isRequestParamDefined, isRequestHeaderDefined, requestOptions, isJsonParsingRequired);

            tsPath = pathStringBuilder.toString();
            writeReturnStatement(writer, httpMethod.toLowerCase(), method, tsPath, requestOptions, isJsonParsingRequired);
        }
    }

    protected void writeReturnStatement(BufferedWriter writer, String httpMethod, TSMethod method, String tsPath, String requestOptions, boolean isJsonParsingRequired) throws IOException {

        writer.write("    return (await axiosInstance." + httpMethod + getGenericType(method, isJsonParsingRequired) + "('" + tsPath + requestOptions + ")).data" + getParseResponseFunction(isJsonParsingRequired, method) + ";");
    }

    protected String getParseResponseFunction(boolean isJsonResponse, TSMethod method) {
//        if (isJsonResponse) {
//            String parseFunction = modelSerializerExtension.generateDeserializationCode("res", method);
//            return ".pipe(map(res => " + parseFunction + "))";
//        }
        return "";
    }

    protected String getGenericType(TSMethod method, boolean isRequestOptionRequired) {
        return "";
//        return isRequestOptionRequired ? "" : "<" + method.getType().getName() + ">";
    }


    protected void initializeHttpParams(StringBuilder requestParamsBuilder, String requestParamsVar) {

        requestParamsBuilder.append("    const ").append(requestParamsVar).append(" : Record<string, string> = {}");
    }

    @Override
    protected void addRequestParameter(StringBuilder requestParamsBuilder, String params, String queryParamVar) {
//        params = params.append(queryParam.name, queryParam.value);
//
//        params[queryParam.name] = queryParam.value;

//        requestParamsBuilder.append("\n      ").append(params).append(" = ").append(params).append(".append(").append(queryParamVar).append(".name").append(", ").append(queryParamVar).append(".value").append(");");
        requestParamsBuilder.append("\n      ").append(params).append("[").append(queryParamVar).append(".name").append("]").append(" = ").append(queryParamVar).append(".value").append("");
    }

    protected boolean isJsonTransformationRequired(TSType type) {
        return type != TypeMapper.tsNumber && type != TypeMapper.tsBoolean && type != TypeMapper.tsString && type != TypeMapper.tsVoid;
    }

    protected void writeRequestOption(BufferedWriter writer, String requestOption, String requestOptionValue, boolean isOptionDefined) throws IOException {
        if (isOptionDefined) {
            writer.write("    const " + requestOption + " = " + requestOptionValue);
            writer.newLine();
        }
    }

    protected String composeRequestBody(String requestBody, boolean isRequestBodyDefined, String requestOptions, String httpMethod, boolean isJsonParsingRequired, String[] consumes, Optional<TSParameter> requestBodyParam) {
        if (bodyIsAllowedInRequest(httpMethod)) {
            if (isRequestBodyDefined) {
                requestOptions += ", " + requestBody;
//                requestOptions = appendRequestBodyPart(requestBody, requestOptions, isJsonParsingRequired, consumes, requestBodyParam.get());
            } else {
                requestOptions += ", null ";
            }
        }
        return requestOptions;
    }

    protected String appendRequestBodyPart(String requestBody, String requestOptions, boolean isJsonParsingRequired, String[] consumes, TSParameter requestBodyParam) {
        if (isJsonParsingRequired) {
            requestOptions += ", " + modelSerializerExtension.generateSerializationCode(requestBody, requestBodyParam) + " ";
        } else {
            requestOptions += ", " + requestBody + " ";
        }
        return requestOptions;
    }

    protected String composeRequestOptions(String requestHeadersVar, String requestParamsVar, boolean isRequestParamDefined, boolean isRequestHeaderDefined, String requestOptions, boolean isJsonParsingRequired) {
        if (isRequestHeaderDefined || isRequestParamDefined || isJsonParsingRequired) {
            List<String> requestOptionsList = new ArrayList<>();
//            if (isRequestHeaderDefined) {
//                requestOptionsList.add(requestHeadersVar);
//            }
            if (isRequestParamDefined) {
                requestOptionsList.add(requestParamsVar);
            }
//            if (isJsonParsingRequired) {
//                requestOptionsList.add("responseType: 'text'");
//            }
            requestOptions += ", {";
            requestOptions += String.join(", ", requestOptionsList);
            requestOptions += "}";
        }
        return requestOptions;
    }

    protected String getContentTypeHeaderFromRequestMapping(String httpMethod, RequestMapping requestMapping, boolean isRequestBodyDefined) {
//        if (bodyIsAllowedInRequest(httpMethod) && isRequestBodyDefined) {
//            String contentType = getContentType(requestMapping.consumes());
//            return "new HttpHeaders().set('Content-type'," + " '" + contentType + "');";
//        }
        return "";
    }

    @Override
    public TSType mapReturnType(TSMethod tsMethod, TSType tsType) {
        if (isRestClass(tsMethod.getOwner())) {
            return new TSClassReference(promiseClass, tsType);
        }
        return tsType;
    }


    @Override
    public List<TSParameter> getImplementationSpecificParameters(TSMethod method) {
        return Collections.emptyList();
    }

    @Override
    public List<TSDecorator> getDecorators(TSMethod tsMethod) {
        return Collections.emptyList();
    }

    @Override
    public List<TSDecorator> getDecorators(TSClass tsClass) {
        return Collections.emptyList();
    }


    @Override
    public void addComplexTypeUsage(TSClass tsClass) {
    }

    /**
     * 构造方法
     */
    @Override
    public void addImplementationSpecificFields(TSComplexElement tsComplexType) {

    }
}
