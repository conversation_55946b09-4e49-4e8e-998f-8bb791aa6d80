package ai.exam.autots;

import com.blueveery.springrest2ts.tsmodel.TSInterface;
import com.blueveery.springrest2ts.tsmodel.TSModule;

import java.io.BufferedWriter;
import java.io.IOException;

public class CustomTSInterface extends TSInterface {
    public CustomTSInterface(String name, TSModule module) {
        super(name, module);
    }

    @Override
    public void write(BufferedWriter writer) throws IOException {
        String name = tsComment.getName();
        System.out.println(name);
        writer.write(name);
        super.write(writer);
    }

}
