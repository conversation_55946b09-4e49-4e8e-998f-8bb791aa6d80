package ai.exam.pages.practice.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamQuestionAnswerVO {
    private Long questionId;
    private String questionContent;
    private String questionType;
    private Map<String, String> options; // 选项
    private String userAnswer; // 用户答案
    private String rightAnswer; // 正确答案
    private Boolean isRight; // 是否正确
    private String analysis; // 解析
}