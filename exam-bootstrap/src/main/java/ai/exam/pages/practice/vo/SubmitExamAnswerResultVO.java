package ai.exam.pages.practice.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class SubmitExamAnswerResultVO {
    private Long examRecordId; // 考试记录id
    private String score; // 考试得分
    private Integer wrongQuestionCount; // 错题数
    private String maxScore; // 最高得分
    private Integer examCount; // 已考次数
    // 错题分布
    private List<WrongQuestionDistributionVO> wrongQuestionDistributionVOS;

    public SubmitExamAnswerResultVO() {
    }
}