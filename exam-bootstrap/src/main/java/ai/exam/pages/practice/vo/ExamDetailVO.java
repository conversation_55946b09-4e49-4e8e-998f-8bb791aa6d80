package ai.exam.pages.practice.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamDetailVO {
    private Long examRecordId; // 考试记录ID
    private String score; // 考试得分
    private Integer totalQuestionCount; // 总题数
    private Integer wrongQuestionCount; // 错题数
    private String accuracy; // 正确率
    private String examTime; // 考试时长
    private List<ExamQuestionAnswerVO> questionAnswers; // 题目作答详情
}