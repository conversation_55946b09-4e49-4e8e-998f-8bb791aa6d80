package ai.exam.pages.practice.vo;

import lombok.Data;

@Data
public class TestPracticeInfoVO {
    private Long questionSetId; // 题集id
    private Integer year;
    private String name; // 题组名称
    private Integer totalQuestionCount; // 总题数
    private Integer completedQuestionCount; // 已完成数量
    private Integer correctQuestionCount;
    private Integer examCount; // 考试次数
    private String maxScore; // 考试最高分

    // 新增
    private Integer gradePass; // 及格分
    private Integer examinationDuration; //考试时长，分钟
    private Boolean isExam; // 是否支持考试模式
    private Integer totalScore; // 总分数
}