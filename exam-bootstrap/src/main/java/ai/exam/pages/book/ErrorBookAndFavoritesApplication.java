package ai.exam.pages.book;

import ai.exam.common.enums.ExamModeEnum;
import ai.exam.domain.exercise.ExerciseDomain;
import ai.exam.domain.exercise.QuestionFavoriteRecordDO;
import ai.exam.domain.exercise.UserSubjectPreferenceDO;
import ai.exam.domain.exercise.WrongQuestionRecordDO;
import ai.exam.domain.knowledge.KnowledgeDomain;
import ai.exam.domain.knowledge.KnowledgeNodeDO;
import ai.exam.domain.question.QuestionDO;
import ai.exam.domain.question.QuestionDomain;
import ai.exam.domain.question.QuestionSetDO;
import ai.exam.domain.question.enums.QuestionSetType;
import ai.exam.domain.question.enums.QuestionType;
import ai.exam.pages.book.mapper.FavoriteQuestionVOMapper;
import ai.exam.pages.book.vo.*;
import ai.exam.pages.common.vo.BooleanResultVO;
import ai.exam.pages.practice.vo.WrongQuestionVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class ErrorBookAndFavoritesApplication {

    @Resource
    private ExerciseDomain exerciseDomain;

    @Resource
    private QuestionDomain questionDomain;

    @Resource
    private KnowledgeDomain knowledgeDomain;

    @Resource
    private FavoriteQuestionVOMapper favoriteQuestionVOMapper;

    public BooleanResultVO saveOrUpdateUserSubjectPreference(Long accountId, String subjectCode, Boolean autoRemoveWrongQuestion) {
        UserSubjectPreferenceDO preferenceDO = new UserSubjectPreferenceDO();
        preferenceDO.setAccountId(accountId);
        preferenceDO.setSubjectCode(subjectCode);
        preferenceDO.setAutoRemoveWrongQuestion(autoRemoveWrongQuestion);

        UserSubjectPreferenceDO result = exerciseDomain.saveOrUpdateUserSubjectPreference(preferenceDO);
        return new BooleanResultVO(result != null);
    }

    public ErrorBookVO getErrorBook(Long accountId, String subjectCode) {
        ErrorBookVO errorBookVO = new ErrorBookVO();

        // 1. 获取用户科目喜好记录
        UserSubjectPreferenceDO preference = exerciseDomain.getUserSubjectPreference(accountId, subjectCode);
        errorBookVO.setAutoRemoveWrongQuestion(preference != null && preference.getAutoRemoveWrongQuestion());

        // 2. 获取该用户对应科目下的所有错题记录
        List<WrongQuestionRecordDO> allWrongQuestionRecords = exerciseDomain.getWrongQuestionRecordsBySubjectAndAccount(subjectCode, accountId);

        // 2.1 对错题记录去重，按练习模式和题目ID分组，每组保留最新的一条
        List<WrongQuestionRecordDO> wrongQuestionRecords = allWrongQuestionRecords.stream().collect(Collectors.groupingBy(record -> record.getExerciseMode() + "_" + record.getQuestionId(), Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(WrongQuestionRecordDO::getUpdatedAt)), optional -> optional.orElse(null)))).values().stream().filter(Objects::nonNull).collect(Collectors.toList());

        // 3. 获取知识点层级结构
        List<KnowledgeNodeDO> knowledgeHierarchy = knowledgeDomain.getKnowledgeHierarchy(subjectCode);

        // 4. 根据练习模式分组错题记录
        Map<String, List<WrongQuestionRecordDO>> wrongQuestionsByMode = wrongQuestionRecords.stream().collect(Collectors.groupingBy(WrongQuestionRecordDO::getExerciseMode));

        // 5. 处理 AI 智练类型的错题重新分类
        redistributeAIWrongQuestions(wrongQuestionsByMode);

        // 6. 初始化返回结果
        List<WrongQuestionSourceVO> chapterSources = new ArrayList<>();
        List<QuestionSetWrongVO> realPracticePapers = new ArrayList<>();
        List<QuestionSetWrongVO> mockPracticePapers = new ArrayList<>();

        // 7. 处理章节类错题(AI智练、章节练习、章节真题)
        handleChapterWrongQuestions(chapterSources, knowledgeHierarchy, wrongQuestionsByMode);

        // 8. 处理试卷类错题(真题演练练习模式、模拟考试练习模式)
        handlePaperWrongQuestions(realPracticePapers, mockPracticePapers, wrongQuestionsByMode);

        // 9. 计算总数
        int totalCount = calculateTotalCount(chapterSources, realPracticePapers, mockPracticePapers);

        // 10. 设置返回结果
        errorBookVO.setTotalCount(totalCount);
        errorBookVO.setChapterSources(chapterSources);
        errorBookVO.setRealPracticePapers(realPracticePapers);
        errorBookVO.setMockPracticePapers(mockPracticePapers);

        return errorBookVO;
    }

    private void handleChapterWrongQuestions(List<WrongQuestionSourceVO> chapterSources, List<KnowledgeNodeDO> knowledgeHierarchy, Map<String, List<WrongQuestionRecordDO>> wrongQuestionsByMode) {
        // 处理章节练习错题
        handleSourceWrongQuestions(chapterSources, knowledgeHierarchy, wrongQuestionsByMode.get(ExamModeEnum.ZJLX.getCode()), ExamModeEnum.ZJLX.getCode());

        // 处理章节真题错题
        handleSourceWrongQuestions(chapterSources, knowledgeHierarchy, wrongQuestionsByMode.get(ExamModeEnum.EXAM.getCode()), ExamModeEnum.EXAM.getCode());

        // 过滤空groups数据
        chapterSources.removeIf(source -> CollectionUtils.isEmpty(source.getGroups()));
    }

    /**
     * 处理某个来源的错题
     */
    private void handleSourceWrongQuestions(List<WrongQuestionSourceVO> chapterSources, List<KnowledgeNodeDO> knowledgeHierarchy, List<WrongQuestionRecordDO> wrongQuestions, String sourceType) {
        if (CollectionUtils.isEmpty(wrongQuestions)) {
            return;
        }

        // 1. 创建来源VO
        WrongQuestionSourceVO sourceVO = new WrongQuestionSourceVO();
        sourceVO.setSource(sourceType);

        // 2. 构建错题分布的层级结构
        List<WrongQuestionGroupVO> groups = buildWrongQuestionGroups(knowledgeHierarchy, wrongQuestions);

        // 如果groups为空就不添加
        if (CollectionUtils.isNotEmpty(groups)) {
            sourceVO.setGroups(groups);
            // 重新计算count,只计算叶子节点
            sourceVO.setCount(calculateLeafNodeCount(groups));
            chapterSources.add(sourceVO);
        }
    }

    /**
     * 根据知识点层级构建错题分布
     */
    private List<WrongQuestionGroupVO> buildWrongQuestionGroups(List<KnowledgeNodeDO> knowledgeHierarchy, List<WrongQuestionRecordDO> wrongQuestions) {
        // 创建错题分布映射
        Map<String, Integer> wrongCountByCode = new HashMap<>();
        for (WrongQuestionRecordDO wrongQuestion : wrongQuestions) {
            // 使用章节编号作为key，如果章节编号为空则使用章编号
            String code = StringUtils.defaultIfBlank(wrongQuestion.getChapterCode(), wrongQuestion.getSectionCode());
            wrongCountByCode.merge(code, 1, Integer::sum);
        }

        // 递归构建层级结构
        return knowledgeHierarchy.stream().map(node -> buildWrongQuestionGroup(node, wrongCountByCode)).filter(group -> group != null && group.getCount() > 0).collect(Collectors.toList());
    }

    /**
     * 根据知识点层级构建错题分布
     */
    private WrongQuestionGroupVO buildWrongQuestionGroup(KnowledgeNodeDO node, Map<String, Integer> wrongCountByCode) {
        WrongQuestionGroupVO group = new WrongQuestionGroupVO();
        group.setCode(node.getCode());
        group.setName(node.getName());
        group.setType(node.getType());

        // 处理子节点
        if (!CollectionUtils.isEmpty(node.getChildren())) {
            List<WrongQuestionGroupVO> children = new ArrayList<>();

            for (KnowledgeNodeDO childNode : node.getChildren()) {
                WrongQuestionGroupVO childGroup = buildWrongQuestionGroup(childNode, wrongCountByCode);
                if (childGroup != null && childGroup.getCount() != null && childGroup.getCount() > 0) {
                    children.add(childGroup);
                }
            }

            if (!children.isEmpty()) {
                group.setChildren(children);
                // 非叶子节点的count应该是子节点count之和
                group.setCount(calculateLeafNodeCount(children));
            } else {
                group.setCount(0);
            }
        } else {
            // 叶子节点直接获取错题数
            group.setCount(wrongCountByCode.getOrDefault(node.getCode(), 0));
        }

        return group;
    }

    /**
     * 处理试卷类错题
     */
    private void handlePaperWrongQuestions(List<QuestionSetWrongVO> realPracticePapers, List<QuestionSetWrongVO> mockPracticePapers, Map<String, List<WrongQuestionRecordDO>> wrongQuestionsByMode) {
        // 处理真题演练练习模式错题
        handlePaperTypeWrongQuestions(realPracticePapers, wrongQuestionsByMode.get(ExamModeEnum.ZTYLLXMS.getCode()));

        // 处理模拟考试练习模式错题
        handlePaperTypeWrongQuestions(mockPracticePapers, wrongQuestionsByMode.get(ExamModeEnum.MOCK_LXMS.getCode()));
    }

    /**
     * 处理某类试卷的错题
     */
    private void handlePaperTypeWrongQuestions(List<QuestionSetWrongVO> paperList, List<WrongQuestionRecordDO> wrongQuestions) {
        if (CollectionUtils.isEmpty(wrongQuestions)) {
            return;
        }

        // 按试卷ID分组统计错题数
        Map<Long, Long> wrongCountByPaper = wrongQuestions.stream().filter(q -> q.getQuestionSetId() != null).collect(Collectors.groupingBy(WrongQuestionRecordDO::getQuestionSetId, Collectors.counting()));

        // 查询试卷信息
        List<QuestionSetDO> questionSets = questionDomain.getQuestionSetsByIds(new ArrayList<>(wrongCountByPaper.keySet()));

        // 构建试卷错题VO
        questionSets.forEach(questionSet -> {
            QuestionSetWrongVO paperVO = new QuestionSetWrongVO();
            paperVO.setQuestionSetId(questionSet.getId());
            paperVO.setName(questionSet.getName());
            paperVO.setCount(wrongCountByPaper.get(questionSet.getId()).intValue());
            paperList.add(paperVO);
        });
    }

    /**
     * 计算错题总数
     */
    private int calculateTotalCount(List<WrongQuestionSourceVO> chapterSources, List<QuestionSetWrongVO> realPracticePapers, List<QuestionSetWrongVO> mockPracticePapers) {
        int total = 0;

        // 统计章节类错题,只统计叶子节点
        for (WrongQuestionSourceVO source : chapterSources) {
            if (CollectionUtils.isNotEmpty(source.getGroups())) {
                total += calculateLeafNodeCount(source.getGroups());
            }
        }

        // 统计试卷类错题
        total += realPracticePapers.stream().mapToInt(QuestionSetWrongVO::getCount).sum();
        total += mockPracticePapers.stream().mapToInt(QuestionSetWrongVO::getCount).sum();

        return total;
    }

    // 递归计算叶子节点的数量
    private int calculateLeafNodeCount(List<WrongQuestionGroupVO> groups) {
        int count = 0;
        for (WrongQuestionGroupVO group : groups) {
            if (CollectionUtils.isEmpty(group.getChildren())) {
                // 叶子节点,累加数量
                count += group.getCount();
            } else {
                // 非叶子节点,递归计算子节点
                count += calculateLeafNodeCount(group.getChildren());
            }
        }
        return count;
    }


    /**
     * 重新分配AI智练错题到其他类别
     */
    private void redistributeAIWrongQuestions(Map<String, List<WrongQuestionRecordDO>> wrongQuestionsByMode) {
        List<WrongQuestionRecordDO> aiWrongQuestions = wrongQuestionsByMode.remove(ExamModeEnum.AIZL.getCode());
        if (CollectionUtils.isEmpty(aiWrongQuestions)) {
            return;
        }

        for (WrongQuestionRecordDO aiQuestion : aiWrongQuestions) {
            // 确定实际归属类型
            String targetMode = determineTargetMode(aiQuestion.getQuestionSetId(), aiQuestion.getChapterCode(), aiQuestion.getSectionCode());
            if (targetMode != null) {
                wrongQuestionsByMode.computeIfAbsent(targetMode, k -> new ArrayList<>()).add(aiQuestion);
            }
        }
    }

    /**
     * 确定AI智练题目的实际归属类型
     */
    private String determineTargetMode(Long questionSetId, String chapterCode, String sectionCode) {
        if (questionSetId == null) {
            return null;
        }

        // 获取题库类型
        QuestionSetType questionSetType = questionDomain.getQuestionSetTypeById(questionSetId);
        if (questionSetType == null) {
            return null;
        }

        // 根据题库类型和字段值确定归属
        if (StringUtils.isNotBlank(chapterCode) || StringUtils.isNotBlank(sectionCode)) {
            // 章节类题目
            return questionSetType == QuestionSetType.CHAP_PRACTICE ? ExamModeEnum.ZJLX.getCode() : ExamModeEnum.EXAM.getCode();
        } else {
            // 试卷类题目
            return questionSetType == QuestionSetType.REAL_EXAMS ? ExamModeEnum.ZTYLLXMS.getCode() : ExamModeEnum.MOCK_LXMS.getCode();
        }
    }

    /**
     * 获取当前科目下所有错题列表
     * 相同题目只返回最新的错题记录
     */
    public List<WrongQuestionVO> getAllWrongQuestions(Long accountId, String subjectCode) {
        // 1. 获取该用户对应科目下的所有错题记录
        List<WrongQuestionRecordDO> allWrongQuestionRecords = exerciseDomain.getWrongQuestionRecordsBySubjectAndAccount(subjectCode, accountId);

        // 2. 对错题记录去重，按题目ID分组，每组保留最新的一条
        List<WrongQuestionRecordDO> uniqueWrongRecords = allWrongQuestionRecords.stream().collect(Collectors.groupingBy(WrongQuestionRecordDO::getQuestionId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(WrongQuestionRecordDO::getUpdatedAt)), optional -> optional.orElse(null)))).values().stream().filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(uniqueWrongRecords)) {
            return Collections.emptyList();
        }

        // 3. 获取题目ID列表
        List<Long> questionIds = uniqueWrongRecords.stream().map(WrongQuestionRecordDO::getQuestionId).collect(Collectors.toList());

        // 4. 批量查询题目信息
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        // 5. 转换为前端展示对象，并保持错题记录顺序
        Map<Long, WrongQuestionRecordDO> wrongRecordMap = uniqueWrongRecords.stream().collect(Collectors.toMap(WrongQuestionRecordDO::getQuestionId, record -> record));

        return questions.stream().map(question -> {
            WrongQuestionVO vo = new WrongQuestionVO();
            vo.setQuestionId(question.getId());
            vo.setQuestionContent(question.getContent());
            vo.setQuestionType(question.getTypeName());
            vo.setOptions(question.getOptions());
            vo.setRightAnswer(question.getAnswer());
            vo.setAnalysis(question.getExplanationWithPrefix());

            // 设置用户答案
            WrongQuestionRecordDO record = wrongRecordMap.get(question.getId());
            if (record != null) {
                vo.setUserAnswer(record.getUserAnswer());
            }

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取错题详细信息
     */
    public List<WrongQuestionVO> getWrongQuestions(String exerciseMode, Long accountId, String subjectCode, String sectionCode, String chapterCode, Long questionSetId) {

        // 1. 获取该用户对应科目下的所有错题记录
        List<WrongQuestionRecordDO> wrongQuestionRecords = exerciseDomain.getWrongQuestionRecordsBySubjectAndAccount(subjectCode, accountId);

        // 2. 根据练习模式过滤记录
        List<WrongQuestionRecordDO> filteredWrongQuestions = new ArrayList<>();

        for (WrongQuestionRecordDO record : wrongQuestionRecords) {
            // 先处理直接匹配的练习模式
            if (exerciseMode.equals(record.getExerciseMode())) {
                if (ExamModeEnum.ZJLX.getCode().equals(exerciseMode) || ExamModeEnum.EXAM.getCode().equals(exerciseMode)) {
                    // 章节练习和章节真题按章节过滤
                    if ((StringUtils.isNotBlank(chapterCode) && chapterCode.equals(record.getChapterCode())) || (StringUtils.isNotBlank(sectionCode) && sectionCode.equals(record.getSectionCode()))) {
                        filteredWrongQuestions.add(record);
                    }
                } else if (ExamModeEnum.MOCK_LXMS.getCode().equals(exerciseMode) || ExamModeEnum.ZTYLLXMS.getCode().equals(exerciseMode)) {
                    // 模拟练习和真题演练按题集过滤
                    if (record.getQuestionSetId() != null && record.getQuestionSetId().equals(questionSetId)) {
                        filteredWrongQuestions.add(record);
                    }
                }
                continue;
            }

            // 处理 AI 智练的错题记录
            if (ExamModeEnum.AIZL.getCode().equals(record.getExerciseMode())) {
                QuestionSetType questionSetType = questionDomain.getQuestionSetTypeById(record.getQuestionSetId());
                if (questionSetType == null) {
                    continue;
                }

                // 有章节信息的AI练习题目
                if (StringUtils.isNotBlank(record.getChapterCode()) || StringUtils.isNotBlank(record.getSectionCode())) {
                    // 章节练习
                    if (ExamModeEnum.ZJLX.getCode().equals(exerciseMode) && questionSetType == QuestionSetType.CHAP_PRACTICE) {
                        if ((StringUtils.isNotBlank(chapterCode) && chapterCode.equals(record.getChapterCode())) || (StringUtils.isNotBlank(sectionCode) && sectionCode.equals(record.getSectionCode()))) {
                            filteredWrongQuestions.add(record);
                        }
                    }
                    // 章节真题
                    else if (ExamModeEnum.EXAM.getCode().equals(exerciseMode) && questionSetType == QuestionSetType.CHAP_REAL_EXAMS) {
                        if ((StringUtils.isNotBlank(chapterCode) && chapterCode.equals(record.getChapterCode())) || (StringUtils.isNotBlank(sectionCode) && sectionCode.equals(record.getSectionCode()))) {
                            filteredWrongQuestions.add(record);
                        }
                    }
                }
                // 无章节信息的AI练习题目
                else {
                    // 真题演练
                    if (ExamModeEnum.ZTYLLXMS.getCode().equals(exerciseMode) && questionSetType == QuestionSetType.REAL_EXAMS) {
                        if (record.getQuestionSetId() != null && record.getQuestionSetId().equals(questionSetId)) {
                            filteredWrongQuestions.add(record);
                        }
                    }
                    // 模拟练习
                    else if (ExamModeEnum.MOCK_LXMS.getCode().equals(exerciseMode) && questionSetType == QuestionSetType.MOCK_EXAMS) {
                        if (record.getQuestionSetId() != null && record.getQuestionSetId().equals(questionSetId)) {
                            filteredWrongQuestions.add(record);
                        }
                    }
                }
            }
        }

        // 3. 获取题目ID列表
        List<Long> questionIds = filteredWrongQuestions.stream().map(WrongQuestionRecordDO::getQuestionId).collect(Collectors.toList());

        // 4. 批量查询题目信息
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        AtomicInteger num = new AtomicInteger(0);
        // 5. 转换为前端展示对象
        return questions.stream().map(question -> {
            WrongQuestionVO vo = new WrongQuestionVO();
            vo.setQuestionId(question.getId());
            vo.setQuestionContent(question.getContent());
            vo.setQuestionType(question.getTypeName());
            vo.setOptions(question.getOptions());
            vo.setRightAnswer(question.getAnswer());
            vo.setAnalysis(question.getExplanationWithPrefix());
            vo.setNumber(num.incrementAndGet());
            // 设置用户答案
            filteredWrongQuestions.stream().filter(record -> record.getQuestionId().equals(question.getId())).findFirst().ifPresent(record -> vo.setUserAnswer(record.getUserAnswer()));

            return vo;
        }).collect(Collectors.toList());
    }

    public ErrorBookSubmitAnswerResultVO submitAnswer(Long accountId, String subjectCode, Long questionId, String answer) {
        // 1. 根据题目id调用QuestionDomain领域中的getQuestionsByQuestionIds方法
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(Collections.singletonList(questionId));
        if (questions.isEmpty()) {
            throw new RuntimeException("题目不存在");
        }
        QuestionDO question = questions.get(0);

        // 判断用户此次的答题结果
        boolean isRight = question.getAnswer().equals(answer) || QuestionType.OPEN.getName().equals(question.getType());

        ErrorBookSubmitAnswerResultVO result = new ErrorBookSubmitAnswerResultVO();
        result.setIsRight(isRight);
        result.setAnalysis(question.getExplanationWithPrefix());
        result.setRightAnswer(question.getAnswer());
        result.setRemoved(false);
        if (isRight) {
            // 3. 若此次答题结果正确, 调用ExerciseDomain领域中getUserSubjectPreference获取用户科目喜好记录
            UserSubjectPreferenceDO preference = exerciseDomain.getUserSubjectPreference(accountId, subjectCode);
            boolean autoRemove = preference != null && preference.getAutoRemoveWrongQuestion();

            // 4. 如果自动删除为true则调用ExerciseDomain领域根据科目编号、账号id及题目id将对应的记录delFlag设置为1
            if (autoRemove) {
                exerciseDomain.removeWrongQuestionRecord(subjectCode, accountId, questionId);
                result.setRemoved(true);
            }
        }

        return result;
    }

    /**
     * =========================================================
     * 获取收藏分布统计
     */
    public QuestionFavoriteVO getFavoriteDistribution(Long accountId, String subjectCode) {
        QuestionFavoriteVO questionFavoriteVO = new QuestionFavoriteVO();

        // 1. 获取该用户对应科目下的所有收藏记录
        List<QuestionFavoriteRecordDO> allFavoriteRecords = exerciseDomain.getQuestionFavoriteRecordsBySubjectAndAccount(subjectCode, accountId);

        // 2. 对收藏记录按练习模式分组
        Map<String, List<QuestionFavoriteRecordDO>> favoritesByMode = allFavoriteRecords.stream()
                .collect(Collectors.groupingBy(QuestionFavoriteRecordDO::getExerciseMode));

        // 3. 处理AI智练类型的收藏重新分类
        redistributeAIFavoriteQuestions(favoritesByMode);

        // 4. 初始化返回结果
        List<FavoriteQuestionSourceVO> chapterSources = new ArrayList<>();
        List<QuestionSetFavoriteVO> realPracticePapers = new ArrayList<>();
        List<QuestionSetFavoriteVO> mockPracticePapers = new ArrayList<>();

        // 5. 获取知识点层级结构
        List<KnowledgeNodeDO> knowledgeHierarchy = knowledgeDomain.getKnowledgeHierarchy(subjectCode);

        // 6. 处理章节类收藏(AI智练、章节练习、章节真题)
        handleChapterFavoriteQuestions(chapterSources, knowledgeHierarchy, favoritesByMode);

        // 7. 处理试卷类收藏(真题演练练习模式、模拟考试练习模式)
        handlePaperFavoriteQuestions(realPracticePapers, mockPracticePapers, favoritesByMode);

        // 8. 计算总数
        int totalCount = calculateTotalFavoriteCount(chapterSources, realPracticePapers, mockPracticePapers);

        // 9. 设置返回结果
        questionFavoriteVO.setTotalCount(totalCount);
        questionFavoriteVO.setChapterSources(chapterSources);
        questionFavoriteVO.setRealPracticePapers(realPracticePapers);
        questionFavoriteVO.setMockPracticePapers(mockPracticePapers);

        return questionFavoriteVO;
    }

    /**
     * 重新分配AI智练收藏到其他类别
     */
    private void redistributeAIFavoriteQuestions(Map<String, List<QuestionFavoriteRecordDO>> favoritesByMode) {
        List<QuestionFavoriteRecordDO> aiFavorites = favoritesByMode.remove(ExamModeEnum.AIZL.getCode());
        if (CollectionUtils.isEmpty(aiFavorites)) {
            return;
        }

        for (QuestionFavoriteRecordDO aiFavorite : aiFavorites) {
            // 确定实际归属类型
            String targetMode = determineTargetMode(aiFavorite.getQuestionSetId(), aiFavorite.getChapterCode(), aiFavorite.getSectionCode());
            if (targetMode != null) {
                favoritesByMode.computeIfAbsent(targetMode, k -> new ArrayList<>()).add(aiFavorite);
            }
        }
    }

    /**
     * 处理章节类收藏题目
     */
    private void handleChapterFavoriteQuestions(List<FavoriteQuestionSourceVO> chapterSources,
                                                List<KnowledgeNodeDO> knowledgeHierarchy,
                                                Map<String, List<QuestionFavoriteRecordDO>> favoritesByMode) {

        // 处理章节练习收藏
        handleSourceFavoriteQuestions(chapterSources, knowledgeHierarchy,
                favoritesByMode.get(ExamModeEnum.ZJLX.getCode()), ExamModeEnum.ZJLX.getCode());

        // 处理章节真题收藏
        handleSourceFavoriteQuestions(chapterSources, knowledgeHierarchy,
                favoritesByMode.get(ExamModeEnum.EXAM.getCode()), ExamModeEnum.EXAM.getCode());

        // 过滤空groups数据
        chapterSources.removeIf(source -> CollectionUtils.isEmpty(source.getGroups()));
    }

    /**
     * 处理某个来源的收藏题目
     */
    private void handleSourceFavoriteQuestions(List<FavoriteQuestionSourceVO> chapterSources,
                                               List<KnowledgeNodeDO> knowledgeHierarchy,
                                               List<QuestionFavoriteRecordDO> favorites, String sourceType) {

        if (CollectionUtils.isEmpty(favorites)) {
            return;
        }

        // 1. 创建来源VO
        FavoriteQuestionSourceVO sourceVO = new FavoriteQuestionSourceVO();
        sourceVO.setSource(sourceType);

        // 2. 构建收藏分布的层级结构
        List<FavoriteQuestionGroupVO> groups = buildFavoriteQuestionGroups(knowledgeHierarchy, favorites);

        // 如果groups为空就不添加
        if (CollectionUtils.isNotEmpty(groups)) {
            sourceVO.setGroups(groups);
            // 重新计算count,只计算叶子节点
            sourceVO.setCount(calculateFavoriteLeafNodeCount(groups));
            chapterSources.add(sourceVO);
        }
    }

    /**
     * 根据知识点层级构建收藏分布
     */
    private List<FavoriteQuestionGroupVO> buildFavoriteQuestionGroups(
            List<KnowledgeNodeDO> knowledgeHierarchy,
            List<QuestionFavoriteRecordDO> favorites) {

        // 创建收藏分布映射
        Map<String, Integer> favoriteCountByCode = new HashMap<>();
        for (QuestionFavoriteRecordDO favorite : favorites) {
            // 使用章节编号作为key，如果章节编号为空则使用章编号
            String code = StringUtils.defaultIfBlank(favorite.getChapterCode(), favorite.getSectionCode());
            favoriteCountByCode.merge(code, 1, Integer::sum);
        }

        // 递归构建层级结构
        return knowledgeHierarchy.stream()
                .map(node -> buildFavoriteQuestionGroup(node, favoriteCountByCode))
                .filter(group -> group != null && group.getCount() > 0)
                .collect(Collectors.toList());
    }

    /**
     * 构建收藏题目分组
     */
    private FavoriteQuestionGroupVO buildFavoriteQuestionGroup(KnowledgeNodeDO node,
                                                               Map<String, Integer> favoriteCountByCode) {

        FavoriteQuestionGroupVO group = new FavoriteQuestionGroupVO();
        group.setCode(node.getCode());
        group.setName(node.getName());
        group.setType(node.getType());

        // 处理子节点
        if (!CollectionUtils.isEmpty(node.getChildren())) {
            List<FavoriteQuestionGroupVO> children = new ArrayList<>();

            for (KnowledgeNodeDO childNode : node.getChildren()) {
                FavoriteQuestionGroupVO childGroup = buildFavoriteQuestionGroup(childNode, favoriteCountByCode);
                if (childGroup != null && childGroup.getCount() != null && childGroup.getCount() > 0) {
                    children.add(childGroup);
                }
            }

            if (!children.isEmpty()) {
                group.setChildren(children);
                // 非叶子节点的count应该是子节点count之和
                group.setCount(calculateFavoriteLeafNodeCount(children));
            } else {
                group.setCount(0);
            }
        } else {
            // 叶子节点直接获取收藏数
            group.setCount(favoriteCountByCode.getOrDefault(node.getCode(), 0));
        }

        return group;
    }

    /**
     * 处理试卷类收藏
     */
    private void handlePaperFavoriteQuestions(List<QuestionSetFavoriteVO> realPracticePapers,
                                              List<QuestionSetFavoriteVO> mockPracticePapers,
                                              Map<String, List<QuestionFavoriteRecordDO>> favoritesByMode) {

        // 处理真题演练练习模式收藏
        handlePaperTypeFavoriteQuestions(realPracticePapers,
                favoritesByMode.get(ExamModeEnum.ZTYLLXMS.getCode()));

        // 处理模拟考试练习模式收藏
        handlePaperTypeFavoriteQuestions(mockPracticePapers,
                favoritesByMode.get(ExamModeEnum.MOCK_LXMS.getCode()));
    }

    /**
     * 处理某类试卷的收藏
     */
    private void handlePaperTypeFavoriteQuestions(List<QuestionSetFavoriteVO> paperList,
                                                  List<QuestionFavoriteRecordDO> favorites) {

        if (CollectionUtils.isEmpty(favorites)) {
            return;
        }

        // 按试卷ID分组统计收藏数
        Map<Long, Long> favoriteCountByPaper = favorites.stream()
                .filter(q -> q.getQuestionSetId() != null)
                .collect(Collectors.groupingBy(QuestionFavoriteRecordDO::getQuestionSetId,
                        Collectors.counting()));

        // 查询试卷信息
        List<QuestionSetDO> questionSets = questionDomain.getQuestionSetsByIds(
                new ArrayList<>(favoriteCountByPaper.keySet()));

        // 构建试卷收藏VO
        questionSets.forEach(questionSet -> {
            QuestionSetFavoriteVO paperVO = new QuestionSetFavoriteVO();
            paperVO.setQuestionSetId(questionSet.getId());
            paperVO.setName(questionSet.getName());
            paperVO.setCount(favoriteCountByPaper.get(questionSet.getId()).intValue());
            paperList.add(paperVO);
        });
    }

    /**
     * 计算收藏总数
     */
    private int calculateTotalFavoriteCount(List<FavoriteQuestionSourceVO> chapterSources,
                                            List<QuestionSetFavoriteVO> realPracticePapers,
                                            List<QuestionSetFavoriteVO> mockPracticePapers) {

        int total = 0;

        // 统计章节类收藏,只统计叶子节点
        for (FavoriteQuestionSourceVO source : chapterSources) {
            if (CollectionUtils.isNotEmpty(source.getGroups())) {
                total += calculateFavoriteLeafNodeCount(source.getGroups());
            }
        }

        // 统计试卷类收藏
        total += realPracticePapers.stream().mapToInt(QuestionSetFavoriteVO::getCount).sum();
        total += mockPracticePapers.stream().mapToInt(QuestionSetFavoriteVO::getCount).sum();

        return total;
    }

    // 递归计算叶子节点的数量
    private int calculateFavoriteLeafNodeCount(List<FavoriteQuestionGroupVO> groups) {
        int count = 0;
        for (FavoriteQuestionGroupVO group : groups) {
            if (CollectionUtils.isEmpty(group.getChildren())) {
                // 叶子节点,累加数量
                count += group.getCount();
            } else {
                // 非叶子节点,递归计算子节点
                count += calculateFavoriteLeafNodeCount(group.getChildren());
            }
        }
        return count;
    }

    /**
     * 获取当前科目下所有收藏题目列表
     * 相同题目只返回最新的收藏记录
     */
    public List<FavoriteQuestionVO> getAllFavoriteQuestions(Long accountId, String subjectCode) {
        // 1. 获取该用户对应科目下的所有收藏记录
        List<QuestionFavoriteRecordDO> allFavoriteRecords = exerciseDomain.getQuestionFavoriteRecordsBySubjectAndAccount(subjectCode, accountId);

        // 2. 对收藏记录去重，按题目ID分组，每组保留最新的一条
        List<QuestionFavoriteRecordDO> uniqueFavoriteRecords = allFavoriteRecords.stream()
                .collect(Collectors.groupingBy(
                        QuestionFavoriteRecordDO::getQuestionId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(QuestionFavoriteRecordDO::getUpdatedAt)),
                                optional -> optional.orElse(null)
                        )
                ))
                .values()
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(uniqueFavoriteRecords)) {
            return Collections.emptyList();
        }

        // 3. 获取题目ID列表
        List<Long> questionIds = uniqueFavoriteRecords.stream()
                .map(QuestionFavoriteRecordDO::getQuestionId)
                .collect(Collectors.toList());

        // 4. 批量查询题目信息
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        // 5. 转换为前端展示对象
        return questions.stream()
                .map(question -> {
                    FavoriteQuestionVO vo = new FavoriteQuestionVO();
                    vo.setQuestionId(question.getId());
                    vo.setQuestionContent(question.getContent());
                    vo.setQuestionType(question.getTypeName());
                    vo.setOptions(question.getOptions());

                    vo.setUserAnswer("");
                    vo.setRightAnswer(question.getAnswer());
                    vo.setAnalysis(question.getExplanationWithPrefix());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取收藏详细信息
     */
    public List<FavoriteQuestionVO> getFavoriteQuestions(String exerciseMode, Long accountId, String subjectCode,
                                                         String sectionCode, String chapterCode, Long questionSetId) {
        // 1. 获取该用户对应科目下的所有收藏记录
        List<QuestionFavoriteRecordDO> favoriteRecords = exerciseDomain.getQuestionFavoriteRecordsBySubjectAndAccount(subjectCode, accountId);

        // 2. 根据练习模式过滤记录
        List<QuestionFavoriteRecordDO> filteredFavoriteQuestions = new ArrayList<>();

        for (QuestionFavoriteRecordDO record : favoriteRecords) {
            // 获取当前记录应该匹配的练习模式列表
            List<String> targetModes = new ArrayList<>();
            targetModes.add(record.getExerciseMode());

            // 如果是AI智练的收藏记录,需要根据题库类型重新分类
            if (ExamModeEnum.AIZL.getCode().equals(record.getExerciseMode())) {
                QuestionSetType questionSetType = questionDomain.getQuestionSetTypeById(record.getQuestionSetId());
                if (questionSetType == null) {
                    continue;
                }

                // 将AI智练记录映射到对应的练习模式
                if (StringUtils.isNotBlank(record.getChapterCode()) || StringUtils.isNotBlank(record.getSectionCode())) {
                    // 章节练习或章节真题
                    targetModes.clear();
                    targetModes.add(questionSetType == QuestionSetType.CHAP_PRACTICE ?
                            ExamModeEnum.ZJLX.getCode() : ExamModeEnum.EXAM.getCode());
                } else {
                    // 试卷类练习
                    targetModes.clear();
                    if (questionSetType == QuestionSetType.REAL_EXAMS) {
                        targetModes.add(ExamModeEnum.ZTYLLXMS.getCode());
                        targetModes.add(ExamModeEnum.ZTYLKSMS.getCode());
                    } else if (questionSetType == QuestionSetType.MOCK_EXAMS) {
                        targetModes.add(ExamModeEnum.MOCK_LXMS.getCode());
                        targetModes.add(ExamModeEnum.MOCK_EXAMS.getCode());
                    }
                }
            } else if (ExamModeEnum.MOCK_LXMS.getCode().equals(record.getExerciseMode()) ||
                    ExamModeEnum.MOCK_EXAMS.getCode().equals(record.getExerciseMode())) {
                // 模拟考试相关模式
                targetModes.add(ExamModeEnum.MOCK_LXMS.getCode());
                targetModes.add(ExamModeEnum.MOCK_EXAMS.getCode());
            } else if (ExamModeEnum.ZTYLLXMS.getCode().equals(record.getExerciseMode()) ||
                    ExamModeEnum.ZTYLKSMS.getCode().equals(record.getExerciseMode())) {
                // 真题演练相关模式
                targetModes.add(ExamModeEnum.ZTYLLXMS.getCode());
                targetModes.add(ExamModeEnum.ZTYLKSMS.getCode());
            }

            // 检查是否匹配当前请求的练习模式
            if (!targetModes.contains(exerciseMode)) {
                continue;
            }

            // 根据练习模式类型进行具体过滤
            if (ExamModeEnum.ZJLX.getCode().equals(exerciseMode) ||
                    ExamModeEnum.EXAM.getCode().equals(exerciseMode)) {
                // 章节练习和章节真题按章节过滤
                if ((StringUtils.isNotBlank(chapterCode) && chapterCode.equals(record.getChapterCode())) ||
                        (StringUtils.isNotBlank(sectionCode) && sectionCode.equals(record.getSectionCode()))) {
                    filteredFavoriteQuestions.add(record);
                }
            } else {
                // 模拟练习和真题演练按题集过滤
                if (record.getQuestionSetId() != null && record.getQuestionSetId().equals(questionSetId)) {
                    filteredFavoriteQuestions.add(record);
                }
            }
        }

        // 3. 获取题目ID列表
        List<Long> questionIds = filteredFavoriteQuestions.stream()
                .map(QuestionFavoriteRecordDO::getQuestionId)
                .collect(Collectors.toList());

        // 4. 批量查询题目信息
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        AtomicInteger num = new AtomicInteger(0);
        // 5. 转换为前端展示对象
        return questions.stream()
                .map(question -> {
                    FavoriteQuestionVO vo = new FavoriteQuestionVO();
                    vo.setQuestionId(question.getId());
                    vo.setQuestionContent(question.getContent());
                    vo.setQuestionType(question.getTypeName());
                    vo.setOptions(question.getOptions());

                    vo.setUserAnswer("");
                    vo.setRightAnswer(question.getAnswer());
                    vo.setAnalysis(question.getExplanationWithPrefix());
                    vo.setNumber(num.incrementAndGet());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    public FavoriteSubmitAnswerResultVO submitFavoriteAnswer(Long accountId, String subjectCode, Long questionId, String answer) {
        // 2. 根据题目id调用QuestionDomain领域中的getQuestionsByQuestionIds方法
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(Collections.singletonList(questionId));
        if (questions.isEmpty()) {
            throw new RuntimeException("题目不存在");
        }
        QuestionDO question = questions.get(0);

        // 3. 判断用户此次的答题结果
        boolean isRight = question.getAnswer().equals(answer);

        FavoriteSubmitAnswerResultVO result = new FavoriteSubmitAnswerResultVO();
        result.setIsRight(isRight);
        result.setAnalysis(question.getExplanationWithPrefix());
        result.setRightAnswer(question.getAnswer());
        return result;
    }

    public BooleanResultVO questionFavorite(Long accountId, FavoriteQuestionRequestionVO favorite) {
        exerciseDomain.saveOrUpdateQuestionFavoriteRecord(accountId, favoriteQuestionVOMapper.toDOByVO(favorite));
        return new BooleanResultVO(true);
    }

    public List<Long> getFavoriteQuestionIds(Long accountId, String subjectCode,String exerciseMode) {
        return exerciseDomain.getFavoriteQuestionIds(accountId, subjectCode,exerciseMode);
    }
}