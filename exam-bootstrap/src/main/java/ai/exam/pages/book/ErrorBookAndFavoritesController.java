package ai.exam.pages.book;

import ai.exam.pages.book.vo.*;
import ai.exam.pages.common.vo.BooleanResultVO;
import ai.exam.pages.knowledgeslicing.vo.SubmitStudyAnswerRequestVO;
import ai.exam.pages.practice.vo.WrongQuestionVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-07-01
 */
@RestController
@RequestMapping("/api/book")
public class ErrorBookAndFavoritesController {

    @Resource
    private ErrorBookAndFavoritesApplication errorBookAndFavoritesApplication;

    @PutMapping("/user-subject-preference")
    public BooleanResultVO saveOrUpdateUserSubjectPreference(Long accountId, @RequestParam String subjectCode, @RequestParam Boolean autoRemoveWrongQuestion) {
        return errorBookAndFavoritesApplication.saveOrUpdateUserSubjectPreference(accountId, subjectCode, autoRemoveWrongQuestion);
    }

    @GetMapping("/error-book")
    public ErrorBookVO getErrorBook(Long accountId, @RequestParam String subjectCode) {
        return errorBookAndFavoritesApplication.getErrorBook(accountId, subjectCode);
    }

    @GetMapping("/wrong-questions")
    public List<WrongQuestionVO> getWrongQuestions(Long accountId, @RequestParam String exerciseMode, @RequestParam String subjectCode, @RequestParam(required = false) String sectionCode, @RequestParam(required = false) String chapterCode, @RequestParam(required = false) Long questionSetId) {
        return errorBookAndFavoritesApplication.getWrongQuestions(exerciseMode, accountId, subjectCode, sectionCode, chapterCode, questionSetId);
    }

    @GetMapping("/all-wrong-questions")
    public List<WrongQuestionVO> getAllWrongQuestions(Long accountId, @RequestParam String subjectCode) {
        return errorBookAndFavoritesApplication.getAllWrongQuestions(accountId, subjectCode);
    }

    @PostMapping("/submit-answer")
    public ErrorBookSubmitAnswerResultVO submitAnswer(Long accountId, @RequestParam String subjectCode, @RequestParam Long questionId, @RequestParam String answer) {
        return errorBookAndFavoritesApplication.submitAnswer(accountId, subjectCode, questionId, answer);
    }

    // ===================收藏====================

    @GetMapping("/favorite-distribution")
    public QuestionFavoriteVO getFavoriteDistribution(Long accountId, @RequestParam String subjectCode) {
        return errorBookAndFavoritesApplication.getFavoriteDistribution(accountId, subjectCode);
    }

    @GetMapping("/all-favorite-questions")
    public List<FavoriteQuestionVO> getAllFavoriteQuestions(Long accountId, @RequestParam String subjectCode) {
        return errorBookAndFavoritesApplication.getAllFavoriteQuestions(accountId, subjectCode);
    }

    @GetMapping("/favorite-questions")
    public List<FavoriteQuestionVO> getFavoriteQuestions(Long accountId
            , @RequestParam String exerciseMode
            , @RequestParam String subjectCode
            , @RequestParam(required = false) String sectionCode
            , @RequestParam(required = false) String chapterCode
            , @RequestParam(required = false) Long questionSetId) {
        return errorBookAndFavoritesApplication.getFavoriteQuestions(exerciseMode, accountId, subjectCode, sectionCode, chapterCode, questionSetId);
    }

    @PostMapping("/submit-favorite-answer")
    public FavoriteSubmitAnswerResultVO submitFavoriteAnswer(Long accountId, @RequestParam String subjectCode, @RequestParam Long questionId, @RequestParam String answer) {
        return errorBookAndFavoritesApplication.submitFavoriteAnswer(accountId, subjectCode, questionId, answer);
    }

    @PutMapping("/question-favorite")
    public BooleanResultVO questionFavorite(HttpServletRequest request, @RequestBody FavoriteQuestionRequestionVO favorite) {
        Long accountId = (Long) request.getAttribute("accountId");
        return errorBookAndFavoritesApplication.questionFavorite(accountId, favorite);
    }

    @GetMapping("/getFavoriteQuestionIds")
    public List<Long> getFavoriteQuestionIds(Long accountId, @RequestParam String subjectCode,@RequestParam String exerciseMode) {
        return errorBookAndFavoritesApplication.getFavoriteQuestionIds(accountId, subjectCode,exerciseMode);
    }
}
