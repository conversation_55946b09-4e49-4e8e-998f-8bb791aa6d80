package ai.exam.pages.book.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 收藏来源分组
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FavoriteQuestionSourceVO {

    private String source;  // exerciseMode来源类型 EXAM/ZJLX, AIZL分布到不同里面去
    private Integer count;  // 该来源下的收藏总数
    private List<FavoriteQuestionGroupVO> groups;  // 章节结构数据

}