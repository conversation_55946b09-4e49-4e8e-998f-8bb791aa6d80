package ai.exam.pages.book.vo;

import lombok.Data;

import java.util.List;

/**
 * 收藏页面的数据结构
 * <AUTHOR>
 * @create 2024-07-01
 */
@Data
public class QuestionFavoriteVO {

    // 收藏总数
    private Integer totalCount;

    // 按来源分类的收藏分布（篇章节结构）
    private List<FavoriteQuestionSourceVO> chapterSources;  // AI智练、章节练习、章节真题
    // 试卷类数据（直接是试卷列表）
    private List<QuestionSetFavoriteVO> realPracticePapers;    // 真题演练练习模式
    private List<QuestionSetFavoriteVO> mockPracticePapers;    // 模拟考试练习模式
}
