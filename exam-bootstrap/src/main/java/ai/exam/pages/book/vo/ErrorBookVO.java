package ai.exam.pages.book.vo;

import ai.exam.pages.practice.vo.WrongQuestionDistributionVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-07-01
 */
@Data
public class ErrorBookVO {

    // 是否自动移除错题
    private Boolean autoRemoveWrongQuestion;
    // 科目下所有错题总数
    private Integer totalCount;

    // 按来源分类的错题分布（篇章节结构）
    private List<WrongQuestionSourceVO> chapterSources;  // AI智练、章节练习、章节真题
    // 试卷类数据（直接是试卷列表）
    private List<QuestionSetWrongVO> realPracticePapers;    // 真题演练练习模式
    private List<QuestionSetWrongVO> mockPracticePapers;    // 模拟考试练习模式
}
