package ai.exam.pages.diagnosis;

import ai.exam.domain.diagnosis.DiagnosisDomain;
import ai.exam.domain.diagnosis.QuestionAnswerDO;
import ai.exam.domain.diagnosis.UserDiagnosisDO;
import ai.exam.domain.exercise.ExerciseDomain;
import ai.exam.domain.question.QuestionDO;
import ai.exam.domain.question.QuestionDomain;
import ai.exam.domain.question.UserQuestionDO;
import ai.exam.pages.diagnosis.vo.*;
import ai.exam.pages.knowledgeslicing.vo.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-06-20
 */
@Component
public class LearningDiagnosisPageApplication {

    @Resource
    private QuestionDomain questionDomain;

    @Resource
    private DiagnosisDomain diagnosisDomain;

    @Resource
    private ExerciseDomain exerciseDomain;

    public LearningDiagnosisInfoVO getDiagnosisInfo(Long accountId, String certificateCode, String subjectCode) {
        LearningDiagnosisInfoVO userDiagnosisVO = new LearningDiagnosisInfoVO();

        // 1. 调用DiagnosisDomain领域根据账号id、科目编号获取用户诊断记录
        UserDiagnosisDO userDiagnosisDO = diagnosisDomain.getUserDiagnosisByAccountIdAndSubjectCode(accountId, subjectCode);

        if (userDiagnosisDO != null) {
            // 2. 若用户存在诊断记录isCompleted设置为true并封装诊断记录信息
            userDiagnosisVO.setIsCompleted(true);
            UserDiagnosisVO userDiagnosisInfo = new UserDiagnosisVO();
            userDiagnosisInfo.setDiagnosisAccuracy(userDiagnosisDO.getDiagnosisAccuracy());
            userDiagnosisInfo.setDiagnosisTime(userDiagnosisDO.getDiagnosisTime());
            userDiagnosisInfo.setWrongCount(userDiagnosisDO.getWrongCount());
            userDiagnosisInfo.setTotalCount(userDiagnosisDO.getTotalCount());
            userDiagnosisInfo.setConclusion(userDiagnosisDO.getConclusion());
            userDiagnosisVO.setUserDiagnosisInfo(userDiagnosisInfo);
        } else {
            // 3. 若用户不存在诊断记录isCompleted设置为false
            userDiagnosisVO.setIsCompleted(false);
        }

        return userDiagnosisVO;
    }

    public List<ExamQuestionVO> submitDiagnosisAndGetQuestions(Long accountId, LearningDiagnosisSubmitVO diagnosisInfo) {
        List<ChapterDiagnosisVO> chapterDiagnosis = diagnosisInfo.getChapterDiagnosis() == null ? new ArrayList<>() : diagnosisInfo.getChapterDiagnosis();
        List<String> masteredChapterCodes = chapterDiagnosis.stream().filter(x -> x.getIsCompleted() != null && x.getIsCompleted()).map(ChapterDiagnosisVO::getChapterCode).collect(Collectors.toList());
        // 1. 根据账号id、科目编号、用户选择的完成的章编号列表调用DiagnosisDomain领域的getDiagnosisQuestions方法获取题目列表
        List<QuestionDO> questionDOList = diagnosisDomain.getDiagnosisQuestions(accountId, diagnosisInfo.getSubjectCode(), masteredChapterCodes);

        // 2. 将从DiagnosisDomain中获取的题目信息转换为QuestionVO对象
        List<ExamQuestionVO> result = questionDOList.stream().map(questionDO -> {
            ExamQuestionVO questionVO = new ExamQuestionVO();
            questionVO.setQuestionId(questionDO.getId());
            questionVO.setQuestionType(questionDO.getTypeName());
            questionVO.setQuestionContent(questionDO.getContent());
            questionVO.setOptions(questionDO.getOptions());
            return questionVO;
        }).collect(Collectors.toList());

        return result;
    }

    public UserDiagnosisVO submitDiagnosisQuestions(Long accountId, DiagnosisQuestionSubmitVO submitDTO) {
        List<Long> questionIds = submitDTO.getAnswers().stream().map(UserQuestionDO::getQuestionId).collect(Collectors.toList());

        // 1. 根据题目id列表调用QuestionDomain领域中getQuestionsByQuestionIds方法获取题目信息
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        // 2. 判断用户提交的答案与题目的答案比对是否一致,判断是否正确, 并且计算出正确率
        AtomicInteger correctCount = new AtomicInteger(0);
        int totalCount = questions.size();
        List<Long> currectQuestionIds = new ArrayList<>();
        List<UserQuestionDO> wrongQuestions = submitDTO.getAnswers().stream().filter(submitAnswerDO -> {
            QuestionDO questionDO = questions.stream().filter(question -> submitAnswerDO.getQuestionId().equals(question.getId())).findFirst().orElse(null);
            if (questionDO != null && questionDO.getAnswer().equals(submitAnswerDO.getAnswer())) {
                correctCount.getAndIncrement();
                currectQuestionIds.add(questionDO.getId());
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        double accuracy = (double) correctCount.get() / totalCount;
        String accuracyStr = String.format("%.0f%%", accuracy * 100);

        // 3. 根据账号id、科目编号、用户选择的答案列表调用DiagnosisDomain领域的getDiagnosisResult方法获取诊断结果
        List<QuestionAnswerDO> userAnswers = submitDTO.getAnswers().stream().map(dto -> {
            QuestionAnswerDO questionAnswerDO = new QuestionAnswerDO();
            questionAnswerDO.setQuestionId(dto.getQuestionId());
            questionAnswerDO.setUserAnswer(dto.getAnswer());
            questionAnswerDO.setIsRight(currectQuestionIds.contains(dto.getQuestionId()));
            return questionAnswerDO;
        }).collect(Collectors.toList());
        String diagnosisResult = diagnosisDomain.getDiagnosisResult(accountId, submitDTO.getSubjectCode(), userAnswers);

        // 4. 调用DiagnosisDomain领域的saveOrUpdateUserDiagnosis方法保存用户诊断记录
        List<ChapterDiagnosisVO> chapterDiagnosis = submitDTO.getChapterDiagnosis() == null ? new ArrayList<>() : submitDTO.getChapterDiagnosis();
        List<String> masteredChapterCodes = chapterDiagnosis.stream().filter(x -> x.getIsCompleted() != null && x.getIsCompleted()).map(ChapterDiagnosisVO::getChapterCode).collect(Collectors.toList());
        UserDiagnosisDO userDiagnosisDO = new UserDiagnosisDO();
        userDiagnosisDO.setAccountId(accountId);
        userDiagnosisDO.setCertificateCode(submitDTO.getCertificateCode());
        userDiagnosisDO.setSubjectCode(submitDTO.getSubjectCode());
        userDiagnosisDO.setPrepareProgress(submitDTO.getPrepareProgress());
        userDiagnosisDO.setLearningStatus(submitDTO.getLearningStatus());
        userDiagnosisDO.setMasteryLevel(submitDTO.getMasteryLevel());
        userDiagnosisDO.setMasteredChapterCodes(String.join(",", masteredChapterCodes));
        userDiagnosisDO.setDiagnosisAccuracy(accuracyStr);
        userDiagnosisDO.setDiagnosisTime(submitDTO.getDiagnosisTime());
        userDiagnosisDO.setWrongCount(totalCount - correctCount.get());
        userDiagnosisDO.setTotalCount(totalCount);
        userDiagnosisDO.setConclusion(diagnosisResult);
        UserDiagnosisDO savedUserDiagnosis = diagnosisDomain.saveOrUpdateUserDiagnosis(userDiagnosisDO);

        // 6. 返回用户诊断记录封装成UserDiagnosisDTO信息返回
        UserDiagnosisVO userDiagnosisVO = new UserDiagnosisVO();
        userDiagnosisVO.setDiagnosisAccuracy(savedUserDiagnosis.getDiagnosisAccuracy());
        userDiagnosisVO.setDiagnosisTime(savedUserDiagnosis.getDiagnosisTime());
        userDiagnosisVO.setWrongCount(savedUserDiagnosis.getWrongCount());
        userDiagnosisVO.setTotalCount(savedUserDiagnosis.getTotalCount());
        userDiagnosisVO.setConclusion(savedUserDiagnosis.getConclusion());

        return userDiagnosisVO;
    }
}