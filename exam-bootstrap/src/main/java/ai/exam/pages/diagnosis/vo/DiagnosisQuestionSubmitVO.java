package ai.exam.pages.diagnosis.vo;

import ai.exam.domain.question.UserQuestionDO;
import lombok.Data;

import java.util.List;

@Data
public class DiagnosisQuestionSubmitVO {
    private String certificateCode; // 证书编号
    private String subjectCode; // 科目编号
    // 备考进度 0：首次备考，尚未开始准备、1：首次备考，刚开始准备、2：首次备考，火热复习中、3：二次备考，考过一次未合格、4：已考过两次及已上
    private Integer prepareProgress;
    // 学习情况 0：第一轮 系统化学习中 1：刷题中 知识点巩固 2: 冲刺复习 查漏补缺 3：近3次模拟考 平均分合格、4：复习达标 试通过
    private Integer learningStatus;
    private List<ChapterDiagnosisVO> chapterDiagnosis; // 章节信息
    // 掌握程度 0：尚未掌握 准确率偏低 1：基本掌握 准确率一般 2: 扎实掌握 准确率高
    private Integer masteryLevel;

    private String diagnosisTime; // 答题时长
    private List<UserQuestionDO> answers;
}