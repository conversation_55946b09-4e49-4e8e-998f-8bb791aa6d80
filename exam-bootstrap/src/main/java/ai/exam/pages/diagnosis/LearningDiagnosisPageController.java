package ai.exam.pages.diagnosis;

import ai.exam.pages.diagnosis.vo.DiagnosisQuestionSubmitVO;
import ai.exam.pages.diagnosis.vo.LearningDiagnosisInfoVO;
import ai.exam.pages.diagnosis.vo.LearningDiagnosisSubmitVO;
import ai.exam.pages.diagnosis.vo.UserDiagnosisVO;
import ai.exam.pages.knowledgeslicing.vo.ExamQuestionVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-06-20
 */
@RestController
@RequestMapping("/api/learningdiagnosis")
public class LearningDiagnosisPageController {

    @Resource
    private LearningDiagnosisPageApplication learningDiagnosisPageApplication;

    @GetMapping("/diagnosisInfo")
    public LearningDiagnosisInfoVO getDiagnosisInfo(Long accountId, @RequestParam String certificateCode, @RequestParam String subjectCode) {
        return learningDiagnosisPageApplication.getDiagnosisInfo(accountId, certificateCode, subjectCode);
    }

    @PostMapping("/submitDiagnosisAndGetQuestions")
    public List<ExamQuestionVO> submitDiagnosisAndGetQuestions(HttpServletRequest request, @RequestBody LearningDiagnosisSubmitVO diagnosisInfo) {
        Long accountId = (Long) request.getAttribute("accountId");
        return learningDiagnosisPageApplication.submitDiagnosisAndGetQuestions(accountId, diagnosisInfo);
    }

    @PostMapping("/submitDiagnosisQuestions")
    public UserDiagnosisVO submitDiagnosisQuestions(HttpServletRequest request, @RequestBody DiagnosisQuestionSubmitVO submitDTO) {
        Long accountId = (Long) request.getAttribute("accountId");
        return learningDiagnosisPageApplication.submitDiagnosisQuestions(accountId, submitDTO);
    }
}
