package ai.exam.pages.knowledgeslicing.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ChoiceQuestionVO extends ExamQuestionVO {
    // start 后面正式写得时候这3个字段需要注释掉
    private Long questionId;
    private String questionContent;
    private String questionType;
    // end
    private Map<String, String> options;
    private String rightOption;
}