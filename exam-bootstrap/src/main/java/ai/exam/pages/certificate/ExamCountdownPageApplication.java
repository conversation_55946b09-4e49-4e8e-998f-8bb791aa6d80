package ai.exam.pages.certificate;

import ai.exam.domain.certificate.ExamCountdownDO;
import ai.exam.domain.certificate.ExamCountdownDomain;
import ai.exam.mapper.ExamCountdownVODOMapper;
import ai.exam.web.certificate.vo.ExamCountdownVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ExamCountdownPageApplication {

    @Resource
    private ExamCountdownDomain examCountdownDomain;

    @Resource
    private ExamCountdownVODOMapper examCountdownVODOMapper;

    /**
     * 获取证书考试的倒计时天数
     *
     * @param certificateCode 证书代码
     * @return 倒计时信息
     */
    public ExamCountdownVO getCountdownByCertificateCode(String certificateCode) {
        ExamCountdownDO countdownDaysDO = examCountdownDomain.getCountdownDays(certificateCode);
        return examCountdownVODOMapper.toExamCountdownVO(countdownDaysDO);
    }
}