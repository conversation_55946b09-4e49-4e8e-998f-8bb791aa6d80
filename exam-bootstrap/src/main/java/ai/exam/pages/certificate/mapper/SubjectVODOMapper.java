package ai.exam.pages.certificate.mapper;

import ai.exam.domain.certificate.SubjectDO;
import ai.exam.pages.certificate.vo.SubjectVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface SubjectVODOMapper {
    SubjectVO toSubjectVO(SubjectDO subjectDO);

    List<SubjectVO> toSubjectVOList(List<SubjectDO> subjectDOList);

    SubjectDO toSubjectDO(SubjectVO subjectVO);

    List<SubjectDO> toSubjectDOList(List<SubjectVO> subjectVOList);
}

