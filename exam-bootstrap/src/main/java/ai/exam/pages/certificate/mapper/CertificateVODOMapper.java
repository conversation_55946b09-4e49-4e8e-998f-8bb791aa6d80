package ai.exam.pages.certificate.mapper;

import ai.exam.domain.certificate.CertificateDO;
import ai.exam.pages.certificate.vo.CertificateVO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface CertificateVODOMapper {
    CertificateVO toCertificateVO(CertificateDO certificateDO);

    List<CertificateVO> toCertificateVOList(List<CertificateDO> certificateDOList);

    CertificateDO toCertificateDO(CertificateVO certificateVO);
}
