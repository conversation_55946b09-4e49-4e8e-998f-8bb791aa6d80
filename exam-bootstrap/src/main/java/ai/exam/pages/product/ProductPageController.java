package ai.exam.pages.product;

import ai.exam.web.product.ProductCourseApplication;
import ai.exam.web.product.vo.ProductCourseVO;
import ai.exam.web.product.vo.ProductHomeSelectedCourseListRequest;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/product")
public class ProductPageController {

    @Resource
    private ProductCourseApplication productCourseApplication;

    @GetMapping("/popularCourses")
    public List<ProductCourseVO> homeSelectedCourseList(ProductHomeSelectedCourseListRequest request) {
        return productCourseApplication.getSelectedCourseList(request);
    }
}
