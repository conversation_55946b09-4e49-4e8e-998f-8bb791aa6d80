package ai.exam.pages.mockexam;

import ai.exam.common.enums.ExamModeEnum;
import ai.exam.common.exception.AIException;
import ai.exam.domain.exercise.*;
import ai.exam.domain.question.*;
import ai.exam.domain.question.enums.QuestionSetType;
import ai.exam.domain.question.enums.QuestionType;
import ai.exam.pages.common.vo.BooleanResultVO;
import ai.exam.pages.knowledgeslicing.vo.*;
import ai.exam.pages.mockexam.vo.MockExamInfoVO;
import ai.exam.pages.practice.vo.*;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class MockExamPageApplication {

    @Resource
    private QuestionDomain questionDomain;

    @Resource
    private ExerciseDomain exerciseDomain;

    public List<MockExamInfoVO> getMockExamList(Long accountId, String certificateCode, String subjectCode) {
        // 1. 获取模拟考试题组列表
        List<QuestionSetDO> questionSets = questionDomain.getQuestionSetsBySubjectAndType(subjectCode, QuestionSetType.MOCK_EXAMS);
        if (CollectionUtils.isEmpty(questionSets)) {
            return Lists.newArrayList();
        }
        List<Long> questionSetIds = questionSets.stream().map(QuestionSetDO::getId).collect(Collectors.toList());

        // 2. 获取用户已完成的练习记录
        List<ExerciseDO> exerciseList = exerciseDomain.findByQuestionSetIdsAndMode(accountId, questionSetIds, ExamModeEnum.MOCK_LXMS.getCode());

        // 3. 批量获取所有题集的考试记录
        List<UserExamRecordDO> examRecords = exerciseDomain.findExamRecordsByAccountIdAndQuestionSetIds(accountId, questionSetIds);

        // 4. 计算每个题集的统计信息
        Map<Long, Long> completedCountMap = exerciseList.stream().collect(Collectors.groupingBy(ExerciseDO::getQuestionSetId, Collectors.counting()));

        Map<Long, ExamRecordStatDO> examRecordStatMap = examRecords.stream().collect(Collectors.groupingBy(UserExamRecordDO::getQuestionSetId, Collectors.collectingAndThen(Collectors.toList(), this::calculateExamRecordStat)));

        // 5. 封装返回结果
        return questionSets.stream().map(questionSet -> {
            Long questionSetId = questionSet.getId();
            ExamRecordStatDO stat = examRecordStatMap.getOrDefault(questionSetId, new ExamRecordStatDO());

            MockExamInfoVO vo = new MockExamInfoVO();
            vo.setQuestionSetId(questionSetId);
            vo.setName(questionSet.getName());
            vo.setTotalQuestionCount(questionSet.getQuestionIds().size());
            vo.setCompletedQuestionCount(completedCountMap.getOrDefault(questionSetId, 0L).intValue());
            vo.setExamCount(stat.getExamCount() == null ? 0 : stat.getExamCount());
            vo.setMaxScore(stat.getMaxScore() == null ? "--分" : stat.getMaxScore());
            vo.setIsExam(questionSet.getMockExamsSupport());
            return vo;
        }).collect(Collectors.toList());
    }

    private ExamRecordStatDO calculateExamRecordStat(List<UserExamRecordDO> records) {
        ExamRecordStatDO stat = new ExamRecordStatDO();
        stat.setExamCount(records.size());
        stat.setMaxScore(records.stream().map(UserExamRecordDO::getScore).max(Comparator.naturalOrder()).map(score -> score.toPlainString() + "分").orElse(null));
        return stat;
    }

    public ExamQuestionInfoVO getMockExamQuestions(Long accountId, String subjectCode, Long questionSetId) {
        // 1. 获取题目列表
        List<QuestionDO> allQuestions = questionDomain.getQuestionsByQuestionSetId(questionSetId);

        allQuestions.sort((q1, q2) -> {
            // 首先按题型顺序排序
            int typeCompare = q1.getType().getOrder().compareTo(q2.getType().getOrder());
            // 如果题型相同,则按题目ID排序
            if (typeCompare == 0) {
                return q1.getId().compareTo(q2.getId());
            }
            return typeCompare;
        });

        // 2. 获取用户已完成的练习记录
        List<ExerciseDO> exercises = exerciseDomain.findByQuestionSetIdsAndMode(accountId, Collections.singletonList(questionSetId), ExamModeEnum.MOCK_LXMS.getCode());

        // 3. 获取用户考题收藏记录
        List<QuestionFavoriteRecordDO> questionFavoriteRecords = exerciseDomain.getQuestionFavoriteRecordsBySubjectAndAccount(subjectCode, accountId);

        // 4. 过滤已完成的练习记录
        List<Long> allQuestionIds = allQuestions.stream().map(QuestionDO::getId).collect(Collectors.toList());
        List<ExerciseDO> completedExercises = exercises.stream().filter(exercise -> allQuestionIds.contains(exercise.getQuestionId())).collect(Collectors.toList());

        List<QuestionFavoriteRecordDO> favoriteRecords = questionFavoriteRecords.stream().filter(questionFavorite -> allQuestionIds.contains(questionFavorite.getQuestionId()) && questionFavorite.getIsFavorite()).collect(Collectors.toList());

        // 5. 确定定位的题目ID
        Long locatedQuestionId = determineLocatedQuestionId(accountId, questionSetId, allQuestionIds);

        // 6. 转换为VO
        List<ExamQuestionVO> examQuestionVOList = convertToExamQuestionVOList(allQuestions, locatedQuestionId);
        List<AccountExamQuestionVO> completedQuestionVOList = convertToAccountExamQuestionVOList(completedExercises, allQuestions);
        List<QuestionFavoriteRecordVO> favoriteRecordVOList = convertToQuestionFavoriteRecordVOList(favoriteRecords);

        return new ExamQuestionInfoVO(examQuestionVOList, completedQuestionVOList, favoriteRecordVOList);
    }

    private Long determineLocatedQuestionId(Long accountId, Long questionSetId, List<Long> allQuestionIds) {
        if (allQuestionIds.isEmpty()) {
            return null;
        }
        LocationInformationDO locationInfo = exerciseDomain.getLocationInformationByMode(accountId, ExamModeEnum.MOCK_LXMS.getCode(), null, questionSetId, null);
        if (locationInfo == null || locationInfo.getQuestionId() == null) {
            return allQuestionIds.get(0);
        }
        return allQuestionIds.contains(locationInfo.getQuestionId()) ? locationInfo.getQuestionId() : allQuestionIds.get(0);
    }

    private List<ExamQuestionVO> convertToExamQuestionVOList(List<QuestionDO> questions, Long locatedQuestionId) {
        return questions.stream().map(question -> {
            ExamQuestionVO vo = new ExamQuestionVO();
            vo.setQuestionId(question.getId());
            vo.setQuestionContent(question.getContent());
            vo.setQuestionType(question.getTypeName());
            vo.setOptions(question.getOptions());
            vo.setLocation(question.getId().equals(locatedQuestionId));
            return vo;
        }).collect(Collectors.toList());
    }

    private List<AccountExamQuestionVO> convertToAccountExamQuestionVOList(List<ExerciseDO> exercises, List<QuestionDO> allQuestions) {
        Map<Long, QuestionDO> questionMap = allQuestions.stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));

        return exercises.stream().map(exercise -> {
            AccountExamQuestionVO vo = new AccountExamQuestionVO();
            vo.setQuestionId(exercise.getQuestionId());
            vo.setSelectedOption(exercise.getUserAnswer());
            vo.setIsRight(exercise.getIsRight());

            QuestionDO question = questionMap.get(exercise.getQuestionId());
            if (question != null) {
                vo.setRightAnswer(question.getAnswer());
                vo.setAnalysis(question.getExplanationWithPrefix());
            }
            return vo;
        }).collect(Collectors.toList());
    }

    private List<QuestionFavoriteRecordVO> convertToQuestionFavoriteRecordVOList(List<QuestionFavoriteRecordDO> favoriteRecords) {
        return favoriteRecords.stream().map(favoriteRecord -> {
            QuestionFavoriteRecordVO vo = new QuestionFavoriteRecordVO();
            vo.setQuestionId(favoriteRecord.getQuestionId());
            vo.setIsFavorite(favoriteRecord.getIsFavorite());
            return vo;
        }).collect(Collectors.toList());
    }

    public SubmitAnswerResultVO submitMockExamAnswer(Long accountId, SubmitTestAnswerVO requestVO) {
        String batchNum = requestVO.getBatchNum();
        if (StringUtils.isBlank(batchNum)) {
            throw new AIException(400, "batchNum不能为空");
        }
        Long questionId = requestVO.getQuestionId();

        // 1. 获取题目信息
        List<QuestionDO> questionList = questionDomain.getQuestionsByQuestionIds(Collections.singletonList(questionId));
        if (questionList.isEmpty()) {
            throw new AIException(400, "根据题目id未获取到题目信息");
        }
        QuestionDO question = questionList.get(0);

        // 2. 判断答案是否正确
        String answer = requestVO.getAnswer();
        boolean isRight = question.getAnswer().equals(answer);

        // 3. 存储用户练习记录
        ExerciseDO exerciseDO = new ExerciseDO();
        exerciseDO.setAccountId(accountId);
        exerciseDO.setExerciseMode(ExamModeEnum.MOCK_LXMS.getCode());
        Long questionSetId = requestVO.getQuestionSetId();
        exerciseDO.setQuestionSetId(questionSetId);
        exerciseDO.setSubjectCode(requestVO.getSubjectCode());
        exerciseDO.setQuestionId(questionId);
        exerciseDO.setUserAnswer(answer);
        exerciseDO.setIsRight(isRight);
        exerciseDO.setBatchNum(batchNum);

        exerciseDomain.savePracticeExercise(exerciseDO);

        // 4. 如果答案错误则记录用户错题集
        if (!isRight) {
            WrongQuestionRecordDO recordDO = new WrongQuestionRecordDO();
            recordDO.setQuestionSetId(questionSetId);
            recordDO.setExerciseMode(ExamModeEnum.MOCK_LXMS.getCode());
            recordDO.setQuestionId(questionId);
            recordDO.setUserAnswer(answer);
            recordDO.setAccountId(accountId);
            recordDO.setSubjectCode(requestVO.getSubjectCode());
            recordDO.setCollectedAt(LocalDateTime.now());
            exerciseDomain.saveWrongQuestionRecord(recordDO);
        }

        return new SubmitAnswerResultVO(batchNum, isRight, question.getExplanationWithPrefix(), question.getAnswer());
    }

    public ExamPracticeInfoVO getMockExamPracticeInfo(Long accountId, String batchNum, Long questionSetId) {
        // 1. 获取当前用户当前批次号的练习记录列表
        List<ExerciseDO> exerciseList = exerciseDomain.findByQuestionSetIdAndModeAndBatchOrderByUpdateTimeDesc(accountId, questionSetId, ExamModeEnum.MOCK_LXMS.getCode(), batchNum);

        if (exerciseList.isEmpty()) {
            return new ExamPracticeInfoVO(false, "-", "00:00:00", 0, 0);
        }

        // 2. 计算练习时间
        BatchNumRecordDO batchNumRecord = exerciseDomain.getBatchNumRecord(accountId, batchNum);
        if (batchNumRecord == null) {
            throw new AIException(400, "根据batchNum未获取到对应的练习开始时间记录");
        }
        LocalDateTime startTime = batchNumRecord.getExerciseStartTime();
        LocalDateTime endTime = exerciseList.get(0).getUpdateTime();
        Duration duration = Duration.between(startTime, endTime);
        String practiceTime = String.format("%02d:%02d:%02d", duration.toHours(), duration.toMinutesPart(), duration.toSecondsPart());

        // 3. 计算正确率和错题数
        long totalQuestionCount = exerciseList.size();
        long rightQuestionCount = exerciseList.stream().filter(ExerciseDO::getIsRight).count();
        String accuracy = String.format("%.0f%%", rightQuestionCount * 100.0 / totalQuestionCount);
        long wrongQuestionCount = totalQuestionCount - rightQuestionCount;

        return new ExamPracticeInfoVO(true, accuracy, practiceTime, (int) wrongQuestionCount, (int) totalQuestionCount);
    }

    public List<WrongQuestionVO> getMockExamWrongQuestions(Long accountId, String batchNum, Long questionSetId) {
        // 1. 获取错误的练习记录
        List<ExerciseDO> wrongExercises = exerciseDomain.findWrongExercisesByBatch(accountId, questionSetId, ExamModeEnum.MOCK_LXMS.getCode(), batchNum);

        // 2. 获取题目信息
        List<Long> questionIds = wrongExercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toList());
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        // 3. 将题目信息转换为Map，以便快速查找
        Map<Long, QuestionDO> questionMap = questions.stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));

        // 4. 组装返回结果
        List<WrongQuestionVO> collect = wrongExercises.stream().map(exercise -> {
            WrongQuestionVO vo = new WrongQuestionVO();
            vo.setQuestionId(exercise.getQuestionId());
            vo.setUserAnswer(exercise.getUserAnswer());

            QuestionDO question = questionMap.get(exercise.getQuestionId());
            if (question != null) {
                vo.setQuestionContent(question.getContent());
                vo.setQuestionType(question.getTypeName());
                vo.setOptions(question.getOptions());
                vo.setRightAnswer(question.getAnswer());
                vo.setAnalysis(question.getExplanationWithPrefix());
            }

            return vo;
        }).collect(Collectors.toList());

        collect.sort((q1, q2) -> {
            // 首先按题型顺序排序
            int typeCompare = QuestionType.getInstance(q1.getQuestionType()).getOrder().compareTo(QuestionType.getInstance(q2.getQuestionType()).getOrder());
            // 如果题型相同,则按题目ID排序
            if (typeCompare == 0) {
                return q1.getQuestionId().compareTo(q2.getQuestionId());
            }
            return typeCompare;
        });
        return collect;
    }

    //    @Transactional
//    public SubmitExamAnswerResultVO submitMockExam(Long accountId, SubmitExamAnswerRequestVO submitVO) {
//        List<Long> questionIds = submitVO.getAnswers().stream().map(ExamAnswerVO::getQuestionId).collect(Collectors.toList());
//
//        // 1. 获取题目信息
//        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);
//        Map<Long, QuestionDO> questionMap = questions.stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));
//
//        BigDecimal totalScore = BigDecimal.ZERO;
//        BigDecimal score = BigDecimal.ZERO;
//        List<ExerciseDO> exercises = new ArrayList<>();
//        List<ExerciseDO> wrongExercises = new ArrayList<>();
//
//        // 2. 判断答案并计算分数
//        for (ExamAnswerVO answer : submitVO.getAnswers()) {
//            QuestionDO question = questionMap.get(answer.getQuestionId());
//            if (question != null) {
//                boolean isRight = question.getAnswer().equals(answer.getAnswer());
//                ExerciseDO exercise = new ExerciseDO();
//                exercise.setAccountId(accountId);
//                exercise.setSubjectCode(submitVO.getSubjectCode());
//                exercise.setExerciseMode(ExamModeEnum.MOCK_EXAMS.getCode());
//                exercise.setQuestionSetId(submitVO.getQuestionSetId());
//                exercise.setQuestionId(answer.getQuestionId());
//                exercise.setUserAnswer(answer.getAnswer());
//                exercise.setIsRight(isRight);
//                exercise.setDelFlag(0);
//                exercises.add(exercise);
//
//                totalScore = totalScore.add(new BigDecimal(question.getScore()));
//                if (isRight) {
//                    score = score.add(new BigDecimal(question.getScore()));
//                } else if (StringUtils.isNotBlank(answer.getAnswer())) {
//                    ExerciseDO wrontExercise = new ExerciseDO();
//                    wrontExercise.setAccountId(accountId);
//                    wrontExercise.setSubjectCode(submitVO.getSubjectCode());
//                    wrontExercise.setExerciseMode(ExamModeEnum.MOCK_EXAMS.getCode());
//                    wrontExercise.setQuestionSetId(submitVO.getQuestionSetId());
//                    wrontExercise.setQuestionId(answer.getQuestionId());
//                    wrontExercise.setUserAnswer(answer.getAnswer());
//                    wrontExercise.setIsRight(false);
//                    wrontExercise.setDelFlag(0);
//                    wrongExercises.add(wrontExercise);
//                }
//            }
//        }
//
//        // 4. 生成考试记录
//        UserExamRecordDO userExamRecord = new UserExamRecordDO();
//        // 定义时间格式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        userExamRecord.setExamStartTime(LocalDateTime.parse(submitVO.getCreateTime(), formatter));
//        userExamRecord.setExamEndTime(LocalDateTime.now());
//        userExamRecord.setAccountId(accountId);
//        userExamRecord.setQuestionSetId(submitVO.getQuestionSetId());
//        userExamRecord.setTotalScore(totalScore);
//        userExamRecord.setScore(score);
//        userExamRecord.setWrongCount((int) exercises.stream().filter(e -> !e.getIsRight()).count());
//        userExamRecord.setTotalCount(questions.size());
//        userExamRecord.setExerciseMode(ExamModeEnum.MOCK_EXAMS.getCode());
//        UserExamRecordDO savedRecord = exerciseDomain.generateExamRecord(userExamRecord, wrongExercises, exercises);
//
//        // 5. 获取最高分和考试次数
//        ExamRecordStatDO examRecordStat = exerciseDomain.getExamRecordStat(accountId, submitVO.getQuestionSetId());
//
//        // 6. 封装返回结果
//        SubmitExamAnswerResultVO resultVO = new SubmitExamAnswerResultVO();
//        resultVO.setExamRecordId(savedRecord.getId());
//        resultVO.setScore(score.toPlainString() + "分");
//        resultVO.setWrongQuestionCount(savedRecord.getWrongCount());
//        resultVO.setMaxScore(examRecordStat.getMaxScore());
//        resultVO.setExamCount(examRecordStat.getExamCount());
//
//        // 实现错题分布统计
//        resultVO.setWrongQuestionDistributionVOS(new ArrayList<>());
//        if (exercises.size() > 0) {
//            List<Long> wrongQuestionIds = exercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toList());
//            List<SectionQuestionsDO> sectionsByQuestionDOS = questionDomain.getSectionsByQuestionIds(wrongQuestionIds);
//            List<WrongQuestionDistributionVO> wrongQuestionDistributionVOS = sectionsByQuestionDOS.stream().map(sectionsByQuestionDO -> {
//                WrongQuestionDistributionVO distributionVO = new WrongQuestionDistributionVO();
//                distributionVO.setSectionCode(sectionsByQuestionDO.getSectionCode());
//                distributionVO.setSectionName(sectionsByQuestionDO.getSectionName());
//                distributionVO.setCount(sectionsByQuestionDO.getQuestionIds().size());
//                return distributionVO;
//            }).collect(Collectors.toList());
//            resultVO.setWrongQuestionDistributionVOS(wrongQuestionDistributionVOS);
//        }
//        return resultVO;
//    }
    @Transactional
    public SubmitExamAnswerResultVO submitMockExam(Long accountId, SubmitExamAnswerRequestVO submitVO) {
        List<Long> questionIds = submitVO.getAnswers().stream().map(ExamAnswerVO::getQuestionId).collect(Collectors.toList());

        // 获取试卷判分规则
        QuestionSetDO questionSetDO = questionDomain.getQuestionSetByQuestionSetId(submitVO.getQuestionSetId());
        QuestionMockExamsScopeDO mockExamsScope = questionSetDO.getMockExamsScope();
        List<ExamsScopeScopeRuleDO> scopeRules = mockExamsScope.getScopeRules();

        // 1. 获取所有题目信息
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);
        Map<Long, QuestionDO> questionMap = questions.stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));

        // 2. 按题型分组用户答案
        Map<String, List<ExamAnswerVO>> answersByType = submitVO.getAnswers().stream().collect(Collectors.groupingBy(answer -> questionMap.get(answer.getQuestionId()).getType().getName()));

        // 3. 计算得分
        BigDecimal totalScore = BigDecimal.ZERO;
        List<ExerciseDO> allExercises = new ArrayList<>();
        List<ExerciseDO> wrongExercises = new ArrayList<>();

        for (ExamsScopeScopeRuleDO rule : scopeRules) {
            String questionType = rule.getQuestionType();
            List<ExamAnswerVO> typeAnswers = answersByType.getOrDefault(questionType, Collections.emptyList()).stream().sorted(Comparator.comparing(answer -> StringUtils.isBlank(answer.getAnswer()))).collect(Collectors.toList());

            // 如果答题数超过限制,只取前N题
            Integer requiredAnswerCount = rule.getRequiredAnswerCount();
            if (requiredAnswerCount == null) {
                requiredAnswerCount = rule.getQuestionCount();
            }

            List<ExamAnswerVO> noScoreByTypeAnswer = new ArrayList<>();
            if (typeAnswers.size() > requiredAnswerCount) {
                noScoreByTypeAnswer = typeAnswers.subList(requiredAnswerCount, typeAnswers.size());
                typeAnswers = typeAnswers.subList(0, requiredAnswerCount);
            }

            // 计算该题型得分
            for (ExamAnswerVO answer : typeAnswers) {
                QuestionDO question = questionMap.get(answer.getQuestionId());
                BigDecimal questionScore = calculateQuestionScore(question, answer.getAnswer(), rule);
                totalScore = totalScore.add(questionScore);

                // 记录答题信息
                boolean isRight = question.getAnswer().equals(answer.getAnswer());
                ExerciseDO exercise = createExerciseRecord(accountId, submitVO, question, answer.getAnswer(), isRight);
                allExercises.add(exercise);

                // 如果答错或答案为空,加入错题集
                if (!isRight) {
                    wrongExercises.add(exercise);
                }
            }
            // 不计分题，仅插入答题记录
            for (ExamAnswerVO answer : noScoreByTypeAnswer) {
                QuestionDO question = questionMap.get(answer.getQuestionId());
                // 记录答题信息
                boolean isRight = question.getAnswer().equals(answer.getAnswer());
                ExerciseDO exercise = createExerciseRecord(accountId, submitVO, question, answer.getAnswer(), isRight);
                allExercises.add(exercise);
                // 如果答错或答案为空,加入错题集
                if (!isRight) {
                    wrongExercises.add(exercise);
                }
            }
        }

        // 4. 生成考试记录
        UserExamRecordDO userExamRecord = createExamRecord(accountId, submitVO, totalScore, questions.size(), wrongExercises.size(), mockExamsScope);

        // 5. 保存考试记录和练习记录
        UserExamRecordDO savedRecord = exerciseDomain.generateExamRecord(userExamRecord, wrongExercises, allExercises);

        // 6. 获取考试统计信息
        ExamRecordStatDO examRecordStat = exerciseDomain.getExamRecordStat(accountId, submitVO.getQuestionSetId());

        // 7. 生成错题分布统计
        List<WrongQuestionDistributionVO> distributionVOS = Collections.emptyList();
        if (!wrongExercises.isEmpty()) {
            distributionVOS = generateWrongQuestionDistribution(wrongExercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toList()));
        }

        // 8. 返回结果
        return new SubmitExamAnswerResultVO(savedRecord.getId(), totalScore.toPlainString() + "分", wrongExercises.size(), examRecordStat.getMaxScore(), examRecordStat.getExamCount(), distributionVOS);
    }

    /**
     * 计算单道题目得分
     */
    private BigDecimal calculateQuestionScore(QuestionDO question, String userAnswer, ExamsScopeScopeRuleDO rule) {
        if (StringUtils.isBlank(userAnswer)) {
            return BigDecimal.ZERO;
        }

        String questionType = question.getType().getName();
        String correctAnswer = question.getAnswer();

        // 单选题和判断题
        if ("CHOICE".equals(questionType) || "TRUE_FALSE".equals(questionType)) {
            return correctAnswer.equals(userAnswer) ? rule.getFullScore() : BigDecimal.ZERO;
        }

        // 多选题和不定项选择题
        if ("CHOICES".equals(questionType) || "INDEFINITE_CHOICE".equals(questionType)) {
            // 完全正确
            if (correctAnswer.equals(userAnswer)) {
                return rule.getFullScore();
            }

            // 部分正确
            if (rule.getPartialScore() != null) {
                // 处理答案格式：将逗号分隔的答案转换为无逗号格式
                String processedUserAnswer = userAnswer.replace(",", "");
                String processedCorrectAnswer = correctAnswer.replace(",", "");

                Set<Character> correctOptions = processedCorrectAnswer.chars().mapToObj(c -> (char) c).collect(Collectors.toSet());
                Set<Character> userOptions = processedUserAnswer.chars().mapToObj(c -> (char) c).collect(Collectors.toSet());

                // 计算正确选项数
                long correctCount = userOptions.stream().filter(correctOptions::contains).count();

                // 如果有错误选项,不得分
                if (userOptions.stream().anyMatch(o -> !correctOptions.contains(o))) {
                    return BigDecimal.ZERO;
                }

                // 按正确选项数计算部分得分
                BigDecimal partialScore = rule.getPartialScore().multiply(BigDecimal.valueOf(correctCount));
                return partialScore.min(rule.getFullScore()); // 不超过满分
            }
        }

        return BigDecimal.ZERO;
    }

    /**
     * 创建练习记录
     */
    private ExerciseDO createExerciseRecord(Long accountId, SubmitExamAnswerRequestVO submitVO, QuestionDO question, String userAnswer, boolean isRight) {
        ExerciseDO exercise = new ExerciseDO();
        exercise.setAccountId(accountId);
        exercise.setSubjectCode(submitVO.getSubjectCode());
        exercise.setExerciseMode(ExamModeEnum.MOCK_EXAMS.getCode());
        exercise.setQuestionSetId(submitVO.getQuestionSetId());
        exercise.setQuestionId(question.getId());
        exercise.setUserAnswer(userAnswer);
        exercise.setIsRight(isRight);
        exercise.setDelFlag(0);
        return exercise;
    }

    /**
     * 创建考试记录
     */
    private UserExamRecordDO createExamRecord(Long accountId, SubmitExamAnswerRequestVO submitVO, BigDecimal score, int totalCount, int wrongCount, QuestionMockExamsScopeDO mockExamsScope) {
        LocalDateTime now = LocalDateTime.now();
        UserExamRecordDO record = new UserExamRecordDO();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 最早时间
        LocalDateTime earlyTime = now.minusMinutes(mockExamsScope.getDuration());
        LocalDateTime createTime = LocalDateTime.parse(submitVO.getCreateTime(), formatter);
        record.setExamStartTime(createTime.isAfter(earlyTime) ? createTime : earlyTime);
        record.setExamEndTime(now);
        record.setAccountId(accountId);
        record.setQuestionSetId(submitVO.getQuestionSetId());
        record.setTotalScore(BigDecimal.valueOf(mockExamsScope.getTotalScore()));
        record.setScore(score);
        record.setWrongCount(wrongCount);
        record.setTotalCount(totalCount);
        record.setExerciseMode(ExamModeEnum.MOCK_EXAMS.getCode());
        return record;
    }

    /**
     * 生成错题分布统计
     */
    private List<WrongQuestionDistributionVO> generateWrongQuestionDistribution(List<Long> wrongQuestionIds) {
        List<SectionQuestionsDO> sectionsByQuestionDOS = questionDomain.getSectionsByQuestionIds(wrongQuestionIds);
        return sectionsByQuestionDOS.stream().map(sectionsByQuestionDO -> {
            WrongQuestionDistributionVO distributionVO = new WrongQuestionDistributionVO();
            distributionVO.setSectionCode(sectionsByQuestionDO.getSectionCode());
            distributionVO.setSectionName(sectionsByQuestionDO.getSectionName());
            distributionVO.setCount(sectionsByQuestionDO.getQuestionIds().size());
            return distributionVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取考试模式的题目信息
     *
     * @param questionSetId 题集ID
     * @return 考试模式题目列表
     */
    public List<ExamModeQuestionVO> getExamModeQuestions(Long questionSetId) {
        // 1. 根据题集id获取题目列表
        List<QuestionDO> allQuestions = questionDomain.getQuestionsByQuestionSetId(questionSetId);

        allQuestions.sort((q1, q2) -> {
            // 首先按题型顺序排序
            int typeCompare = q1.getType().getOrder().compareTo(q2.getType().getOrder());
            // 如果题型相同,则按题目ID排序
            if (typeCompare == 0) {
                return q1.getId().compareTo(q2.getId());
            }
            return typeCompare;
        });

        // 2. 将题目信息转换为VO
        return allQuestions.stream().map(question -> {
            ExamModeQuestionVO vo = new ExamModeQuestionVO();
            vo.setQuestionId(question.getId());
            vo.setQuestionContent(question.getContent());
            vo.setQuestionType(question.getTypeName());
            vo.setOptions(question.getOptions());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取指定考试记录和节的错题信息
     *
     * @param examRecordId 考试记录ID
     * @param sectionCode  节编号
     * @param subjectCode  科目编号
     * @return 错题列表
     */
    public List<WrongQuestionVO> getSectionWrongQuestions(Long accountId, Long examRecordId, String sectionCode, String subjectCode) {
        // 1. 获取该考试记录的所有错题
        List<ExerciseDO> wrongExercises = exerciseDomain.findWrongExercisesByExamRecordId(accountId, examRecordId, ExamModeEnum.MOCK_EXAMS.getCode());

        if (CollectionUtils.isEmpty(wrongExercises)) {
            return Collections.emptyList();
        }

        // 2. 获取这些错题对应的题目ID
        List<Long> wrongQuestionIds = wrongExercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toList());

        // 3. 获取这些题目ID对应的节信息
        List<SectionQuestionsDO> sectionQuestions = questionDomain.getSectionsByQuestionIds(wrongQuestionIds);

        // 4. 筛选出指定节的题目ID
        Optional<SectionQuestionsDO> targetSection = sectionQuestions.stream().filter(sq -> sq.getSectionCode().equals(sectionCode)).findFirst();

        if (!targetSection.isPresent() || CollectionUtils.isEmpty(targetSection.get().getQuestionIds())) {
            return Collections.emptyList();
        }

        List<Long> sectionQuestionIds = targetSection.get().getQuestionIds();

        // 5. 获取这些题目的详细信息
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(sectionQuestionIds);

        // 6. 构建错题VO列表
        Map<Long, QuestionDO> questionMap = questions.stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));

        return wrongExercises.stream().filter(exercise -> sectionQuestionIds.contains(exercise.getQuestionId())).map(exercise -> {
            WrongQuestionVO vo = new WrongQuestionVO();
            vo.setQuestionId(exercise.getQuestionId());
            vo.setUserAnswer(exercise.getUserAnswer());

            QuestionDO question = questionMap.get(exercise.getQuestionId());
            if (question != null) {
                vo.setQuestionContent(question.getContent());
                vo.setQuestionType(question.getTypeName());
                vo.setOptions(question.getOptions());
                vo.setRightAnswer(question.getAnswer());
                vo.setAnalysis(question.getExplanationWithPrefix());
            }

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取最近一次考试记录
     *
     * @param accountId     账号ID
     * @param questionSetId 题集ID
     * @param subjectCode   科目编号
     * @return 考试结果信息
     */
    public SubmitExamAnswerResultVO getLatelyExamRecord(Long accountId, Long questionSetId, String subjectCode) {
        SubmitExamAnswerResultVO result = new SubmitExamAnswerResultVO();

        // 1. 获取最近一次考试记录
        UserExamRecordDO latestRecord = exerciseDomain.findExamRecordByAccountIdAndQuestionSetIdLately(accountId, questionSetId);
        if (latestRecord == null) {
            return result;
        }

        // 2. 获取考试统计信息
        ExamRecordStatDO examRecordStat = exerciseDomain.getExamRecordStat(accountId, questionSetId);

        // 3. 填充结果信息
        result.setExamRecordId(latestRecord.getId());
        result.setScore(latestRecord.getScore().toPlainString() + "分");
        result.setWrongQuestionCount(latestRecord.getWrongCount());
        result.setMaxScore(examRecordStat.getMaxScore());
        result.setExamCount(examRecordStat.getExamCount());

        // 4. 如果有错题，获取错题分布
        if (latestRecord.getWrongCount() > 0) {
            List<ExerciseDO> wrongExercises = exerciseDomain.findWrongExercisesByExamRecordId(accountId, latestRecord.getId(), ExamModeEnum.MOCK_EXAMS.getCode());
            List<Long> wrongQuestionIds = wrongExercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toList());

            List<SectionQuestionsDO> sectionQuestions = questionDomain.getSectionsByQuestionIds(wrongQuestionIds);
            List<WrongQuestionDistributionVO> distribution = sectionQuestions.stream().map(sq -> {
                WrongQuestionDistributionVO vo = new WrongQuestionDistributionVO();
                vo.setSectionCode(sq.getSectionCode());
                vo.setSectionName(sq.getSectionName());
                vo.setCount(sq.getQuestionIds().size());
                return vo;
            }).collect(Collectors.toList());

            result.setWrongQuestionDistributionVOS(distribution);
        } else {
            result.setWrongQuestionDistributionVOS(Collections.emptyList());
        }

        return result;
    }

    /**
     * 获取指定考试记录的所有错题
     *
     * @param examRecordId 考试记录ID
     * @return 错题列表
     */
    public List<WrongQuestionVO> getExamWrongQuestions(Long accountId, Long examRecordId) {
        // 1. 获取错误的考试记录
        List<ExerciseDO> wrongExercises = exerciseDomain.findWrongExercisesByExamRecordId(accountId, examRecordId, ExamModeEnum.MOCK_EXAMS.getCode());

        if (CollectionUtils.isEmpty(wrongExercises)) {
            return Collections.emptyList();
        }

        // 2. 获取题目信息
        List<Long> questionIds = wrongExercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toList());
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        // 3. 将题目信息转换为Map，以便快速查找
        Map<Long, QuestionDO> questionMap = questions.stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));

        // 4. 构建错题VO列表
        List<WrongQuestionVO> collect = wrongExercises.stream().map(exercise -> {
            WrongQuestionVO vo = new WrongQuestionVO();
            vo.setQuestionId(exercise.getQuestionId());
            vo.setUserAnswer(exercise.getUserAnswer());
            vo.setQuestionSetId(exercise.getQuestionSetId());
            QuestionDO question = questionMap.get(exercise.getQuestionId());
            if (question != null) {
                vo.setQuestionContent(question.getContent());
                vo.setQuestionType(question.getTypeName());
                vo.setOptions(question.getOptions());
                vo.setRightAnswer(question.getAnswer());
                vo.setAnalysis(question.getExplanationWithPrefix());
            }

            return vo;
        }).collect(Collectors.toList());

        collect.sort((q1, q2) -> {
            // 首先按题型顺序排序
            int typeCompare = QuestionType.getInstance(q1.getQuestionType()).getOrder().compareTo(QuestionType.getInstance(q2.getQuestionType()).getOrder());
            // 如果题型相同,则按题目ID排序
            if (typeCompare == 0) {
                return q1.getQuestionId().compareTo(q2.getQuestionId());
            }
            return typeCompare;
        });

        return collect;
    }

    public SubmitExamAnswerResultVO getExamRecordById(Long accountId, Long examRecordId) {
        SubmitExamAnswerResultVO examAnswerResultDTO = new SubmitExamAnswerResultVO();
        UserExamRecordDO userExamRecordDO = exerciseDomain.getExamRecordById(examRecordId);
        if (userExamRecordDO == null) {
            return examAnswerResultDTO;
        }
        // 获取最高分和考试次数
        ExamRecordStatDO examRecordStat = exerciseDomain.getExamRecordStat(accountId, userExamRecordDO.getQuestionSetId());

        // 6. 返回用户诊断记录封装成UserDiagnosisDTO信息返回
        examAnswerResultDTO.setExamRecordId(userExamRecordDO.getId());
        examAnswerResultDTO.setScore(userExamRecordDO.getScore().toPlainString() + "分");
        examAnswerResultDTO.setWrongQuestionCount(userExamRecordDO.getWrongCount());
        examAnswerResultDTO.setMaxScore(examRecordStat.getMaxScore());
        examAnswerResultDTO.setExamCount(examRecordStat.getExamCount());

        examAnswerResultDTO.setWrongQuestionDistributionVOS(Collections.emptyList());
        if (userExamRecordDO.getWrongCount() > 0) {
            List<ExerciseDO> wrongExercises = exerciseDomain.findWrongExercisesByExamRecordId(accountId, userExamRecordDO.getId(), ExamModeEnum.MOCK_EXAMS.getCode());

            List<Long> wrongQuestionIds = wrongExercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toList());
            List<SectionQuestionsDO> sectionsByQuestionDOS = questionDomain.getSectionsByQuestionIds(wrongQuestionIds);
            List<WrongQuestionDistributionVO> wrongQuestionDistributionVOS = sectionsByQuestionDOS.stream().map(sectionsByQuestionDO -> {
                WrongQuestionDistributionVO distributionVO = new WrongQuestionDistributionVO();
                distributionVO.setSectionCode(sectionsByQuestionDO.getSectionCode());
                distributionVO.setSectionName(sectionsByQuestionDO.getSectionName());
                distributionVO.setCount(sectionsByQuestionDO.getQuestionIds().size());
                return distributionVO;
            }).collect(Collectors.toList());
            examAnswerResultDTO.setWrongQuestionDistributionVOS(wrongQuestionDistributionVOS);
        }
        return examAnswerResultDTO;
    }
}
