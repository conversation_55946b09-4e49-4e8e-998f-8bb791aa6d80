package ai.exam.pages.advice;

import ai.exam.common.enums.ExamModeEnum;
import ai.exam.domain.account.AccountDO;
import ai.exam.domain.account.AccountDomain;
import ai.exam.domain.exercise.ExerciseDO;
import ai.exam.domain.exercise.ExerciseDomain;
import ai.exam.domain.exercise.UserExamRecordDO;
import ai.exam.domain.knowledge.ChapterDO;
import ai.exam.domain.knowledge.KnowledgeDomain;
import ai.exam.domain.knowledge.QuestionSumDO;
import ai.exam.domain.knowledge.SectionDO;
import ai.exam.domain.question.QuestionDomain;
import ai.exam.domain.question.QuestionSetDO;
import ai.exam.domain.question.enums.QuestionSetType;
import ai.exam.pages.advice.enums.GraspLevelEnum;
import ai.exam.pages.advice.enums.PassRateTypeEnum;
import ai.exam.pages.advice.enums.WrongRateTypeEnum;
import ai.exam.pages.advice.vo.*;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class AdviceExamPageApplication {


    @Resource
    private KnowledgeDomain knowledgeDomain;

    @Resource
    private ExerciseDomain exerciseDomain;

    @Resource
    private QuestionDomain questionDomain;

    @Resource
    private AccountDomain accountDomain;

    /**
     * -------------------------
     * 备考建议：
     * 注：如果这个章-节没有真题，就不显示该章-节。
     * <p>
     * 1，通过率：
     * 章节真题正确题目数 / 章节真题总题目数  》= 70% 为较高（绿色） ， 50% - 70% = 一般 （黄色） ，  《 50% 偏低（红色）
     * <p>
     * 2，雷达图：
     * 每个章节真题的正确题目数 / 这个章节真题总题目数  * 100 ，即为章节雷达图数据。
     * <p>
     * 4，章 - 节， 菜单显示；
     * 3，章节掌握程度：
     * 章节真题正确率 》= 80% ，较高。
     * 80% 》 章节真题正确率 >= 60% , 一般。
     * 章节真题正确率 《 60% ， 较差。
     * <p>
     * 5，练题情况分析：
     * 类型        总题目数   已做题目数   做题率   错题率   错误率   说明（根据错误率显示不同文案）
     * 章节真题
     * 历年真题
     * 模拟考试
     * <p>
     * 6，错题分析：
     * 注：如果对应章节没有错题，则对应章-节不显示。
     * 6.1，错误率： 错题数/已做题目数
     * 6.2，章 - 节， 菜单显示。
     */
    public AdviceExamVO getAdviceExam(Long accountId, String subjectCode) {
        AdviceExamVO adviceExamVO = new AdviceExamVO();

        // 1. 获取章节信息和练习记录
        List<ChapterDO> chapters = knowledgeDomain.findChaptersBySubjectCode(subjectCode, true);
        // 返回的信息：每章，每节都要算一下
        // 每章题目总数，查询这个人在章节练习中，每章的正确题目数（题目重复以最后一个更新为主），错误题目数，已做的题目数
        // 错题分析中，查询这个人在章节练习中，有没有错题，和已做的题目
        // 设置章节数据
        int total = processChapters(chapters, accountId);

        // 2，设置昵称
        Optional<AccountDO> accountDO = accountDomain.getById(accountId);
        adviceExamVO.setNickName(accountDO.map(AccountDO::getNickname).orElse(null));

        // 3. 设置雷达图数据
        adviceExamVO.setOnlyChart(chapters.isEmpty());
        adviceExamVO.setChartCount(chapters.size());
        List<ChartRadarVO> radarList = calculateRadarData(chapters);
        adviceExamVO.setChartRadarList(radarList);

        // 4. 计算科目整体通过率
        double subjectPassRate = calculateSubjectPassRate(chapters, total);
        setSubjectPassRateInfo(adviceExamVO, subjectPassRate);

        // 5. 计算掌握程度不高的章节
        List<NoHighChartVO> noHighCharts = calculateNoHighCharts(chapters);
        adviceExamVO.setNoHighChartVOList(noHighCharts);

        // 6. 错题分析
        Double wrongRate = calculateOverallWrongRate(chapters);
        adviceExamVO.setWrongRate(wrongRate);
        List<ChartWrongRateInfoVO> wrongRateInfo = calculateWrongRateInfo(chapters);
        adviceExamVO.setChartWrongRateInfoVOList(wrongRateInfo);

        // 7. 确定错题分析类型
        adviceExamVO.setErrorAnalysisType(determineErrorAnalysisType(wrongRate));

        // 8. 设置练题情况分析
        List<AnswerQuestionVO> answerQuestionVOs = calculateAnswerQuestionAnalysis(chapters, accountId, subjectCode);
        adviceExamVO.setAnswerQuestionVO(answerQuestionVOs);

        return adviceExamVO;
    }

    private void setSubjectPassRateInfo(AdviceExamVO adviceExamVO, double subjectPassRate) {
        adviceExamVO.setSubjectPassRate(subjectPassRate);
        PassRateTypeEnum passRateType = PassRateTypeEnum.getByRate(subjectPassRate);
        adviceExamVO.setSubjectPassRateType(passRateType.getCode());
        adviceExamVO.setSubjectPassRateDesc(passRateType.getDescription());
    }

    private int processChapters(List<ChapterDO> chapters, Long accountId) {
        int total = 0;
        for (ChapterDO chapter : chapters) {
            chapter.setQuestionSumDO(new QuestionSumDO());
            List<String> sectionCodes = processSections(chapter, total);
            processSectionExercises(chapter, accountId, sectionCodes);
            summarizeChapterQuestionSum(chapter);
        }
        return total;
    }

    private List<String> processSections(ChapterDO chapter, int total) {
        List<String> sectionCodes = new ArrayList<>();
        for (SectionDO section : chapter.getSections()) {
            sectionCodes.add(section.getSectionCode());
            section.setQuestionSumDO(new QuestionSumDO());
            int num = Optional.ofNullable(section.getQuestionIds()).orElse(Lists.newArrayList()).size();
            section.getQuestionSumDO().setQuestionTotal(num);
            total += num;
        }
        return sectionCodes;
    }

    private void processSectionExercises(ChapterDO chapter, Long accountId, List<String> sectionCodes) {
        List<ExerciseDO> sectionsAndMode = exerciseDomain.findBySectionsAndMode(accountId, sectionCodes, ExamModeEnum.EXAM.getCode());
        // 如果同一题做了多次，保留最新的一条
        sectionsAndMode = new ArrayList<>(sectionsAndMode.stream().collect(Collectors.toMap(exercise -> exercise.getSectionCode() + "_" + exercise.getQuestionId(), Function.identity(), (existing, replacement) -> existing.getUpdateTime().isAfter(replacement.getUpdateTime()) ? existing : replacement)).values());

        Map<String, QuestionSumDO> sectionQuestionSumMap = sectionsAndMode.stream().collect(Collectors.groupingBy(ExerciseDO::getSectionCode, Collectors.collectingAndThen(Collectors.toList(), this::calculateQuestionSum)));

        for (SectionDO section : chapter.getSections()) {
            QuestionSumDO questionSum = sectionQuestionSumMap.getOrDefault(section.getSectionCode(), new QuestionSumDO());
            questionSum.setQuestionTotal(section.getQuestionSumDO().getQuestionTotal());
            section.setQuestionSumDO(questionSum);
        }
    }

    /**
     * 根据答题记录，封装题目总数
     *
     * @param exercises
     * @return
     */
    private QuestionSumDO calculateQuestionSum(List<ExerciseDO> exercises) {
        QuestionSumDO questionSum = new QuestionSumDO();
        questionSum.setQuestionUserAnswerTotal(exercises.size());
        questionSum.setQuestionUserRightTotal((int) exercises.stream().filter(ExerciseDO::getIsRight).count());
        questionSum.setQuestionUserWrongTotal(questionSum.getQuestionUserAnswerTotal() - questionSum.getQuestionUserRightTotal());
        return questionSum;
    }

    /**
     * 汇总节里面的答题总数，到章中
     *
     * @param chapterDO
     */
    private void summarizeChapterQuestionSum(ChapterDO chapterDO) {
        QuestionSumDO chapterSum = Optional.ofNullable(chapterDO.getSections()).orElse(Collections.emptyList()).stream().map(section -> Optional.ofNullable(section.getQuestionSumDO()).orElse(new QuestionSumDO())).reduce(new QuestionSumDO(), (sum1, sum2) -> {
            sum1.setQuestionTotal(Optional.ofNullable(sum1.getQuestionTotal()).orElse(0) + Optional.ofNullable(sum2.getQuestionTotal()).orElse(0));
            sum1.setQuestionUserAnswerTotal(Optional.ofNullable(sum1.getQuestionUserAnswerTotal()).orElse(0) + Optional.ofNullable(sum2.getQuestionUserAnswerTotal()).orElse(0));
            sum1.setQuestionUserWrongTotal(Optional.ofNullable(sum1.getQuestionUserWrongTotal()).orElse(0) + Optional.ofNullable(sum2.getQuestionUserWrongTotal()).orElse(0));
            sum1.setQuestionUserRightTotal(Optional.ofNullable(sum1.getQuestionUserRightTotal()).orElse(0) + Optional.ofNullable(sum2.getQuestionUserRightTotal()).orElse(0));
            return sum1;
        });

        chapterDO.setQuestionSumDO(chapterSum);
    }

    /**
     * 计算雷达图章节通过率
     *
     * @param chapters
     * @return
     */
    private List<ChartRadarVO> calculateRadarData(List<ChapterDO> chapters) {
        return chapters.stream().map(chapter -> {
            ChartRadarVO radar = new ChartRadarVO();
            String label = chapter.getChapterName().split("章")[0];
            radar.setLabel(label + "章");
            // 计算该章节的正确率
            double correctRate = calculateChapterCorrectRate(chapter.getQuestionSumDO());
            radar.setRadarValue(correctRate * 100);
            return radar;
        }).collect(Collectors.toList());
    }

    /**
     * 做题数 / 总题目数 = 做题率
     *
     * @param questionSumDO
     * @return
     */
    private double calculateChapterCorrectRate(QuestionSumDO questionSumDO) {
        if (questionSumDO == null || questionSumDO.getQuestionTotal() == 0) {
            return 0.0;
        }
        return questionSumDO.getQuestionUserRightTotal().doubleValue() / questionSumDO.getQuestionTotal();
    }

    /**
     * 科目通过率
     *
     * @param chapters
     * @param total
     * @return
     */
    private double calculateSubjectPassRate(List<ChapterDO> chapters, int total) {
        // 1. 确保chapters不为空
        List<ChapterDO> safeChapters = Optional.ofNullable(chapters).orElse(Collections.emptyList());

        // 2. 获取所有非空的QuestionSumDO
        List<QuestionSumDO> questionSums = safeChapters.stream().map(ChapterDO::getQuestionSumDO).filter(Objects::nonNull).collect(Collectors.toList());

        // 3. 统计正确题目总数
        int totalCorrect = questionSums.stream().map(sum -> Optional.ofNullable(sum.getQuestionUserRightTotal()).orElse(0)).mapToInt(Integer::intValue).sum();

        return total == 0 ? 0.0 : (double) totalCorrect / total;
    }

    /**
     * 章节掌握程度： 章-节 菜单，掌握程度
     *
     * @param chapters
     * @return
     */
    private List<NoHighChartVO> calculateNoHighCharts(List<ChapterDO> chapters) {
        List<NoHighChartVO> noHighCharts = new ArrayList<>();

        for (ChapterDO chapter : chapters) {
            // 只处理掌握程度不高的章节
            double chapterGraspRate = calculateChapterCorrectRate(chapter.getQuestionSumDO());

            NoHighChartVO noHighChart = new NoHighChartVO();
            noHighChart.setChapterCode(chapter.getChapterCode());
            noHighChart.setChapterName(chapter.getChapterName());

            GraspLevelEnum graspLevel = GraspLevelEnum.getByAccuracy(chapterGraspRate);
            noHighChart.setChartGraspType(graspLevel.getCode());
            noHighChart.setChartGraspDesc(graspLevel.getDescription());

            // 处理小节信息
            List<NoHighSectionVO> noHighSections = calculateNoHighSections(chapter.getSections());
            if (!noHighSections.isEmpty()) {
                noHighChart.setNoHighSectionVOList(noHighSections);
            }
            noHighCharts.add(noHighChart);
        }

        return noHighCharts;
    }

    /**
     * 章节掌握程度： 节 菜单，掌握程度
     *
     * @param sections
     * @return
     */
    private List<NoHighSectionVO> calculateNoHighSections(List<SectionDO> sections) {
        if (CollectionUtils.isEmpty(sections)) {
            return Collections.emptyList();
        }
        return sections.stream().map(section -> {
            // 计算小节的掌握程度
            double sectionGraspRate = calculateChapterCorrectRate(section.getQuestionSumDO());

            NoHighSectionVO sectionVO = new NoHighSectionVO();
            sectionVO.setSectionCode(section.getSectionCode());
            sectionVO.setSectionName(section.getSectionName());

            GraspLevelEnum graspLevel = GraspLevelEnum.getByAccuracy(sectionGraspRate);
            sectionVO.setSectionGraspType(graspLevel.getCode());
            sectionVO.setSectionGraspDesc(graspLevel.getDescription());

            return sectionVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 错题分析，章-节，菜单，错题率
     *
     * @param chapters
     * @return
     */
    private List<ChartWrongRateInfoVO> calculateWrongRateInfo(List<ChapterDO> chapters) {
        List<ChartWrongRateInfoVO> wrongRateInfos = new ArrayList<>();

        for (ChapterDO chapter : chapters) {
            QuestionSumDO questionSumDO = chapter.getQuestionSumDO();
            if (questionSumDO == null || questionSumDO.getQuestionUserWrongTotal() == null || questionSumDO.getQuestionUserWrongTotal() == 0) {
                continue;
            }

            ChartWrongRateInfoVO wrongRateInfo = new ChartWrongRateInfoVO();
            wrongRateInfo.setChapterCode(chapter.getChapterCode());
            wrongRateInfo.setChapterName(chapter.getChapterName());


            double chapterWrongRate = (double) questionSumDO.getQuestionUserWrongTotal() / questionSumDO.getQuestionUserAnswerTotal();
            WrongRateTypeEnum wrongRateType = WrongRateTypeEnum.getByRate(chapterWrongRate);
            wrongRateInfo.setChartWrongType(wrongRateType.getCode());
            wrongRateInfo.setChartWrongDesc(wrongRateType.getDescription());

            // 处理小节错误率信息
            List<SectionWrongRateInfoVO> sectionWrongRates = calculateSectionWrongRates(chapter.getSections());
            if (!sectionWrongRates.isEmpty()) {
                wrongRateInfo.setSectionWrongRateInfoVOList(sectionWrongRates);
            }
            wrongRateInfos.add(wrongRateInfo);
        }

        return wrongRateInfos;
    }


    /**
     * 错题分析，节，菜单，错题率
     *
     * @param sections
     * @return
     */
    private List<SectionWrongRateInfoVO> calculateSectionWrongRates(List<SectionDO> sections) {
        if (CollectionUtils.isEmpty(sections)) {
            return Collections.emptyList();
        }

        return sections.stream().map(section -> {
            QuestionSumDO questionSumDO = section.getQuestionSumDO();
            if (questionSumDO == null || questionSumDO.getQuestionUserWrongTotal() == null || questionSumDO.getQuestionUserWrongTotal() == 0) {
                return null;
            }
            // 计算小节的掌握程度
            SectionWrongRateInfoVO sectionWrongRate = new SectionWrongRateInfoVO();
            sectionWrongRate.setSectionCode(section.getSectionCode());
            sectionWrongRate.setSectionName(section.getSectionName());

            double wrongRate = (double) questionSumDO.getQuestionUserWrongTotal() / questionSumDO.getQuestionUserAnswerTotal();
            WrongRateTypeEnum wrongRateType = WrongRateTypeEnum.getByRate(wrongRate);
            sectionWrongRate.setSectionWrongType(wrongRateType.getCode());
            sectionWrongRate.setSectionWrongDesc(wrongRateType.getDescription());

            return sectionWrongRate;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 计算总体错误率
     *
     * @param chapters 章节列表
     * @return 错误率(百分比), 如果未答题则返回null
     */
    private Double calculateOverallWrongRate(List<ChapterDO> chapters) {
        int totalWrong = calculateTotalQuestionCount(chapters, QuestionSumDO::getQuestionUserWrongTotal);
        int totalAnswered = calculateTotalQuestionCount(chapters, QuestionSumDO::getQuestionUserAnswerTotal);

        if (totalAnswered == 0) {
            return null;
        }

        return (double) totalWrong / totalAnswered * 100;
    }

    /**
     * 计算题目总数
     *
     * @param chapters       章节列表
     * @param countExtractor 数量提取函数 (如获取错题数、已做题数等)
     * @return 总数
     */
    private int calculateTotalQuestionCount(List<ChapterDO> chapters, Function<QuestionSumDO, Integer> countExtractor) {
        // 1. 确保chapters不为空,返回安全的列表
        if (chapters == null) {
            return 0;
        }
        // 2. 遍历chapters获取有效的统计数据
        int totalCount = 0;
        for (ChapterDO chapter : chapters) {
            // 获取章节的统计数据
            QuestionSumDO questionSum = chapter.getQuestionSumDO();
            if (questionSum == null) {
                continue;
            }
            // 使用传入的函数获取具体的计数值(错题数/总数等)
            Integer count = countExtractor.apply(questionSum);
            if (count != null) {
                totalCount += count;
            }
        }
        return totalCount;
    }


    /**
     * 计算答题情况分析
     */
    private List<AnswerQuestionVO> calculateAnswerQuestionAnalysis(List<ChapterDO> chapters, Long accountId, String subjectCode) {
        List<AnswerQuestionVO> answerQuestionVOs = new ArrayList<>();

        // 添加三种类型的练题情况分析
        addAnswerQuestionAnalysis(answerQuestionVOs, chapters, AnswerQuestionConstant.TYPE_CHAPTER, AnswerQuestionConstant.NAME_CHAPTER, accountId, subjectCode);
        addAnswerQuestionAnalysis(answerQuestionVOs, chapters, AnswerQuestionConstant.TYPE_MOCK, AnswerQuestionConstant.NAME_MOCK, accountId, subjectCode);
        addAnswerQuestionAnalysis(answerQuestionVOs, chapters, AnswerQuestionConstant.TYPE_REAL, AnswerQuestionConstant.NAME_REAL, accountId, subjectCode);

        return answerQuestionVOs;
    }

    /**
     * 添加答题分析
     */
    private void addAnswerQuestionAnalysis(List<AnswerQuestionVO> answerQuestionVOs, List<ChapterDO> chapters, String type, String typeName, Long accountId, String subjectCode) {
        AnswerQuestionVO vo = new AnswerQuestionVO();
        vo.setType(type);
        vo.setTypeName(typeName);

        if (AnswerQuestionConstant.TYPE_CHAPTER.equals(type)) {
            processChapterExamAnalysis(vo, chapters);
        } else {
            QuestionSetType questionSetType = AnswerQuestionConstant.TYPE_MOCK.equals(type) ? QuestionSetType.MOCK_EXAMS : QuestionSetType.REAL_EXAMS;
            processOtherExamAnalysis(vo, accountId, subjectCode, type, questionSetType);
        }

        setWrongType(vo);
        answerQuestionVOs.add(vo);
    }

    /**
     * 处理章节练习分析
     */
    private void processChapterExamAnalysis(AnswerQuestionVO vo, List<ChapterDO> chapters) {
        // 计算总题数、已答题数、错题数
        int totalQuestions = calculateQuestionCount(chapters, QuestionSumDO::getQuestionTotal);
        int answeredQuestions = calculateQuestionCount(chapters, QuestionSumDO::getQuestionUserAnswerTotal);
        int wrongQuestions = calculateQuestionCount(chapters, QuestionSumDO::getQuestionUserWrongTotal);

        // 设置统计结果
        setAnswerQuestionStats(vo, totalQuestions, answeredQuestions, wrongQuestions);
    }

    /**
     * 处理其他类型考试分析
     */
    private void processOtherExamAnalysis(AnswerQuestionVO vo, Long accountId, String subjectCode, String examType, QuestionSetType questionSetType) {
        // 获取题集信息
        List<QuestionSetDO> questionSetIds = getQuestionSetIds(subjectCode, questionSetType);
        int count = (int) questionSetIds.stream().flatMap(q -> q.getQuestionIds().stream()).count();

        // 获取并过滤考试记录
        List<UserExamRecordDO> filteredExamRecords = getFilteredExamRecords(accountId, questionSetIds.stream().map(q -> q.getId()).collect(Collectors.toList()));

        // 获取答题记录
        List<ExerciseDO> exerciseRecords = Optional.ofNullable(getExerciseRecords(accountId, filteredExamRecords, examType)).orElse(new ArrayList<>());

        List<ExerciseDO> distinctExerciseRecords = exerciseRecords.stream()
                .filter(Objects::nonNull)
                .filter(e -> StringUtils.isNotBlank(e.getUserAnswer()))
                .collect(Collectors.toMap(
                        ExerciseDO::getQuestionId,
                        Function.identity(),
                        (existing, replacement) -> existing.getCreateTime().isAfter(replacement.getCreateTime())
                                ? existing
                                : replacement
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        int wrongAnswerd = (int) distinctExerciseRecords.stream().filter(u -> !Boolean.TRUE.equals(u.getIsRight())).count();
        // 计算并设置统计结果
        setAnswerQuestionStats(vo, count, distinctExerciseRecords.size(), wrongAnswerd);
    }

    /**
     * 获取题集ID列表
     */
    private List<QuestionSetDO> getQuestionSetIds(String subjectCode, QuestionSetType questionSetType) {
        return questionDomain.getQuestionSetsBySubjectAndType(subjectCode, questionSetType);
    }

    /**
     * 获取过滤后的考试记录
     * 对于相同的accountId和questionSetId，只保留最新的记录
     */
    private List<UserExamRecordDO> getFilteredExamRecords(Long accountId, List<Long> questionSetIds) {
        // 获取考试记录
        List<UserExamRecordDO> examRecords = exerciseDomain.findExamRecordsByAccountIdAndQuestionSetIds(accountId, questionSetIds);

        // 按照accountId和questionSetId分组，取最新记录
        Map<String, Optional<UserExamRecordDO>> groupedRecords = examRecords.stream().collect(Collectors.groupingBy(record -> record.getAccountId() + "_" + record.getQuestionSetId(), Collectors.reducing((a, b) -> a.getUpdateTime().isAfter(b.getUpdateTime()) ? a : b)));

        return groupedRecords.values().stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
    }

    /**
     * 获取练习记录
     */
    private List<ExerciseDO> getExerciseRecords(Long accountId, List<UserExamRecordDO> examRecords, String examType) {
        List<Long> recordIds = examRecords.stream().map(UserExamRecordDO::getId).collect(Collectors.toList());

        return exerciseDomain.findUserAnswerExercisesByExamRecordId(accountId, recordIds, examType);
    }

    /**
     * 计算题目数量
     */
    private int calculateQuestionCount(List<ChapterDO> chapters, Function<QuestionSumDO, Integer> countExtractor) {
        if (chapters == null) {
            return 0;
        }

        int total = 0;
        for (ChapterDO chapter : chapters) {
            QuestionSumDO questionSum = chapter.getQuestionSumDO();
            if (questionSum != null) {
                Integer count = countExtractor.apply(questionSum);
                total += count != null ? count : 0;
            }
        }
        return total;
    }

    /**
     * 设置答题统计结果
     */
    private void setAnswerQuestionStats(AnswerQuestionVO vo, int totalQuestions, int answeredQuestions, int wrongQuestions) {
        vo.setTotalCount(totalQuestions);
        vo.setAnswerCount(answeredQuestions);
        vo.setAnswerRate(calculateRate(answeredQuestions, totalQuestions));
        vo.setWrongCount(wrongQuestions);
        vo.setWrongRate(calculateRate(wrongQuestions, answeredQuestions));
    }

    /**
     * 计算比率
     */
    private double calculateRate(int numerator, int denominator) {
        return denominator == 0 ? 0.0 : (double) numerator / denominator * 100;
    }

    /**
     * 设置错误类型
     */
    private void setWrongType(AnswerQuestionVO vo) {
        if (vo.getAnswerCount() == 0) {
            vo.setWrongType(WrongRateTypeEnum.NONE.getCode());
            vo.setWrongTypeDesc(WrongRateTypeEnum.NONE.getDescription());
        } else {
            WrongRateTypeEnum wrongRateType = WrongRateTypeEnum.getByRate(vo.getWrongRate() / 100);
            vo.setWrongType(wrongRateType.getCode());
            vo.setWrongTypeDesc(wrongRateType.getDescription());
        }
    }

    /**
     * 是否存在错题分析
     *
     * @param wrongRate
     * @return
     */
    private Integer determineErrorAnalysisType(Double wrongRate) {
        if (wrongRate == null) {
            return 2; // 未答过题目
        } else if (wrongRate != 0) {
            return 0; // 有错题
        } else {
            return 1; // 答过题并且全部正确
        }
    }
}