package ai.exam.pages.question;

import ai.exam.domain.question.QuestionDO;
import ai.exam.domain.question.QuestionDomain;
import ai.exam.pages.question.mapper.QuestionVODOMapper;
import ai.exam.pages.question.vo.QuestionVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class QuestionPageApplication {

    @Resource
    private QuestionDomain questionDomain;

    @Resource
    private QuestionVODOMapper questionVODOMapper;

    public List<QuestionVO> getQuestions(int page, int size) {
        // TODO 按新的领域方法来实现
        // List<QuestionDO> questionDOS = questionDomain.findAll(page, size);

        // return questionVODOMapper.toQuestionVOList(questionDOS);
        return null;
    }
}
