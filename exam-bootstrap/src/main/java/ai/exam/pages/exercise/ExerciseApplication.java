package ai.exam.pages.exercise;

import ai.exam.common.enums.ExamModeEnum;
import ai.exam.common.exception.AIException;
import ai.exam.domain.exercise.*;
import ai.exam.domain.question.*;
import ai.exam.domain.question.enums.QuestionSetType;
import ai.exam.pages.common.vo.BooleanResultVO;
import ai.exam.pages.exercise.mapper.UserExamRecordMapper;
import ai.exam.pages.exercise.vo.AIExerciseQuestionVO;
import ai.exam.pages.exercise.vo.SubmitAIExerciseAnswerVO;
import ai.exam.pages.exercise.vo.UserExamRecordlVO;
import ai.exam.pages.knowledgeslicing.vo.*;
import ai.exam.pages.practice.vo.WrongQuestionVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExerciseApplication {

    @Resource
    private ExerciseDomain exerciseDomain;

    @Resource
    private QuestionDomain questionDomain;
    @Resource
    private UserExamRecordMapper userExamRecordMapper;
    @Resource
    private QuestionSetDomain questionSetDomain;

    public BooleanResultVO saveOrUpdateExerciseExplain(Long accountId, String subjectCode, Boolean showExplain) {
        ExerciseExplainDO exerciseExplainDO = new ExerciseExplainDO();
        exerciseExplainDO.setSubjectCode(subjectCode);
        exerciseExplainDO.setAccountId(accountId);
        exerciseExplainDO.setShowExplain(showExplain);
        exerciseDomain.saveOrUpdateExerciseExplain(exerciseExplainDO);
        return new BooleanResultVO(true);
    }

    public AIExerciseQuestionVO getAIExerciseQuestions(Long accountId, String subjectCode) {
        AIExerciseQuestionVO result = new AIExerciseQuestionVO();

        // 获取是否展示AI个性化练习说明
        ExerciseExplainDO exerciseExplain = exerciseDomain.getExerciseExplainByAccountIdAndSubjectCode(accountId, subjectCode);
        boolean showExplain = exerciseExplain == null || exerciseExplain.getShowExplain();
        result.setShowExplain(showExplain);

        // 获取用户最近一次AI智练记录
        AIExerciseRecordDO latestRecord = exerciseDomain.getLatestAIExerciseRecordBySubjectCode(accountId, subjectCode);

        List<QuestionDO> questions;
        String aiDescription;

        if (latestRecord == null || isAllQuestionsAnswered(latestRecord.getAiExerciseQuestions())) {
            // 如果记录不存在或所有题目都已练习，获取新的AI智练题目
            AIQuestionsDO aiQuestions = getZnttQuestionList(subjectCode, accountId);
            questions = aiQuestions.getQuestions();
            aiDescription = aiQuestions.getDescription();

            // 生成新的AI智练记录
            latestRecord = exerciseDomain.generateAIExerciseRecord(accountId, subjectCode, aiDescription, questions);
        } else {
            // 使用现有的AI智练记录
            questions = getQuestionsFromAIExerciseRecord(latestRecord);
            aiDescription = latestRecord.getAiDescription();
        }

        questions.sort((q1, q2) -> {
            // 首先按题型顺序排序
            int typeCompare = q1.getType().getOrder().compareTo(q2.getType().getOrder());
            // 如果题型相同,则按题目ID排序
            if (typeCompare == 0) {
                return q1.getId().compareTo(q2.getId());
            }
            return typeCompare;
        });

        // 设置AI智练记录ID
        result.setAiExerciseRecordId(latestRecord.getId());

        // 设置AI个性化说明
        result.setAiDescription(aiDescription);

        // 确定定位的题目ID
        Long locatedQuestionId = null;
        if (!questions.isEmpty()) {
            // 获取题目定位信息
            LocationInformationDO locationInfo = exerciseDomain.getLocationInformationByMode(accountId, ExamModeEnum.AIZL.getCode(), null, null, latestRecord.getId());
            if (locationInfo == null || locationInfo.getQuestionId() == null) {
                locatedQuestionId = questions.get(0).getId();
            } else {
                boolean questionExists = questions.stream().anyMatch(q -> q.getId().equals(locationInfo.getQuestionId()));
                locatedQuestionId = questionExists ? locationInfo.getQuestionId() : questions.get(0).getId();
            }
        }
        // 转换题目列表
        List<ExamQuestionVO> examQuestionVOList = convertToExamQuestionVOList(questions, locatedQuestionId);
        result.setAllQuestions(examQuestionVOList);

        // 获取已完成的题目信息
        List<AccountExamQuestionVO> completedQuestions = getCompletedQuestions(latestRecord);
        result.setCompletedQuestions(completedQuestions);

        // 获取收藏题目信息
        List<QuestionFavoriteRecordVO> favoriteRecords = getFavoriteRecords(accountId, subjectCode, questions);
        result.setFavoriteRecordVOS(favoriteRecords);

        return result;
    }


    // private AIQuestionsDO getZnttQuestionList(String subjectCode, Long accountId) {
    //     AIQuestionsDO aiQuestions = new AIQuestionsDO();
    //     // key = questionId, value=questionSetId
    //     Map<Long, Long> questionSetResultMap = new HashMap<>();
    //     try {
    //         // 1. 获取近5年真题及题目
    //         List<QuestionSetDO> questionSets = Optional.ofNullable(questionDomain.findBySubjectCodeAndYearGreaterThanEqualAndType(subjectCode, LocalDate.now().getYear() - 5, QuestionSetType.REAL_EXAMS, false)).orElse(Collections.emptyList());
    //         if (questionSets.isEmpty()) {
    //             return createEmptyResult("未找到真题");
    //         }
    //         questionSets.sort((a, b) -> b.getYear().compareTo(a.getYear()));
    //         List<Long> questionSetIds = questionSets.stream().map(QuestionSetDO::getId).collect(Collectors.toList());
    //
    //         // 获取近5年真题题目
    //         List<QuestionDO> questions = questionDomain.getQuestionsByQuestionSetId(questionSetIds);
    //
    //         // 2. 计算推送题目数量
    //         int totalQuestions = questions.size();
    //         int pushSum = totalQuestions > 0 ? Math.min(20, Math.max(1, (int) Math.ceil(totalQuestions / (45 * 0.65)))) : 0;
    //         int newQuestionsCount = Math.max(0, (int) Math.ceil(pushSum * 0.65));
    //         int wrongQuestionsCount = Math.max(0, (int) Math.ceil(pushSum * 0.25));
    //         int reviewQuestionsCount = Math.max(0, pushSum - newQuestionsCount - wrongQuestionsCount);
    //
    //         // 3. 获取用户做题记录
    //         List<ExerciseDO> allExercises = Optional.ofNullable(exerciseDomain.findByQuestionSetIdsAndMode(accountId, questionSetIds, ExamModeEnum.ZTYLKSMS.getCode())).orElse(Collections.emptyList());
    //         // 用户答案不为空的
    //         List<ExerciseDO> answeredExercises = allExercises.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getUserAnswer())).collect(Collectors.toList());
    //         Set<Long> answeredQuestionIds = answeredExercises.stream().map(e -> e.getQuestionId()).collect(Collectors.toSet());
    //
    //         // 4. 获取未做题目
    //         List<Long> newQuestionIds = new ArrayList<>();
    //         int remainingNewCount = newQuestionsCount;
    //         for (QuestionSetDO questionSet : questionSets) {
    //             if (remainingNewCount <= 0) break;
    //             // 用户答案为空的该年真题
    //             List<Long> yearQuestionIds = questionDomain.getQuestionsByQuestionSetId(questionSet.getId()).stream().map(QuestionDO::getId).filter(id -> !answeredQuestionIds.contains(id)).collect(Collectors.toList());
    //             List<Long> selectedIds = getRandomElements(yearQuestionIds, remainingNewCount);
    //             newQuestionIds.addAll(selectedIds);
    //             selectedIds.forEach(s -> questionSetResultMap.put(s, questionSet.getId()));
    //             remainingNewCount -= selectedIds.size();
    //         }
    //
    //         // 5. 获取复习题目（做对的题）
    //         Set<Long> correctQuestionIds = answeredExercises.stream().filter(e -> Boolean.TRUE.equals(e.getIsRight())).map(ExerciseDO::getQuestionId).collect(Collectors.toSet());
    //         List<Long> reviewQuestionIds = getRandomElements(new ArrayList<>(correctQuestionIds), reviewQuestionsCount);
    //         answeredExercises.stream().filter(a -> reviewQuestionIds.contains(a.getQuestionId())).forEach(a -> questionSetResultMap.put(a.getQuestionId(), a.getQuestionSetId()));
    //
    //         // 6. 获取错题
    //         List<WrongQuestionRecordDO> recentWrongRecords = Optional.ofNullable(exerciseDomain.getWrongQuestionRecordsBySubjectAndAccount(subjectCode, accountId)).orElse(Collections.emptyList()).stream().filter(w -> w.getUpdatedAt() != null && w.getUpdatedAt().isAfter(LocalDateTime.now().minusDays(7))).collect(Collectors.toList());
    //         List<Long> wrongQuestionIds = getRandomElements(recentWrongRecords.stream().filter(w -> w.getQuestionSetId() != null).map(WrongQuestionRecordDO::getQuestionId).filter(id -> !newQuestionIds.contains(id) && !reviewQuestionIds.contains(id)).collect(Collectors.toList()), wrongQuestionsCount);
    //         recentWrongRecords.stream().filter(r -> wrongQuestionIds.contains(r.getQuestionId())).forEach(r -> questionSetResultMap.put(r.getQuestionId(), r.getQuestionSetId()));
    //
    //         // 7. 补充题目（如果上述题目数量不足）
    //         int remainingCount = Math.max(0, pushSum - newQuestionIds.size() - reviewQuestionIds.size() - wrongQuestionIds.size());
    //
    //         List<Long> supplementQuestionIds;
    //         if (remainingCount > 0) {
    //             List<Long> remainingQuestionIds = questions.stream().map(QuestionDO::getId).filter(id -> !newQuestionIds.contains(id) && !reviewQuestionIds.contains(id) && !wrongQuestionIds.contains(id)).collect(Collectors.toList());
    //             supplementQuestionIds = getRandomElements(remainingQuestionIds, remainingCount);
    //             questions.stream().filter(s -> supplementQuestionIds.contains(s.getId())).forEach(s -> questionSetResultMap.put(s.getId(), s.getQuestionSetId()));
    //         } else {
    //             supplementQuestionIds = new ArrayList<>();
    //         }
    //
    //         // 8. 组装结果
    //         Set<Long> allSelectedIds = new HashSet<>();
    //         allSelectedIds.addAll(Optional.ofNullable(newQuestionIds).orElse(Collections.emptyList()));
    //         allSelectedIds.addAll(Optional.ofNullable(reviewQuestionIds).orElse(Collections.emptyList()));
    //         allSelectedIds.addAll(Optional.ofNullable(wrongQuestionIds).orElse(Collections.emptyList()));
    //         allSelectedIds.addAll(Optional.ofNullable(supplementQuestionIds).orElse(Collections.emptyList()));
    //
    //         // 返回所有题目详情
    //         List<QuestionDO> selectedQuestions = questionDomain.getQuestionsByQuestionIds(new ArrayList<>(allSelectedIds));
    //         selectedQuestions.forEach(s -> s.setQuestionSetId(questionSetResultMap.get(s.getId())));
    //
    //         // 9. 构建描述信息
    //         String description = buildDescription(allSelectedIds.size(), newQuestionIds, reviewQuestionIds, wrongQuestionIds, supplementQuestionIds);
    //         aiQuestions.setQuestions(selectedQuestions);
    //         aiQuestions.setDescription(description);
    //         return aiQuestions;
    //
    //      } catch (Exception e) {
    //             log.error("getZnttQuestionList.ERROR.Exception，{}，{}", e.getMessage(), e);
    //             return null;
    //         }
    // }

    private AIQuestionsDO getZnttQuestionList(String subjectCode, Long accountId) {
        AIQuestionsDO aiQuestions = new AIQuestionsDO();
        Map<Long, Long> questionSetResultMap = new ConcurrentHashMap<>();
        try {
            // 1. 获取近5年真题
            List<QuestionSetDO> questionSets = Optional.ofNullable(questionDomain.findBySubjectCodeAndYearGreaterThanEqualAndType(subjectCode, LocalDate.now().getYear() - 5, QuestionSetType.REAL_EXAMS, false)).orElse(Collections.emptyList());
            questionSets.sort((a, b) -> b.getYear().compareTo(a.getYear()));
            List<Long> questionSetIds = questionSets.stream().map(QuestionSetDO::getId).collect(Collectors.toList());

            // 并发获取真题题目和用户做题记录
            Map<Long, List<Long>> questions = questionDomain.getMapQuestionIdsByQuestionSetId(questionSetIds);
            List<ExerciseDO> allExercises = Optional.ofNullable(exerciseDomain.findByQuestionSetIdsAndMode(accountId, questionSetIds, ExamModeEnum.ZTYLKSMS.getCode())).orElse(Collections.emptyList());
            List<WrongQuestionRecordDO> wrongRecords = Optional.ofNullable(exerciseDomain.getWrongQuestionRecordsBySubjectAndAccount(subjectCode, accountId)).orElse(Collections.emptyList());
            List<AIExerciseQuestionDO> aiExerciseQuestionsBySubjectCode = Optional.ofNullable(exerciseDomain.getAIExerciseQuestionsBySubjectCode(accountId, subjectCode)).orElse(Collections.emptyList());
            Set<Long> aiExerciseQuestionIds = aiExerciseQuestionsBySubjectCode.stream().map(a -> a.getQuestionId()).collect(Collectors.toSet());

            // 2. 计算推送题目数量
            int totalQuestions = questions.values().stream().mapToInt(List::size).sum();
            int pushSum = totalQuestions > 0 ? Math.min(20, Math.max(1, (int) Math.ceil(totalQuestions / (45 * 0.65)))) : 0;
            int newQuestionsCount = Math.max(0, (int) Math.ceil(pushSum * 0.65));
            int wrongQuestionsCount = Math.max(0, (int) Math.ceil(pushSum * 0.25));
            int reviewQuestionsCount = Math.max(0, pushSum - newQuestionsCount - wrongQuestionsCount);

            // 3. 处理用户做题记录
            List<ExerciseDO> answeredExercises = allExercises.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getUserAnswer())).collect(Collectors.toList());
            Set<Long> answeredQuestionIds = answeredExercises.stream().map(ExerciseDO::getQuestionId).collect(Collectors.toSet());

            // 4. 获取未做题目
            List<Long> newQuestionIds = Collections.synchronizedList(new ArrayList<>());
            AtomicInteger remainingNewCount = new AtomicInteger(newQuestionsCount);

            questionSets.parallelStream().forEach(questionSet -> {
                if (remainingNewCount.get() > 0) {
                    List<Long> yearQuestionIds = questionDomain.getQuestionIdsByQuestionSetId(questionSet.getId()).stream().filter(id -> !answeredQuestionIds.contains(id)).filter(id -> !aiExerciseQuestionIds.contains(id)).collect(Collectors.toList());
                    List<Long> selectedIds = getRandomElements(yearQuestionIds, remainingNewCount.get());
                    newQuestionIds.addAll(selectedIds);
                    selectedIds.forEach(s -> questionSetResultMap.put(s, questionSet.getId()));
                    remainingNewCount.addAndGet(-selectedIds.size());
                }
            });

            // 5. 获取复习题目（做对的题），考试中做的
            Set<Long> correctQuestionIds = answeredExercises.stream().filter(e -> Boolean.TRUE.equals(e.getIsRight())).map(ExerciseDO::getQuestionId).collect(Collectors.toSet());
            // 5.1 做对的题目，包括智能推题中的（做对的题）
            Set<Long> correctAiQuestionIds = aiExerciseQuestionsBySubjectCode.stream().filter(a -> Boolean.TRUE.equals(a.getIsRight())).map(AIExerciseQuestionDO::getQuestionId).collect(Collectors.toSet());
            ArrayList<Long> longs = new ArrayList<>(correctQuestionIds);
            longs.addAll(correctAiQuestionIds);
            List<Long> reviewQuestionIds = getRandomElements(longs, reviewQuestionsCount);
            answeredExercises.stream().filter(a -> reviewQuestionIds.contains(a.getQuestionId())).forEach(a -> questionSetResultMap.put(a.getQuestionId(), a.getQuestionSetId()));
            aiExerciseQuestionsBySubjectCode.stream().filter(a -> reviewQuestionIds.contains(a.getQuestionId())).forEach(a -> questionSetResultMap.put(a.getQuestionId(), a.getQuestionSetId()));

            // 6. 获取错题
            List<Long> wrongQuestionIds = wrongRecords.parallelStream().filter(w -> w.getUpdatedAt() != null && w.getUpdatedAt().isAfter(LocalDateTime.now().minusDays(7))).filter(w -> w.getQuestionSetId() != null).map(WrongQuestionRecordDO::getQuestionId).filter(id -> !newQuestionIds.contains(id) && !reviewQuestionIds.contains(id)).collect(Collectors.collectingAndThen(Collectors.toList(), list -> getRandomElements(list, wrongQuestionsCount)));
            wrongRecords.stream().filter(r -> wrongQuestionIds.contains(r.getQuestionId())).forEach(r -> questionSetResultMap.put(r.getQuestionId(), r.getQuestionSetId()));

            // 7. 补充题目
            int remainingCount = Math.max(0, pushSum - newQuestionIds.size() - reviewQuestionIds.size() - wrongQuestionIds.size());
            List<Long> supplementQuestionIds;
            if (remainingCount > 0) {
                List<Long> remainingQuestionIds = questions.values().stream().flatMap(List::stream).filter(id -> !newQuestionIds.contains(id) && !reviewQuestionIds.contains(id) && !wrongQuestionIds.contains(id)).collect(Collectors.toList());
                supplementQuestionIds = getRandomElements(remainingQuestionIds, remainingCount);
                questions.forEach((questionSetId, questionIds) -> questionIds.stream().filter(questionId -> supplementQuestionIds.contains(questionId)).forEach(questionId -> questionSetResultMap.put(questionId, questionSetId)));
            } else {
                supplementQuestionIds = Collections.emptyList();
            }

            // 8. 组装结果
            Set<Long> allSelectedIds = new HashSet<>();
            allSelectedIds.addAll(Optional.ofNullable(newQuestionIds).orElse(Collections.emptyList()));
            allSelectedIds.addAll(Optional.ofNullable(reviewQuestionIds).orElse(Collections.emptyList()));
            allSelectedIds.addAll(Optional.ofNullable(wrongQuestionIds).orElse(Collections.emptyList()));
            allSelectedIds.addAll(Optional.ofNullable(supplementQuestionIds).orElse(Collections.emptyList()));

            // 并发获取所有题目详情
            List<QuestionDO> selectedQuestions = questionDomain.getQuestionsByQuestionIds(new ArrayList<>(allSelectedIds));
            selectedQuestions.forEach(s -> s.setQuestionSetId(questionSetResultMap.get(s.getId())));

            // 9. 构建描述信息
            String description = buildDescription(allSelectedIds.size(), newQuestionIds, reviewQuestionIds, wrongQuestionIds, supplementQuestionIds);
            aiQuestions.setQuestions(selectedQuestions);
            aiQuestions.setDescription(description);
            return aiQuestions;

        } catch (Exception e) {
            log.error("getZnttQuestionList.ERROR.Exception，{}，{}", e.getMessage(), e);
            return aiQuestions;
        }
    }


    private String buildDescription(int totalSize, List<Long> newQuestionIds, List<Long> reviewQuestionIds, List<Long> wrongQuestionIds, List<Long> supplementQuestionIds) {
        StringBuilder description = new StringBuilder().append(String.format("您的个性化习题已生成，合计 %d 题，包含：\n ", totalSize));

        if (CollectionUtils.isNotEmpty(newQuestionIds)) {
            description.append(String.format("【%d道】您已系统复习过知识点，但尚未练习到的该知识点对应的高频真题\n ", newQuestionIds.size()));
        }
        if (CollectionUtils.isNotEmpty(wrongQuestionIds)) {
            description.append(String.format("【%d道】您最近一周错题的变式练习题\n ", wrongQuestionIds.size()));
        }
        if (CollectionUtils.isNotEmpty(reviewQuestionIds)) {
            description.append(String.format("【%d道】您已系统复习过的知识点再次巩固\n\n ", reviewQuestionIds.size()));
        }
        if (CollectionUtils.isNotEmpty(supplementQuestionIds)) {
            description.append(String.format("【%d道】该知识点对应的高频真题推荐 \n\n ", supplementQuestionIds.size()));
        }
        return description.toString();
    }


    private List<Long> getRandomElements(List<Long> list, int numElements) {
        if (list.size() <= numElements) {
            return list;
        }

        List<Long> copyList = new ArrayList<>(list);
        List<Long> result = new ArrayList<>();
        Random random = new Random();

        for (int i = 0; i < numElements; i++) {
            int randomIndex = random.nextInt(copyList.size());
            result.add(copyList.get(randomIndex));
            copyList.remove(randomIndex);
        }

        return result;
    }

    public AIExerciseQuestionVO getAIExerciseQuestionsById(Long accountId, String subjectCode, Long recordId) {
        AIExerciseQuestionVO result = new AIExerciseQuestionVO();

        // 获取用户最近一次AI智练记录
        AIExerciseRecordDO recordDO = exerciseDomain.getAIExerciseRecordByIdAndAccountId(recordId, accountId);
        if (recordDO == null) {
            throw new AIException(400, "未获取到对应得练习记录");
        }
        List<QuestionDO> questions = getQuestionsFromAIExerciseRecord(recordDO);

        // 设置AI智练记录ID
        result.setAiExerciseRecordId(recordDO.getId());

        // 确定定位的题目ID
        Long locatedQuestionId = null;
        if (!questions.isEmpty()) {
            // 获取题目定位信息
            LocationInformationDO locationInfo = exerciseDomain.getLocationInformationByMode(accountId, ExamModeEnum.AIZL.getCode(), null, null, recordDO.getId());
            if (locationInfo == null || locationInfo.getQuestionId() == null) {
                locatedQuestionId = questions.get(0).getId();
            } else {
                boolean questionExists = questions.stream().anyMatch(q -> q.getId().equals(locationInfo.getQuestionId()));
                locatedQuestionId = questionExists ? locationInfo.getQuestionId() : questions.get(0).getId();
            }
        }
        // 转换题目列表
        List<ExamQuestionVO> examQuestionVOList = convertToExamQuestionVOList(questions, locatedQuestionId);
        result.setAllQuestions(examQuestionVOList);

        // 获取已完成的题目信息
        List<AccountExamQuestionVO> completedQuestions = getCompletedQuestions(recordDO);
        result.setCompletedQuestions(completedQuestions);

        // 获取收藏题目信息
        List<QuestionFavoriteRecordVO> favoriteRecords = getFavoriteRecords(accountId, subjectCode, questions);
        result.setFavoriteRecordVOS(favoriteRecords);

        return result;
    }

    private boolean isAllQuestionsAnswered(List<AIExerciseQuestionDO> aiExerciseQuestions) {
        return aiExerciseQuestions.stream().allMatch(q -> q.getIsRight() != null);
    }

    private List<QuestionDO> getQuestionsFromAIExerciseRecord(AIExerciseRecordDO record) {
        List<Long> questionIds = record.getAiExerciseQuestions().stream().map(AIExerciseQuestionDO::getQuestionId).collect(Collectors.toList());

        // 构建 questionId -> questionSetId 的映射
        // 如果有重复key，保留第一个值
        Map<Long, Long> questionSetIdMap = record.getAiExerciseQuestions().stream().collect(Collectors.toMap(AIExerciseQuestionDO::getQuestionId, AIExerciseQuestionDO::getQuestionSetId, (existing, replacement) -> existing));

        // 构建 questionSetId -> QuestionSetDO 的映射
        Map<Long, QuestionSetDO> questionSetMap = questionSetDomain.findQuestionSetInfoByQuestionSetIds(new ArrayList<>(questionSetIdMap.values())).stream().collect(Collectors.toMap(QuestionSetDO::getId, questionSet -> questionSet, (existing, replacement) -> existing  // 如果有重复key，保留第一个值
        ));

        List<QuestionDO> questionsByQuestionIds = questionDomain.getQuestionsByQuestionIds(questionIds);
        List<QuestionDO> result = new ArrayList<>(questionsByQuestionIds.size());

        for (Long questionId : questionIds) {
            questionsByQuestionIds.stream().filter(q -> q.getId().equals(questionId)).findFirst().ifPresent(question -> {
                // 可以在这里使用 questionSetIdMap 和 questionSetMap 设置额外信息
                Long questionSetId = questionSetIdMap.get(questionId);
                QuestionSetDO questionSet = questionSetMap.get(questionSetId);

                // 如果需要设置题集相关信息到 question
                question.setQuestionSetId(questionSetId);
                if (questionSet != null) {
                    question.setQuestionSetType(questionSet.getType().getName());
                    question.setChapterCode(questionSet.getChapterCode());
                    question.setSectionCode(questionSet.getSectionCode());
                }
                result.add(question);
            });
        }
        return result;
    }

    private List<ExamQuestionVO> convertToExamQuestionVOList(List<QuestionDO> questions, Long locatedQuestionId) {
        AtomicInteger num = new AtomicInteger();
        return questions.stream().map(question -> {
            ExamQuestionVO vo = new ExamQuestionVO();
            vo.setQuestionId(question.getId());
            vo.setQuestionContent(question.getContent());
            vo.setQuestionType(question.getTypeName());
            vo.setOptions(question.getOptions());
            vo.setLocation(question.getId().equals(locatedQuestionId));
            vo.setNumber(num.incrementAndGet());
            vo.setRightAnswer(question.getAnswer());
            vo.setExplanation(question.getExplanationWithPrefix());

            vo.setQuestionSetId(question.getQuestionSetId());
            vo.setChapterCode(question.getChapterCode());
            vo.setSectionCode(question.getSectionCode());
            vo.setQuestionSetType(question.getQuestionSetType());
            return vo;
        }).collect(Collectors.toList());
    }

    private List<AccountExamQuestionVO> getCompletedQuestions(AIExerciseRecordDO record) {
        List<AccountExamQuestionVO> completedQuestions = new ArrayList<>();
        Map<Long, QuestionDO> questionMap = getQuestionsFromAIExerciseRecord(record).stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));

        for (AIExerciseQuestionDO aiQuestion : record.getAiExerciseQuestions()) {
            if (aiQuestion.getIsRight() != null) {
                AccountExamQuestionVO vo = new AccountExamQuestionVO();
                vo.setQuestionId(aiQuestion.getQuestionId());
                vo.setSelectedOption(aiQuestion.getUserAnswer());
                vo.setIsRight(aiQuestion.getIsRight());

                QuestionDO question = questionMap.get(aiQuestion.getQuestionId());
                if (question != null) {
                    vo.setRightAnswer(question.getAnswer());
                    vo.setAnalysis(question.getExplanationWithPrefix());
                }

                completedQuestions.add(vo);
            }
        }
        return completedQuestions;
    }

    private List<QuestionFavoriteRecordVO> getFavoriteRecords(Long accountId, String subjectCode, List<QuestionDO> questions) {
        List<Long> questionIds = questions.stream().map(QuestionDO::getId).collect(Collectors.toList());
        List<QuestionFavoriteRecordDO> favoriteRecords = exerciseDomain.getQuestionFavoriteRecordsBySubjectAndAccount(subjectCode, accountId);

        return favoriteRecords.stream().filter(record -> questionIds.contains(record.getQuestionId())).map(record -> {
            QuestionFavoriteRecordVO vo = new QuestionFavoriteRecordVO();
            vo.setQuestionId(record.getQuestionId());
            vo.setIsFavorite(record.getIsFavorite());
            return vo;
        }).collect(Collectors.toList());
    }

    public SubmitAnswerResultVO submitAIExerciseAnswer(Long accountId, SubmitAIExerciseAnswerVO submitVO) {
        String batchNum = submitVO.getBatchNum();
        if (StringUtils.isBlank(batchNum)) {
            throw new AIException(400, "batchNum不能为空");
        }
        // 获取题目信息
        QuestionDO question = questionDomain.getQuestionsByQuestionIds(List.of(submitVO.getQuestionId())).get(0);

        // 判断答案是否正确
        boolean isRight = question.getAnswer().equals(submitVO.getAnswer());

        // 更新AI练习题目记录
        AIExerciseQuestionDO updatedQuestion = exerciseDomain.updateAIExerciseQuestion(submitVO.getAiExerciseRecordId(), submitVO.getQuestionId(), isRight, submitVO.getAnswer(), batchNum);

        // 同时如果答案错误则记录用户错题集
        if (!isRight) {
            WrongQuestionRecordDO recordDO = new WrongQuestionRecordDO();
            recordDO.setExerciseMode(ExamModeEnum.AIZL.getCode());
            recordDO.setQuestionId(submitVO.getQuestionId());
            recordDO.setUserAnswer(submitVO.getAnswer());
            recordDO.setAccountId(accountId);
            recordDO.setSubjectCode(submitVO.getSubjectCode());
            recordDO.setCollectedAt(LocalDateTime.now());

            recordDO.setQuestionSetId(submitVO.getQuestionSetId());
            recordDO.setChapterCode(submitVO.getChapterCode());
            recordDO.setSectionCode(submitVO.getSectionCode());
            exerciseDomain.saveWrongQuestionRecord(recordDO);
        }

        // 返回结果
        return new SubmitAnswerResultVO(batchNum, isRight, question.getExplanationWithPrefix(), question.getAnswer());
    }

    public ExamPracticeInfoVO aiPracticeInfo(Long accountId, String batchNum, Long aiExerciseRecordId) {
        // 1. 调用ExerciseDomain练习领域获取当前用户当前批次号的练习记录列表, 列表根据创建时间倒序(传入参数账号 ID、批次号、练习模式)
        List<AIExerciseQuestionDO> aiExerciseQuestions = exerciseDomain.getAIExerciseQuestionsByRecordIdAndBatchNum(aiExerciseRecordId, batchNum);

        // 2. 返回的练习时间根据最后一次提交的时间和最早一次提交的时间计算
        if (CollectionUtils.isNotEmpty(aiExerciseQuestions)) {
            // 根据批次号
            BatchNumRecordDO batchNumRecord = exerciseDomain.getBatchNumRecord(accountId, batchNum);
            if (batchNumRecord == null) {
                throw new AIException(400, "根据batchNum未获取到对应的练习开始时间记录");
            }
            LocalDateTime startTime = batchNumRecord.getExerciseStartTime();
            LocalDateTime endTime = aiExerciseQuestions.get(0).getUpdateTime();
            Duration duration = Duration.between(startTime, endTime);
            String practiceTime = String.format("%02d:%02d:%02d", duration.toHours(), duration.toMinutesPart(), duration.toSecondsPart());

            long totalQuestionCount = aiExerciseQuestions.size();
            long rightQuestionCount = aiExerciseQuestions.stream().filter(AIExerciseQuestionDO::getIsRight).count();
            String accuracy = String.format("%.0f%%", rightQuestionCount * 100.0 / totalQuestionCount);
            long wrongQuestionCount = totalQuestionCount - rightQuestionCount;

            return new ExamPracticeInfoVO(true, accuracy, practiceTime, (int) wrongQuestionCount, (int) totalQuestionCount);
        } else {
            return new ExamPracticeInfoVO(false, "-", "00:00:00", 0, 0);
        }
    }

    public List<WrongQuestionVO> getWrongQuestions(Long accountId, String batchNum, Long aiExerciseRecordId) {
        List<AIExerciseQuestionDO> aiExerciseQuestions = exerciseDomain.getAIExerciseQuestionsByRecordIdAndBatchNum(aiExerciseRecordId, batchNum);
        List<AIExerciseQuestionDO> wrongExerciseQuestions = aiExerciseQuestions.stream().filter(x -> x.getIsRight() != null && !x.getIsRight()).collect(Collectors.toList());

        // 获取题目信息
        List<Long> questionIds = wrongExerciseQuestions.stream().map(AIExerciseQuestionDO::getQuestionId).collect(Collectors.toList());
        List<QuestionDO> questions = questionDomain.getQuestionsByQuestionIds(questionIds);

        // 将题目信息转换为Map，以便快速查找
        Map<Long, QuestionDO> questionMap = questions.stream().collect(Collectors.toMap(QuestionDO::getId, q -> q));

        // 组装返回结果
        return wrongExerciseQuestions.stream().map(exerciseQuestion -> {
            WrongQuestionVO vo = new WrongQuestionVO();
            vo.setQuestionId(exerciseQuestion.getQuestionId());
            vo.setUserAnswer(exerciseQuestion.getUserAnswer());
            vo.setQuestionSetId(exerciseQuestion.getQuestionSetId());
            QuestionDO question = questionMap.get(exerciseQuestion.getQuestionId());
            if (question != null) {
                vo.setQuestionContent(question.getContent());
                vo.setQuestionType(question.getTypeName());
                vo.setOptions(question.getOptions());
                vo.setRightAnswer(question.getAnswer());
                vo.setAnalysis(question.getExplanationWithPrefix());
            }
            return vo;
        }).collect(Collectors.toList());
    }

    public List<UserExamRecordlVO> getHistoryExamList(Long accountId, Long questionSetId) {
        List<UserExamRecordDO> examRecords = exerciseDomain.findExamRecordsByAccountIdAndQuestionSetIdOrderByUpdateTimeDesc(accountId, questionSetId);
        return examRecords.stream().map(userExamRecordMapper::toVO).collect(Collectors.toList());
    }
}