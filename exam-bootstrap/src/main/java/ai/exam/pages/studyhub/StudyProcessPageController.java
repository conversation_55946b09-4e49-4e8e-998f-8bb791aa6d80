package ai.exam.pages.studyhub;

import ai.exam.pages.studyhub.vo.StudyProcessVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/studyhub")
public class StudyProcessPageController {

    @Resource
    private StudyProcessPageApplication studyProcessPageApplication;

    @GetMapping("/learning-process")
    public StudyProcessVO getStudyProcessVO(Long accountId, @RequestParam String subjectCode) {
        return studyProcessPageApplication.getStudyProcess(accountId, subjectCode);
    }

    @PostMapping("/track_study_duration")
    public void trackStudyDuration(Long accountId) {
        studyProcessPageApplication.trackStudyDuration(accountId);
    }

    @GetMapping("/get_user_study_duration")
    public String getUserStudyDuration(Long accountId) {
        return studyProcessPageApplication.getUserStudyDuration(accountId);
    }

    @GetMapping("/get_user_exercise_questions_count")
    public Integer getUserExerciseQuestionsCount(Long accountId) {
        return studyProcessPageApplication.getUserExerciseQuestionsCount(accountId);
    }

}
