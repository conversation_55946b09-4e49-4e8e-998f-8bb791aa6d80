package ai.exam.pages.chapterquestion;

import ai.exam.pages.chapterquestion.vo.ArticleAndChapterAndSectionVO;
import ai.exam.pages.chapterquestion.vo.ChapterQuestionChapterVO;
import ai.exam.pages.chapterquestion.vo.ChapterQuestionSectionVO;
import ai.exam.pages.common.vo.BooleanResultVO;
import ai.exam.pages.knowledgeslicing.vo.ExamPracticeInfoVO;
import ai.exam.pages.knowledgeslicing.vo.ExamQuestionInfoVO;
import ai.exam.pages.knowledgeslicing.vo.SubmitAnswerResultVO;
import ai.exam.pages.knowledgeslicing.vo.SubmitStudyAnswerRequestVO;
import ai.exam.pages.practice.vo.WrongQuestionVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 章节题库控制层
 *
 * <AUTHOR>
 * @create 2024-07-14
 */
@RestController
@RequestMapping("/api/chapter-questions")
public class ChapterQuestionController {

    @Resource
    private ChapterQuestionApplication chapterQuestionApplication;

    @GetMapping("/articleAndChapterAndSections-real")
    public List<ArticleAndChapterAndSectionVO> getArticleAndChapterAndSectionsReal(Long accountId, @RequestParam String subjectCode) {
        return chapterQuestionApplication.getArticleAndChapterAndSectionsReal(accountId, subjectCode);
    }

    @GetMapping("/articleAndChapterAndSections")
    public List<ArticleAndChapterAndSectionVO> getArticleAndChapterAndSections(Long accountId, @RequestParam String subjectCode) {
        return chapterQuestionApplication.getArticleAndChapterAndSections(accountId, subjectCode);
    }
    //
    // @Deprecated
    // @GetMapping("/chapters")
    // public List<ChapterQuestionChapterVO> getChapters(Long accountId, @RequestParam String subjectCode) {
    //     return chapterQuestionApplication.getChapters(accountId, subjectCode);
    // }
    //
    // @Deprecated
    // @GetMapping("/chapters-real")
    // public List<ChapterQuestionChapterVO> getChaptersReal(Long accountId, @RequestParam String subjectCode) {
    //     return chapterQuestionApplication.getChaptersReal(accountId, subjectCode);
    // }
    //
    // @Deprecated
    // @GetMapping("/sections")
    // public List<ChapterQuestionSectionVO> getSections(Long accountId, @RequestParam String chapterCode) {
    //     return chapterQuestionApplication.getSectionsRepeat(accountId, chapterCode);
    // }
    //
    // @Deprecated
    // @GetMapping("/sections-real")
    // public List<ChapterQuestionSectionVO> getSectionsReal(Long accountId, @RequestParam String chapterCode) {
    //     return chapterQuestionApplication.getSectionsRepeatReal(accountId, chapterCode);
    // }

    @GetMapping("/examQuestionInfo")
    public ExamQuestionInfoVO getExamQuestionInfo(Long accountId, @RequestParam String subjectCode, @RequestParam String sectionCode, @RequestParam String chapterCode) {
        return chapterQuestionApplication.getExamQuestionInfo(accountId, subjectCode, sectionCode, chapterCode);
    }

    @GetMapping("/examQuestionInfo-real")
    public ExamQuestionInfoVO getExamQuestionInfoReal(Long accountId, @RequestParam String subjectCode, @RequestParam String sectionCode, @RequestParam String chapterCode) {
        return chapterQuestionApplication.getExamQuestionInfoReal(accountId, subjectCode, sectionCode, chapterCode);
    }

    @PostMapping("/submitAnswer")
    public SubmitAnswerResultVO submitAnswer(HttpServletRequest request, @RequestBody SubmitStudyAnswerRequestVO requestDTO) {
        Long accountId = (Long) request.getAttribute("accountId");
        return chapterQuestionApplication.submitAnswer(accountId, requestDTO);
    }

    @PostMapping("/submitAnswer-real")
    public SubmitAnswerResultVO submitAnswerReal(HttpServletRequest request, @RequestBody SubmitStudyAnswerRequestVO requestDTO) {
        Long accountId = (Long) request.getAttribute("accountId");
        return chapterQuestionApplication.submitAnswerReal(accountId, requestDTO);
    }

    @GetMapping("/examPracticeInfo")
    public ExamPracticeInfoVO getExamPracticeInfo(Long accountId, @RequestParam String batchNum, @RequestParam String sectionCode, @RequestParam String chapterCode) {
        return chapterQuestionApplication.getExamPracticeInfo(accountId, batchNum, sectionCode, chapterCode);
    }

    @GetMapping("/examPracticeInfo-real")
    public ExamPracticeInfoVO getExamPracticeInfoReal(Long accountId, @RequestParam String batchNum, @RequestParam String sectionCode, @RequestParam String chapterCode) {
        return chapterQuestionApplication.getExamPracticeInfoReal(accountId, batchNum, sectionCode, chapterCode);
    }

    @GetMapping("/wrongQuestions")
    public List<WrongQuestionVO> wrongQuestions(Long accountId, @RequestParam String batchNum, @RequestParam String sectionCode, @RequestParam String chapterCode) {
        return chapterQuestionApplication.wrongQuestions(accountId, batchNum, sectionCode, chapterCode);
    }

    @GetMapping("/wrongQuestions-real")
    public List<WrongQuestionVO> wrongQuestionsReal(Long accountId, @RequestParam String batchNum, @RequestParam String sectionCode, @RequestParam String chapterCode) {
        return chapterQuestionApplication.wrongQuestionsReal(accountId, batchNum, sectionCode, chapterCode);
    }

}
