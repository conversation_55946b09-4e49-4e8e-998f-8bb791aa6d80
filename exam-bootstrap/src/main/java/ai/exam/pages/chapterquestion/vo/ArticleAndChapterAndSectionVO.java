package ai.exam.pages.chapterquestion.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-11-19
 */
@Data
public class ArticleAndChapterAndSectionVO {

    // 代码
    private String code;

    // 篇名称
    private String name;

    // 总数
    private int totalCount;

    // 已做
    private int completedCount;

    private int correctCount;

    // 正确率
    private String accuracy = "-";

    /**
     * 类型：article/chapter/section
     */
    private String type;

    // 是否有权限
    private boolean access = true;

    private List<ArticleAndChapterAndSectionVO> children;
}