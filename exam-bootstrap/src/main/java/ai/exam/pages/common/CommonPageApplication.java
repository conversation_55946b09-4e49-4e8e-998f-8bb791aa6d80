package ai.exam.pages.common;

import ai.exam.common.util.GenerateBatchNumUtil;
import ai.exam.domain.certificate.CertificateDO;
import ai.exam.domain.certificate.CertificateDomain;
import ai.exam.domain.exercise.ExerciseDomain;
import ai.exam.domain.exercise.LocationInformationDO;
import ai.exam.domain.knowledge.ChapterDO;
import ai.exam.domain.knowledge.KnowledgeDomain;
import ai.exam.pages.common.mapper.LocationInformationVODOMapper;
import ai.exam.pages.common.vo.*;
import ai.exam.web.certificate.vo.CertificateWithMajorsVO;
import ai.exam.web.certificate.vo.MajorVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CommonPageApplication {

    @Resource
    private CertificateDomain certificateDomain;

    @Resource
    private KnowledgeDomain knowledgeDomain;

    @Resource
    private ExerciseDomain exerciseDomain;

    @Resource
    private LocationInformationVODOMapper locationInformationVODOMapper;

    public List<CertificateWithSubjectsVO> getCertificatesWithSubjects() {
        // 1. 调用CertificateDomain中的findAllCertificates方法, 获取所有的证书及证书下的科目列表信息
        List<CertificateDO> certificateDOList = certificateDomain.findAllCertificates(true);

        // 2. 将从CertificateDomain中获取的信息转换成方法的输出
        return certificateDOList.stream().map(certificateDO -> {
            CertificateWithSubjectsVO certificateDTO = new CertificateWithSubjectsVO();
            certificateDTO.setCertificateCode(certificateDO.getCertificateCode());
            certificateDTO.setCertificateName(certificateDO.getCertificateName());
            certificateDTO.setCertificateDesc(certificateDO.getCertificateDesc());

            List<SubjectVO> subjectDTOList = certificateDO.getSubjects().stream().map(subjectDO -> {
                SubjectVO subjectDTO = new SubjectVO();
                subjectDTO.setSubjectCode(subjectDO.getSubjectCode());
                subjectDTO.setSubjectName(subjectDO.getSubjectName());
                return subjectDTO;
            }).collect(Collectors.toList());

            certificateDTO.setSubjects(subjectDTOList);

            return certificateDTO;
        }).collect(Collectors.toList());
    }

    public List<ChapterVO> getChaptersBySubjectCode(String subjectCode) {
        // 1. 调用KnowledgeDomain中的findChaptersBySubjectCode方法, 获取章信息
        List<ChapterDO> chapterDOList = knowledgeDomain.findChaptersBySubjectCode(subjectCode, false);

        // 2. 将从KnowledgeDomain中获取的章信息转换为ChapterVO对象
        return chapterDOList.stream()
                .map(chapterDO -> new ChapterVO(chapterDO.getChapterCode(), chapterDO.getChapterName()))
                .collect(Collectors.toList());
    }

    public BooleanResultVO updateLocationInformation(Long accountId, LocationInformationVO locationInformationVO) {
        LocationInformationDO locationInformationDO = locationInformationVODOMapper.toLocationInformationDO(locationInformationVO);
        locationInformationDO.setAccountId(accountId);
        exerciseDomain.saveOrUpdateLocationInformation(locationInformationDO);
        return new BooleanResultVO(true);
    }

    public GenerateBatchNumResVO generateBatchNum(Long accountId, String exerciseMode) {
        String batchNum = GenerateBatchNumUtil.generateBatchNum(exerciseMode, accountId);
        // 调用 ExerciseDomain 保存批次号记录
        exerciseDomain.saveBatchNumRecord(accountId, exerciseMode, batchNum);
        return new GenerateBatchNumResVO(batchNum);
    }
}