package ai.exam.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
public class LogAspect {

    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Pointcut("execution(* ai.exam.pages.*.*Controller.*(..))")
    public void logPointcut() {
    }

    @Around("logPointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        objectMapper.registerModule(new JavaTimeModule());
        // 记录请求信息
        Map<String, Object> requestLog = new HashMap<>();
        requestLog.put("URL", request.getRequestURL().toString());
        requestLog.put("HTTP_METHOD", request.getMethod());
        requestLog.put("ARGS", getArgsMap(point));

        logger.info("Request: {}", objectMapper.writeValueAsString(requestLog));

        Object result = point.proceed();

        // 记录响应信息
        Map<String, Object> responseLog = new HashMap<>();
        responseLog.put("RESPONSE", result);
        responseLog.put("SPEND_TIME", System.currentTimeMillis() - beginTime);

        logger.info("Response: {}", objectMapper.writeValueAsString(responseLog));

        return result;
    }

    private Map<String, Object> getArgsMap(ProceedingJoinPoint point) {
        Map<String, Object> argsMap = new HashMap<>();
        MethodSignature signature = (MethodSignature) point.getSignature();
        String[] parameterNames = signature.getParameterNames();
        Object[] args = point.getArgs();

        for (int i = 0; i < parameterNames.length; i++) {
            if (args[i] instanceof HttpServletRequest) {
                argsMap.put(parameterNames[i], "HttpServletRequest");
            } else {
                try {
                    argsMap.put(parameterNames[i], objectMapper.writeValueAsString(args[i]));
                } catch (Exception e) {
                    argsMap.put(parameterNames[i], "Unserializable object: " + args[i].getClass().getSimpleName());
                }
            }
        }
        return argsMap;
    }
}