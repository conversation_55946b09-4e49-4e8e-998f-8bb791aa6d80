package ai.exam.mgt.pages.question.vo;

import ai.exam.mgt.pages.common.vo.PageReqVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 题库查询参数 VO 类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class QuestionSetQueryVO extends PageReqVO {

    /**
     * 科目代码列表
     */
    private List<String> subjectCodes;

    /**
     * 章代码列表
     */
    private List<String> chapterCodes;

    /**
     * 节代码列表
     */
    private List<String> sectionCodes;

    /**
     * 题库类型列表
     */
    private List<String> types;

    /**
     * 年份列表
     */
    private List<Integer> years;
}