package ai.exam.mgt.pages.oss;

import ai.exam.mgt.pages.oss.vo.ImageUploadRespVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * OSS 管理控制器
 */
@RestController
@RequestMapping("/mgt/oss")
public class MgtOSSController {

    @Resource
    private MgtOSSApplication mgtOSSApplication;

    /**
     * 上传图片
     *
     * @param file 图片文件
     * @return 图片访问 URL
     */
    @PostMapping("/image/upload")
    public ImageUploadRespVO uploadImage(@RequestParam("file") MultipartFile file) {
        return mgtOSSApplication.uploadImage(file);
    }
}