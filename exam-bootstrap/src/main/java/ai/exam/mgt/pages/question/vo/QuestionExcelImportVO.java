package ai.exam.mgt.pages.question.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionExcelImportVO {

    /**
     * 文件名
     */
    private String filename;

    /**
     * 题库类型
     */
    private String questionSetType;

    /**
     * 题库名称
     */
    private String questionSetName;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 解析的题目数据
     */
    private List<QuestionExcelParseVO> excelParseVOs;
}