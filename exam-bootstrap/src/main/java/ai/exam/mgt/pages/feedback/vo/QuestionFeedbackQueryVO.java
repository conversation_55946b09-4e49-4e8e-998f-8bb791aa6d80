package ai.exam.mgt.pages.feedback.vo;

import ai.exam.mgt.pages.common.vo.PageReqVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 题目反馈查询参数 VO 类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class QuestionFeedbackQueryVO extends PageReqVO {

    /**
     * 反馈 Id 列表
     */
    private List<Long> ids;

    /**
     * 题目 Id 列表
     */
    private List<Long> questionIds;

    /**
     * 证书代码列表
     */
    private List<String> certificateCodes;

    /**
     * 科目代码列表
     */
    private List<String> subjectCodes;

    /**
     * 状态列表：0-未处理 1-已处理
     */
    private List<Integer> statuses;

    /**
     * 反馈类型列表
     */
    private List<String> feedbackTypes;
} 