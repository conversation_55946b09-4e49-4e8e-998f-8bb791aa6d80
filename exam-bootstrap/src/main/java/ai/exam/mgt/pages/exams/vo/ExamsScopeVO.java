package ai.exam.mgt.pages.exams.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考试记分规则信息 VO 类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ExamsScopeVO {

	/**
	 * 考试记分规则 Id
	 */
	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建者
	 */
	private String createBy;

	/**
	 * 更新者
	 */
	private String updateBy;

	/**
	 * 证书代码
	 */
	private String certificateCode;

	/**
	 * 证书名称
	 */
	private String certificateName;

	/**
	 * 科目代码
	 */
	private String subjectCode;

	/**
	 * 科目名称
	 */
	private String subjectName;

	/**
	 * 考试时长（分钟）
	 */
	private Integer duration;

	/**
	 * 总分
	 */
	private Integer totalScore;

	/**
	 * 及格分数
	 */
	private Integer passingScore;

	/**
	 * 记分规则
	 */
	private List<ExamsScopeScopeRuleVO> scopeRules;
}