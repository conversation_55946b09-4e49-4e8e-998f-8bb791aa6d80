package ai.exam.mgt.pages.question.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入题目并创建题库的入参 VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionSetImportRequestVO {

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 证书名称。如果没有科目代码，则需要通过证书名称 + 科目名称，获取对应的 证书代码 + 科目代码
     */
    private String certificateName;

    /**
     * 科目名称。如果没有科目代码，则需要通过证书名称 + 科目名称，获取对应的 证书代码 + 科目代码
     */
    private String subjectName;

    /**
     * 题库类型。如果没有传，则从 Excel 文件名读取
     */
    private String questionSetType;

    /**
     * 题库名称。如果没有传，则从 Excel 文件名读取
     */
    private String questionSetName;

    /**
     * 题库年份，只有题库类型为历年真题才有。如果没有传，则从 Excel 文件名读取
     */
    private Integer questionSetYear;
}
