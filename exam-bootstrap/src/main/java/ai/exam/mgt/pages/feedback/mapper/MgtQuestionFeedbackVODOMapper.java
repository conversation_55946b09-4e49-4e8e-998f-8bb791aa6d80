package ai.exam.mgt.pages.feedback.mapper;

import ai.exam.domain.question.FeedbackFormDO;
import ai.exam.domain.question.FeedbackQuestionInfoDO;
import ai.exam.domain.question.MgtQuestionFeedbackQueryDO;
import ai.exam.domain.question.QuestionDO;
import ai.exam.domain.question.enums.QuestionType;
import ai.exam.mgt.pages.feedback.vo.ProcessFeedbackQuestionInfoVO;
import ai.exam.mgt.pages.feedback.vo.QuestionFeedbackQueryVO;
import ai.exam.mgt.pages.feedback.vo.QuestionFeedbackVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 题目反馈 VO 与 DO 转换映射器
 */
@Mapper(componentModel = "spring", imports = {
		QuestionType.class,
})
public interface MgtQuestionFeedbackVODOMapper {

	/**
	 * 转换查询参数
	 */
	MgtQuestionFeedbackQueryDO toMgtQuestionFeedbackQueryDO(QuestionFeedbackQueryVO queryVO);

	/**
	 * 转换 DO 为返回 VO
	 */
	@Mapping(target = "userPhone", ignore = true)
	@Mapping(target = "userNickname", ignore = true)
	@Mapping(target = "certificateName", ignore = true)
	@Mapping(target = "subjectName", ignore = true)
	QuestionFeedbackVO toQuestionFeedbackVO(FeedbackFormDO feedbackFormDO);

	/**
	 * 转换题目信息 VO 为 DO
	 */
	@Mapping(target = "type", expression = "java(QuestionType.valueOf(questionInfoVO.getType()))")
	QuestionDO toQuestionDO(ProcessFeedbackQuestionInfoVO questionInfoVO);

	/**
	 * 转换题目信息 DO 为反馈题目信息 DO
	 */
	@Mapping(target = "type", expression = "java(questionDO.getType().getName())")
	FeedbackQuestionInfoDO toFeedbackQuestionInfoDO(QuestionDO questionDO);
} 