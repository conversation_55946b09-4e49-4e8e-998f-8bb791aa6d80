package ai.exam.mgt.pages.user;

import ai.exam.auth.MgtJwtUtils;
import ai.exam.auth.MgtUserContextHolder;
import ai.exam.common.util.PasswordEncoderUtils;
import ai.exam.domain.account.PhoneVerificationCodeDomain;
import ai.exam.domain.mgt.PageRespDO;
import ai.exam.domain.mgt.user.MgtMenuDO;
import ai.exam.domain.mgt.user.MgtRoleDO;
import ai.exam.domain.mgt.user.MgtUserDO;
import ai.exam.domain.mgt.user.MgtUserDomain;
import ai.exam.domain.mgt.user.MgtUserQueryDO;
import ai.exam.mgt.pages.common.vo.PageRespVO;
import ai.exam.mgt.pages.user.mapper.MgtMenuVODOMapper;
import ai.exam.mgt.pages.user.mapper.MgtRoleVODOMapper;
import ai.exam.mgt.pages.user.mapper.MgtUserVODOMapper;
import ai.exam.mgt.pages.user.vo.*;
import ai.exam.web.account.SMSService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/10/20 17:22
 * @description 用户应用服务
 */
@Slf4j
@Component
public class MgtUserApplication {

    @Resource
    private MgtUserDomain mgtUserDomain;

    @Resource
    private PhoneVerificationCodeDomain phoneVerificationCodeDomain;

    @Resource
    private MgtUserVODOMapper mgtUserVODOMapper;

    @Resource
    private MgtRoleVODOMapper mgtRoleVODOMapper;

    @Resource
    private MgtMenuVODOMapper mgtMenuVODOMapper;

    /**
     * 分页查询用户信息
     *
     * @param queryVO 查询条件
     * @return 分页用户信息
     */
    public PageRespVO<MgtUserVO> pageQuery(MgtUserQueryVO queryVO) {
        // 将查询 VO 转换为 DO
        MgtUserQueryDO queryDO = mgtUserVODOMapper.toMgtUserQueryDO(queryVO);

        // 使用领域层执行分页查询
        PageRespDO<MgtUserDO> mgtUserDOPage = mgtUserDomain.pageQuery(queryDO);

        // 转换为 VO 并返回
        return mgtUserVODOMapper.toMgtUserVOPage(mgtUserDOPage);
    }

    /**
     * 创建用户
     *
     * @param createVO 用户信息
     * @return 用户 Id
     */
    public Long create(CreateMgtUserVO createVO) {
        // 获取当前用户名
        String username = MgtUserContextHolder.getUsername();

        // 转换为 DO 对象
        MgtUserDO mgtUserDO = mgtUserVODOMapper.toMgtUserDO(createVO);

        // 设置用户信息
        mgtUserDO.setCreateBy(username);
        mgtUserDO.setUpdateBy(username);
        // 设置初始状态为启用
        mgtUserDO.setStatus(1);
        // 密码加密
        mgtUserDO.setPassword(PasswordEncoderUtils.encode(createVO.getPassword()));
        mgtUserDO.setNickname(mgtUserDO.getNickname() == null ? StringUtils.EMPTY : mgtUserDO.getNickname());
        mgtUserDO.setAvatar(mgtUserDO.getAvatar() == null ? StringUtils.EMPTY : mgtUserDO.getAvatar());

        // 保存用户
        MgtUserDO savedUser = mgtUserDomain.saveUser(mgtUserDO, createVO.getRoleIds());

        return savedUser.getId();
    }

    /**
     * 更新用户信息
     *
     * @param id       用户 Id
     * @param updateVO 用户信息
     */
    public void update(Long id, UpdateMgtUserVO updateVO) {
        // 获取当前用户名
        String username = MgtUserContextHolder.getUsername();

        // 转换为 DO 对象
        MgtUserDO mgtUserDO = mgtUserVODOMapper.toMgtUserDO(updateVO);
        mgtUserDO.setId(id);
        mgtUserDO.setUpdateBy(username);

        // 如果有传密码，则加密
        if (StringUtils.isNotBlank(updateVO.getPassword())) {
            mgtUserDO.setPassword(PasswordEncoderUtils.encode(updateVO.getPassword()));
        }
        mgtUserDO.setNickname(mgtUserDO.getNickname() == null ? StringUtils.EMPTY : mgtUserDO.getNickname());
        mgtUserDO.setAvatar(mgtUserDO.getAvatar() == null ? StringUtils.EMPTY : mgtUserDO.getAvatar());

        // 更新用户
        mgtUserDomain.saveUser(mgtUserDO, updateVO.getRoleIds());
    }

    /**
     * 批量更新用户状态
     *
     * @param userIds          用户 Id 列表
     * @param targetStatus 目标状态
     */
    public void batchUpdateStatus(List<Long> userIds, Integer targetStatus) {
        String username = MgtUserContextHolder.getUsername();

        // 参数校验
        if (targetStatus != 0 && targetStatus != 1) {
            throw new IllegalArgumentException("非法的状态值");
        }

        // 执行批量更新
        mgtUserDomain.batchUpdateStatus(userIds, targetStatus, username);
    }

    /**
     * 批量删除用户
     *
     * @param userIds 用户 Id 列表
     */
    public void batchDelete(List<Long> userIds) {
        String username = MgtUserContextHolder.getUsername();

        // 执行批量删除
        mgtUserDomain.batchDelete(userIds, username);
    }

    /**
     * 根据用户 Id 查询用户信息（包含启用状态的角色信息）
     *
     * @param id 用户 Id
     * @return 用户信息
     */
    public MgtUserWithRolesVO getById(Long id) {
        // 查询用户基本信息
        MgtUserDO mgtUserDO = mgtUserDomain.getById(id);
        if (mgtUserDO == null) {
            return null;
        }

        return toUserWithRolesVO(mgtUserDO);
    }

    /**
     * 根据用户名查询用户信息（包含启用状态的角色信息）
     *
     * @param username 用户名
     * @return 用户信息
     */
    public MgtUserWithRolesVO getByUsername(String username) {
        // 查询用户基本信息
        MgtUserDO mgtUserDO = mgtUserDomain.getByUsername(username);
        if (mgtUserDO == null) {
            return null;
        }

        return toUserWithRolesVO(mgtUserDO);
    }

    /**
     * 根据手机号查询用户信息（包含启用状态的角色信息）
     *
     * @param phoneNumber 手机号
     * @return 用户信息
     */
    public MgtUserWithRolesVO getByPhoneNumber(String phoneNumber) {
        // 查询用户基本信息
        MgtUserDO mgtUserDO = mgtUserDomain.getByPhoneNumber(phoneNumber);
        if (mgtUserDO == null) {
            return null;
        }

        return toUserWithRolesVO(mgtUserDO);
    }

    /**
     * 将用户 DO 转换为包含启用状态角色的 VO
     *
     * @param userDO 用户 DO
     * @return 包含启用状态角色的用户 VO
     */
    private MgtUserWithRolesVO toUserWithRolesVO(MgtUserDO userDO) {
        // 转换为 VO
        MgtUserWithRolesVO userWithRolesVO = mgtUserVODOMapper.toMgtUserWithRolesVO(userDO);

        // 查询用户的启用状态角色
        List<MgtRoleDO> enabledRoles = mgtUserDomain.getUserEnabledRoles(userDO.getId());
        userWithRolesVO.setRoles(mgtRoleVODOMapper.toMgtRoleVOList(enabledRoles));

        return userWithRolesVO;
    }

    /**
     * 发送手机验证码
     * 该方法会校验手机号的有效性，并确保发送频率限制
     *
     * @param phoneNumber 手机号
     * @throws RuntimeException 当手机号无效或发送验证码失败时抛出
     */
    public void sendVerificationCode(String phoneNumber) {
        // 1. 验证手机号是否已注册
        MgtUserDO userDO = mgtUserDomain.getByPhoneNumber(phoneNumber);
        if (userDO == null) {
            throw new RuntimeException("该手机号未注册");
        }

        // 2. 检查用户状态
        if (userDO.getStatus() != 1) {
            throw new RuntimeException("该用户已被禁用");
        }

        // 3. 验证发送频率（防止短信轰炸）
        // TODO liuruiyu 实现发送频率限制，例如 1 分钟内最多发送 1 次，1 小时内最多发送 5 次

        try {
            // 4. 生成验证码
            String verificationCode = SMSService.generateRandomVerificationCode();

            // 5. 保存验证码到缓存
            phoneVerificationCodeDomain.saveVerificationCode(phoneNumber, verificationCode);

            // 6. 发验证码
            SMSService.sendVerificationCode(phoneNumber, verificationCode);

            log.info("验证码发送成功，手机号: {}, 验证码: {}", phoneNumber, verificationCode);
        } catch (Exception e) {
            log.error("验证码发送失败，手机号: {}, 错误信息: {}", phoneNumber, e.getMessage());
            throw new RuntimeException("验证码发送失败，请稍后重试");
        }
    }

    /**
     * 用户密码登录
     *
     * @param loginReqVO 登录请求参数，包含用户名和密码
     * @return 登录响应信息，包含用户信息和 token
     * @throws RuntimeException 当用户名或密码错误时抛出
     */
    public LoginRespVO loginByPassword(LoginReqVO loginReqVO) {
        MgtUserDO mgtUserDO = mgtUserDomain.getByUsername(loginReqVO.getUsername());
        if (mgtUserDO == null) {
            throw new RuntimeException("用户名错误");
        }
        if (mgtUserDO.getStatus() != 1) {
            throw new RuntimeException("该用户已被禁用");
        }
        if (!PasswordEncoderUtils.matches(loginReqVO.getPassword(), mgtUserDO.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        LoginRespVO loginRespVO = mgtUserVODOMapper.toLoginRespVO(mgtUserDO);
        loginRespVO.setToken(MgtJwtUtils.generateMgtUserToken(mgtUserDO));
        return loginRespVO;
    }

    /**
     * 用户手机号验证码登录
     *
     * @param loginReqVO 登录请求参数，包含手机号和验证码
     * @return 登录响应信息，包含用户信息和 token
     * @throws RuntimeException 当手机号错误或验证码错误时抛出
     */
    public LoginRespVO loginByPhoneNumber(LoginReqVO loginReqVO) {
        String phoneNumber = loginReqVO.getPhoneNumber();
        String verificationCode = loginReqVO.getVerificationCode();
        MgtUserDO mgtUserDO = mgtUserDomain.getByPhoneNumber(phoneNumber);
        if (mgtUserDO == null) {
            throw new RuntimeException("手机号错误");
        }
        if (mgtUserDO.getStatus() != 1) {
            throw new RuntimeException("该用户已被禁用");
        }
        if (!phoneVerificationCodeDomain.checkVerificationCode(phoneNumber, verificationCode)) {
            throw new RuntimeException("验证码错误或已过期");
        }
        LoginRespVO loginRespVO = mgtUserVODOMapper.toLoginRespVO(mgtUserDO);
        loginRespVO.setToken(MgtJwtUtils.generateMgtUserToken(mgtUserDO));
        return loginRespVO;
    }

    /**
     * 获取用户的所有启用状态的角色
     *
     * @param userId 用户 Id
     * @return 用户的启用状态角色列表
     */
    public List<MgtRoleVO> getUserEnabledRoles(Long userId) {
        List<MgtRoleDO> roleDOList = mgtUserDomain.getUserEnabledRoles(userId);
        return mgtRoleVODOMapper.toMgtRoleVOList(roleDOList);
    }

    /**
     * 获取用户的所有启用状态的菜单树
     *
     * @param userId 用户 Id
     * @return 用户的启用状态菜单树
     */
    public List<MgtMenuVO> getUserEnabledMenus(Long userId) {
        List<MgtMenuDO> menuDOList = mgtUserDomain.getUserEnabledMenus(userId);
        return mgtMenuVODOMapper.toMgtMenuVOList(menuDOList);
    }
}