package ai.exam.mgt.pages.user.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/10/22 10:00
 * @description
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MgtRoleVO {

    /**
     * 角色 Id
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 排序号
     */
    private Long sortNum;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
