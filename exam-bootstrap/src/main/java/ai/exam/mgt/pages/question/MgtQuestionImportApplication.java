package ai.exam.mgt.pages.question;

import ai.exam.auth.MgtUserContextHolder;
import ai.exam.common.util.QuestionContentUtils;
import ai.exam.domain.certificate.CertificateDomain;
import ai.exam.domain.certificate.SubjectDO;
import ai.exam.domain.question.*;
import ai.exam.domain.question.enums.QuestionType;
import ai.exam.mgt.pages.question.mapper.MgtQuestionVODOMapper;
import ai.exam.mgt.pages.question.vo.*;
import ai.exam.util.QuestionSetFilenameParserUtils;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/11/9 18:05
 * @description
 */
@Slf4j
@Component
public class MgtQuestionImportApplication {

    /**
     * Excel 列名映射
     */
    private static final Map<String, List<String>> COLUMN_MAPPINGS = Map.ofEntries(
            Map.entry("number", List.of("序号", "number")),
            Map.entry("type", List.of("试题类型", "题目类型", "类型", "type")),
            Map.entry("content", List.of("题干", "题目内容", "内容", "content")),
            Map.entry("answer", List.of("答案", "正确答案", "answer")),
            Map.entry("optionA", List.of("选项A", "选项A(勿删)", "optionA")),
            Map.entry("optionB", List.of("选项B", "选项B(勿删)", "optionB")),
            Map.entry("optionC", List.of("选项C", "选项C(勿删)", "optionC")),
            Map.entry("optionD", List.of("选项D", "选项D(勿删)", "optionD")),
            Map.entry("optionE", List.of("选项E", "选项E(勿删)", "optionE")),
            Map.entry("optionF", List.of("选项F", "选项F(勿删)", "optionF")),
            Map.entry("optionG", List.of("选项G", "选项G(勿删)", "optionG")),
            Map.entry("optionH", List.of("选项H", "选项H(勿删)", "optionH")),
            Map.entry("optionI", List.of("选项I", "选项I(勿删)", "optionI")),
            Map.entry("optionJ", List.of("选项J", "选项J(勿删)", "optionJ")),
            Map.entry("analysis", List.of("解析", "题目解析", "答案解析", "analysis")),
            Map.entry("sectionName", List.of("章节", "包含章节", "sectionName")),
            Map.entry("questionExternalId", List.of("题目ID", "questionExternalId")),
            Map.entry("questionChangeDate", List.of("更新时间", "questionChangeDate")),
            Map.entry("questionSetExternalId", List.of("题库ID", "questionSetExternalId"))
    );

    @Resource
    private QuestionDomain questionDomain;

    @Resource
    private ImportQuestionDomain importQuestionDomain;

    @Resource
    private CertificateDomain certificateDomain;

    @Resource
    private MgtQuestionVODOMapper mgtQuestionVODOMapper;

    /**
     * 导入题目
     *
     * @param subjectCode 科目代码
     * @param file        Excel 文件
     * @return 导入结果
     */
    @Transactional
    public QuestionExcelImportResultVO importQuestions(String subjectCode, MultipartFile file) {
        // 解析 Excel，表头是第一行（row 下标从 0 开始）
        QuestionExcelImportVO importVO = parseExcel(file, 0);

        return importQuestions( subjectCode, importVO);
    }

    /**
     * 导入题目并进行自动审核
     * @param subjectCode 科目代码
     * @param importVO 导入数据
     * @return 导入结果
     */
    private QuestionExcelImportResultVO importQuestions(String subjectCode,
                                                        QuestionExcelImportVO importVO) {
        // 参数校验
        Assert.hasText(subjectCode, "科目代码不能为空");
        Assert.notNull(importVO, "导入数据不能为空");
        Assert.notEmpty(importVO.getExcelParseVOs(), "导入题目不能为空");

        String username = MgtUserContextHolder.getUsername();

        try {
            // 1. 获取证书代码并构建题目数据
            String certificateCode = getCertificateCode(subjectCode);
            List<QuestionDO> questionDOs = mgtQuestionVODOMapper.toQuestionDOList(importVO.getExcelParseVOs());

            // 2. 设置题目基础信息
            questionDOs.forEach(question -> {
                question.setCertificateCode(certificateCode);
                question.setSubjectCode(subjectCode);
                question.setCreateBy(username);
                question.setUpdateBy(username);
            });

            // 3. 批量导入并自动审核题目
            List<QuestionDO> questions = questionDomain.createQuestions(questionDOs);
            List<Long> questionIds = questions.stream()
                    .map(QuestionDO::getId)
                    .collect(Collectors.toList());

            QuestionAutoApproveResultDO approveResult = questionDomain.autoApproveQuestions(
                    questionIds,
                    username,
                    true
            );

            // 4. 构建返回结果
            QuestionExcelImportResultVO result = new QuestionExcelImportResultVO();
            result.setAllImportQuestionIds(questionIds);
            result.setApprovedQuestionIds(approveResult.getApprovedQuestionIds());
            result.setFailedQuestionIds(approveResult.getFailedQuestionIds());

            return result;

        } catch (Exception e) {
            log.error("导入题目失败, subjectCode={}, username={}", subjectCode, username, e);
            throw new RuntimeException("导入题目失败: " + e.getMessage());
        }
    }

    /**
     * 解析 Excel 文件
     *
     * @param file            Excel 文件
     * @param headerRowCursor 标题 row 下标
     * @return Excel 导入 VO
     */
    public QuestionExcelImportVO parseExcel(MultipartFile file, Integer headerRowCursor) {
        String filename = Optional.ofNullable(file.getOriginalFilename())
                .filter(StringUtils::isNotBlank)
                .orElseThrow(() -> new IllegalArgumentException("文件名称不能为空"));

        QuestionSetFilenameParserUtils.ParseResult parseResult = QuestionSetFilenameParserUtils.parse(filename);

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // 解析标题行
            Row headerRow = sheet.getRow(headerRowCursor);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel 文件格式错误：缺少标题行");
            }

            List<String> headers = StreamSupport.stream(headerRow.spliterator(), false)
                    .map(this::getStringCellValue)
                    .toList();

            // 获取各列的索引
            Map<String, Integer> columnIndexes = findColumnIndexes(headers);

            // 解析数据行（从表头的下一行开始）
            List<QuestionExcelParseVO> questions = StreamSupport
                    .stream(sheet.spliterator(), false)
                    .skip(headerRowCursor + 1)
                    .map(row -> parseQuestionRow(row, columnIndexes))
                    .toList();

            // 数据行为空检查
            if (questions.isEmpty()) {
                throw new IllegalArgumentException("Excel 文件内容为空");
            }

            return QuestionExcelImportVO.builder()
                    .filename(filename)
                    .questionSetType(parseResult == null ? null : parseResult.questionSetType())
                    .questionSetName(QuestionSetFilenameParserUtils.processFilename(filename))
                    .year(parseResult == null ? null : parseResult.year())
                    .excelParseVOs(questions)
                    .build();

        } catch (IOException e) {
            log.error("解析 Excel 文件失败", e);
            throw new IllegalStateException("解析 Excel 文件失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("处理 Excel 数据失败", e);
            throw new IllegalStateException("处理 Excel 数据失败：" + e.getMessage());
        }
    }

    /**
     * 解析题目数据行
     */
    private QuestionExcelParseVO parseQuestionRow(Row row, Map<String, Integer> columnIndexes) {
        // 构建选项映射
        Map<String, String> options = IntStream.rangeClosed('A', 'J')
                .mapToObj(c -> String.valueOf((char) c))
                .filter(option -> columnIndexes.containsKey("option" + option))
                .map(option -> Map.entry(
                        option,
                        processOptionValue(getStringCellValue(row, columnIndexes.get("option" + option)))
                ))
                .filter(entry -> StringUtils.isNotBlank(entry.getValue()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));

        // 构建并返回题目对象
        String questionType = convertQuestionType(getStringCellValue(row, columnIndexes.get("type")));
        String answer = getStringCellValue(row, columnIndexes.get("answer"));

        if (Arrays.asList(QuestionType.CHOICE.getName(),
                        QuestionType.CHOICES.getName(),
                        QuestionType.INDEFINITE_CHOICE.getName())
                .contains(questionType)) {
            answer = answer.toUpperCase()
                    .chars()
                    .mapToObj(ch -> String.valueOf((char) ch))
                    .sorted()
                    .collect(Collectors.joining());
        }

        Integer questionContent = columnIndexes.get("content");

        return QuestionExcelParseVO.builder()
                .sectionName(getStringCellValue(row, columnIndexes.get("sectionName")))
                .number(getStringCellValue(row, columnIndexes.get("number")))
                .type(questionType)
                .content(QuestionContentUtils.removeYearRealExamsPrefix(getStringCellValue(row, questionContent)))
                .options(options)
                .answer(answer)
                .explanation(getStringCellValue(row, columnIndexes.get("analysis")))
                .caseAnalysisQuestion(null)
                .remark(null)
                .questionExternalId(getStringCellValue(row, columnIndexes.get("questionExternalId")))
                .questionChangeDate(getDateTimeCellValue(row, columnIndexes.get("questionChangeDate")))
                .questionSetExternalId(getStringCellValue(row, columnIndexes.get("questionSetExternalId")))
                .build();
    }

    /**
     * 查找列索引
     *
     * @param headers Excel 标题行
     * @return 字段名到列索引的映射
     */
    private Map<String, Integer> findColumnIndexes(List<String> headers) {
        return headers.stream()
                .collect(LinkedHashMap::new,
                        (indexMap, header) -> {
                            String trimHeader = header.replace("*", "").trim();
                            Optional<Map.Entry<String, List<String>>> first = COLUMN_MAPPINGS.entrySet().stream()
                                    .filter(entry ->
                                            entry.getValue().stream().anyMatch(v -> StringUtils.equals(v, trimHeader))
                                    )
                                    .findFirst();
                            first.ifPresent(entry -> indexMap.put(entry.getKey(), headers.indexOf(header)));
                        },
                        LinkedHashMap::putAll);
    }

    /**
     * 处理单元格可能为空的情况
     */
    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        try {
            return switch (cell.getCellType()) {
                case STRING -> StringUtils.trimToEmpty(cell.getStringCellValue());
                case NUMERIC -> {
                    if (DateUtil.isCellDateFormatted(cell)) {
                        yield formatDateCell(cell);
                    }
                    // 防止科学计数法
                    yield new BigDecimal(String.valueOf(cell.getNumericCellValue()))
                            .stripTrailingZeros()
                            .toPlainString();
                }
                case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
                case FORMULA -> {
                    try {
                        // 先尝试获取字符串
                        yield StringUtils.trimToEmpty(cell.getStringCellValue());
                    } catch (Exception e) {
                        // 如果是数值,防止科学计数法
                        yield new BigDecimal(String.valueOf(cell.getNumericCellValue()))
                                .stripTrailingZeros()
                                .toPlainString();
                    }
                }
                case ERROR -> throw new IllegalArgumentException("单元格数据错误");
                default -> "";
            };
        } catch (Exception e) {
            log.warn("解析单元格内容失败", e);
            return "";
        }
    }

    /**
     * 统一格式化日期为 yyyy-MM-dd HH:mm:ss
     */
    private String formatDateCell(Cell cell) {
        try {
            LocalDateTime dateTime = cell.getLocalDateTimeCellValue();
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            log.warn("格式化日期失败", e);
            return "";
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getStringCellValue(Row row, Integer cellNum) {
        if (cellNum == null || cellNum < 0) {
            return null;
        }
        return getStringCellValue(row.getCell(cellNum));
    }

    /**
     * 获取单元格日期时间值
     */
    private LocalDateTime getDateTimeCellValue(Row row, Integer cellNum) {
        if (cellNum == null || cellNum < 0) {
            return null;
        }

        String dateStr = getStringCellValue(row, cellNum);
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * 转换题目类型
     */
    private String convertQuestionType(String excelType) {
        if (StringUtils.isBlank(excelType)) {
            return null;
        }
        return switch (excelType) {
            case "单选题" -> QuestionType.CHOICE.getName();
            case "多选题" -> QuestionType.CHOICES.getName();
            case "不定项选择题" -> QuestionType.INDEFINITE_CHOICE.getName();
            case "判断题" -> QuestionType.TRUE_FALSE.getName();
            case "主观题" -> QuestionType.OPEN.getName();
            default -> null;
        };
    }

    private String getCertificateCode(String subjectCode) {
        List<SubjectDO> subjects = certificateDomain.findSubjectsByCodes(List.of(subjectCode));
        if (CollectionUtils.isEmpty(subjects)) {
            log.error("科目代码有误, subjectCode: {}", subjectCode);
            throw new IllegalStateException("科目代码有误：" + subjectCode);
        }
        return subjects.get(0).getCertificateCode();
    }

    /**
     * 处理选项内容，去除选项前缀
     *
     * @param value 原始选项内容
     * @return 处理后的选项内容
     */
    private String processOptionValue(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        // 去除 A. B. C. 等前缀，支持点号和、号
        return value.replaceFirst("^[A-J][.、]\\s*", "").trim();
    }

    public QuestionAutoApproveResultVO autoApproveQuestions(BatchAutoApproveQuestionVO autoApproveVO) {
        // 获取当前用户名
        String username = MgtUserContextHolder.getUsername();

        // 调用领域层执行自动审核
        QuestionAutoApproveResultDO resultDO = questionDomain.autoApproveQuestions(
                autoApproveVO.getQuestionIds(),
                username,
                autoApproveVO.getNeedRepeatCheck()
        );

        // 转换返回结果
        QuestionAutoApproveResultVO resultVO = new QuestionAutoApproveResultVO();
        if (resultDO != null) {
            // 设置审核成功的题目 ID 列表
            resultVO.setApprovedQuestionIds(resultDO.getApprovedQuestionIds());

            // 设置审核失败的题目 ID 列表
            resultVO.setFailedQuestionIds(resultDO.getFailedQuestionIds());

            // 设置审核成功数量
            resultVO.setSuccessCount(CollectionUtils.size(resultDO.getApprovedQuestionIds()));
        }

        return resultVO;
    }
}
