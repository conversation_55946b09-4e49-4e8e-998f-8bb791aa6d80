package ai.exam.mgt.pages.certificate;

import ai.exam.mgt.pages.certificate.vo.CertificateSubjectsVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 证书科目管理控制器
 */
@RestController
@RequestMapping("/mgt/certificate-subject")
public class MgtCertificateSubjectController {

	@Resource
	private MgtCertificateSubjectApplication mgtCertificateSubjectApplication;

	/**
	 * 查询所有证书及其科目列表
	 *
	 * @return 证书及其科目列表
	 */
	@GetMapping("/list")
	public List<CertificateSubjectsVO> getCertificatesWithSubjects() {
		return mgtCertificateSubjectApplication.getCertificatesWithSubjects();
	}
}