package ai.exam.mgt.pages.knowledge;

import ai.exam.mgt.pages.knowledge.vo.KnowledgeNodeVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识点管理控制器
 */
@RestController
@RequestMapping("/mgt/knowledge")
public class MgtKnowledgeController {

	@Resource
	private MgtKnowledgeApplication mgtKnowledgeApplication;

	/**
	 * 根据科目代码查询知识点层级
	 *
	 * @param subjectCode 科目代码
	 * @return 知识点层级列表
	 */
	@GetMapping("/hierarchy")
	public List<KnowledgeNodeVO> getKnowledgeHierarchy(@RequestParam String subjectCode) {
		return mgtKnowledgeApplication.getKnowledgeHierarchy(subjectCode);
	}
}