package ai.exam.mgt.pages.question.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Set;

/**
 * 题目绑定题库请求 VO
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionSetBindVO {

    /**
     * 题目 Id
     */
    @NotNull(message = "题目 Id 不能为空")
    private Long questionId;

    /**
     * 待绑定的题库 Id 列表
     */
    @NotEmpty(message = "题库 Id 列表不能为空")
    @Size(max = 500, message = "单次批量操作不能超过 500 条")
    private Set<Long> questionSetIds;
}