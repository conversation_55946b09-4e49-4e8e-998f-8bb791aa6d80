package ai.exam.mgt.pages.user.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/10/20 18:40
 * @description
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MgtUserVO {

    /**
     * 用户 Id
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 头像 URL
     */
    private String avatar;

    /**
     * 用户状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 用户是否关联指定角色
     */
    private Boolean inTargetRole;
}
