package ai.exam.mgt.pages.certificate.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 证书科目返回 VO
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CertificateSubjectsVO {

	/**
	 * 证书代码
	 */
	private String certificateCode;

	/**
	 * 证书名称
	 */
	private String certificateName;

	/**
	 * 科目列表
	 */
	private List<SubjectVO> subjects;
}