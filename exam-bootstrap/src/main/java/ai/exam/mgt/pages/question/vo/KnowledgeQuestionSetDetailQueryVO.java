package ai.exam.mgt.pages.question.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 知识点层级（篇章节）题库查询请求参数
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class KnowledgeQuestionSetDetailQueryVO {

    /**
     * 科目代码
     */
    @NotBlank(message = "科目代码不能为空")
    private String subjectCode;

    /**
     * 题库类型
     */
    @NotBlank(message = "题库类型不能为空")
    private String type;

    /**
     * 篇代码
     */
    private String articleCode;

    /**
     * 章代码
     */
    private String chapterCode;

    /**
     * 节代码
     */
    private String sectionCode;
}