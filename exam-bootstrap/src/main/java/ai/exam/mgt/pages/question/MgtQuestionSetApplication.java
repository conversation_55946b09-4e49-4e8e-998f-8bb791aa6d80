package ai.exam.mgt.pages.question;

import ai.exam.auth.MgtUserContextHolder;
import ai.exam.domain.certificate.CertificateDO;
import ai.exam.domain.certificate.CertificateDomain;
import ai.exam.domain.certificate.SubjectDO;
import ai.exam.domain.knowledge.*;
import ai.exam.domain.knowledge.enums.KnowledgeNodeType;
import ai.exam.domain.mgt.PageRespDO;
import ai.exam.domain.question.MgtQuestionSetQuestionQueryDO;
import ai.exam.domain.question.MgtQuestionSetQueryDO;
import ai.exam.domain.question.QuestionDO;
import ai.exam.domain.question.QuestionDomain;
import ai.exam.domain.question.QuestionSetDO;
import ai.exam.domain.question.QuestionSetDomain;
import ai.exam.domain.question.enums.QuestionSetType;
import ai.exam.mgt.pages.common.vo.PageRespVO;
import ai.exam.mgt.pages.question.mapper.MgtQuestionSetVODOMapper;
import ai.exam.mgt.pages.question.vo.*;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 题库管理应用服务
 */
@SuppressWarnings("DuplicatedCode")
@Slf4j
@Component
public class MgtQuestionSetApplication {

    private final static int MAX_BATCH_SIZE = 500;

    @Resource
    private QuestionSetDomain questionSetDomain;

    @Resource
    private QuestionDomain questionDomain;

    @Resource
    private CertificateDomain certificateDomain;

    @Resource
    private KnowledgeDomain knowledgeDomain;

    @Resource
    private MgtQuestionSetVODOMapper mgtQuestionSetVODOMapper;

    @Resource
    private MgtQuestionApplication mgtQuestionApplication;

    /**
     * 查询题库详情信息
     *
     * @param id 题库 Id
     * @return 题库详情信息
     */
    public QuestionSetVO getDetail(Long id) {
        // 查询题库信息
        QuestionSetDO questionSetDO = questionSetDomain.getQuestionSetDetail(id);
        if (questionSetDO == null) {
            return null;
        }

        // 转换为 VO 并补充证书名称和科目名称
        QuestionSetVO vo = mgtQuestionSetVODOMapper.toQuestionSetVO(questionSetDO);
        fillCertificateAndSubjectName(Collections.singletonList(vo));

        return vo;
    }

    /**
     * 根据科目代码和知识点层级查询题库详情
     *
     * @param queryVO 查询参数
     * @return 题库详情
     */
    public QuestionSetVO getDetailByKnowledge(KnowledgeQuestionSetDetailQueryVO queryVO) {
        // 1. 查询已有题库
        String username = MgtUserContextHolder.getUsername();
        List<QuestionSetDO> existingQuestionSets = questionSetDomain.findQuestionSetsByKnowledge(
                mgtQuestionSetVODOMapper.toKnowledgeQuestionSetDetailQueryDO(queryVO)
        );

        QuestionSetDO questionSetDO;
        if (existingQuestionSets.isEmpty()) {
            // 2. 如果没有题库则创建
            questionSetDO = createKnowledgeQuestionSet(queryVO, username);
        } else {
            // 3. 如果有多个题库，取最早创建的一个并打印日志
            questionSetDO = existingQuestionSets.get(0);
            if (existingQuestionSets.size() > 1) {
                log.warn("查询到多个题库，取最早创建的一个返回。题库 Id: {}, 名称: {}",
                        questionSetDO.getId(), questionSetDO.getName());
            }
        }

        // 4. 转换为 VO 并补充名称
        QuestionSetVO vo = mgtQuestionSetVODOMapper.toQuestionSetVO(questionSetDO);
        fillCertificateAndSubjectName(List.of(vo));
        return vo;
    }

    /**
     * 创建知识点层级（篇章节）题库
     */
    private QuestionSetDO createKnowledgeQuestionSet(
            KnowledgeQuestionSetDetailQueryVO queryVO,
            String username
    ) {
        // 1. 获取知识点层级
        List<KnowledgeNodeDO> hierarchy = knowledgeDomain.getKnowledgeHierarchy(queryVO.getSubjectCode());

        // 2. 构建知识点代码到节点的映射
        Map<String, KnowledgeNodeDO> nodeMap = KnowledgeNodeUtils.buildKnowledgeNodeMap(hierarchy);

        // 3. 确定当前层级的节点和代码
        String articleCode = queryVO.getArticleCode();
        String chapterCode = queryVO.getChapterCode();
        String sectionCode = queryVO.getSectionCode();
        String name = "";

        // 4. 处理不同层级的情况
        if (StringUtils.isNotBlank(sectionCode)) {
            // 节级别
            KnowledgeNodeDO section = nodeMap.get(sectionCode);
            chapterCode = Optional.ofNullable(section).map(KnowledgeNodeDO::getParentCode).orElse("");
            KnowledgeNodeDO chapter = nodeMap.get(chapterCode);
            articleCode = Optional.ofNullable(chapter).map(KnowledgeNodeDO::getParentCode).orElse("");
            name = Optional.ofNullable(section).map(KnowledgeNodeDO::getName).orElse("");
        } else if (StringUtils.isNotBlank(chapterCode)) {
            // 章级别
            KnowledgeNodeDO chapter = nodeMap.get(chapterCode);
            articleCode = Optional.ofNullable(chapter).map(KnowledgeNodeDO::getParentCode).orElse("");
            name = Optional.ofNullable(chapter).map(KnowledgeNodeDO::getName).orElse("");
        } else if (StringUtils.isNotBlank(articleCode)) {
            // 篇级别
            KnowledgeNodeDO article = nodeMap.get(articleCode);
            name = Optional.ofNullable(article).map(KnowledgeNodeDO::getName).orElse("");
        }

        // 5. 拼接题库类型到名称
        if (StringUtils.isNotBlank(name)) {
            name = QuestionSetType.valueOf(queryVO.getType()).getDescription() + "-" + name;
        } else {
            name = QuestionSetType.valueOf(queryVO.getType()).getDescription();
        }

        // 6. 构建题库对象
        QuestionSetDO questionSetDO = new QuestionSetDO();
        questionSetDO.setName(name);
        questionSetDO.setCertificateCode(getCertificateCode(queryVO.getSubjectCode()));
        questionSetDO.setSubjectCode(queryVO.getSubjectCode());
        questionSetDO.setType(QuestionSetType.valueOf(queryVO.getType()));
        questionSetDO.setArticleCode(StringUtils.defaultIfBlank(articleCode, StringUtils.EMPTY));
        questionSetDO.setChapterCode(StringUtils.defaultIfBlank(chapterCode, StringUtils.EMPTY));
        questionSetDO.setSectionCode(StringUtils.defaultIfBlank(sectionCode, StringUtils.EMPTY));
        questionSetDO.setYear(-1);
        questionSetDO.setCreateBy(username);
        questionSetDO.setUpdateBy(username);

        // 7. 保存并返回题库
        return questionSetDomain.createQuestionSet(questionSetDO);
    }

    /**
     * 分页查询题库信息
     *
     * @param queryVO 查询条件
     * @return 分页题库信息
     */
    public PageRespVO<QuestionSetVO> pageQuery(QuestionSetQueryVO queryVO) {
        // 将查询 VO 转换为 DO
        MgtQuestionSetQueryDO queryDO = mgtQuestionSetVODOMapper.toMgtQuestionSetQueryDO(queryVO);

        // 使用领域层执行分页查询
        PageRespDO<QuestionSetDO> questionSetDOPage = questionSetDomain.pageQuery(queryDO);
        if (questionSetDOPage == null) {
            return PageRespVO.ofEmpty();
        }

        return getQuestionSetVOPageRespVO(questionSetDOPage);
    }

    private PageRespVO<QuestionSetVO> getQuestionSetVOPageRespVO(PageRespDO<QuestionSetDO> questionSetDOPage) {
        // 转换列表
        List<QuestionSetVO> voList = CollectionUtils.isEmpty(questionSetDOPage.getList()) ? new ArrayList<>()
                : questionSetDOPage.getList().stream()
                .map(mgtQuestionSetVODOMapper::toQuestionSetVO)
                .collect(Collectors.toList());

        // 补充证书名称和科目名称
        fillCertificateAndSubjectName(voList);

        // 构建并返回分页结果
        return PageRespVO.of(voList, questionSetDOPage.getTotal(), questionSetDOPage.getHasNext());
    }

    /**
     * 分页查询题库下的题目
     *
     * @param queryVO 查询条件
     * @return 分页题目信息
     */
    public PageRespVO<QuestionVO> pageQueryQuestions(QuestionSetQuestionQueryVO queryVO) {
        // 题库 Id 列表为空时，根据其他条件查询题库 Id
        if (CollectionUtils.isEmpty(queryVO.getQuestionSetIds())) {
            List<Long> questionSetIds = questionSetDomain.findQuestionSetIds(
                    queryVO.getChapterCode(),
                    queryVO.getSectionCode(),
                    queryVO.getQuestionSetType()
            );
            if (CollectionUtils.isEmpty(questionSetIds)) {
                return PageRespVO.ofEmpty();
            }
            queryVO.setQuestionSetIds(questionSetIds);
        }

        // 将查询 VO 转换为 DO
        MgtQuestionSetQuestionQueryDO queryDO = mgtQuestionSetVODOMapper.toMgtQuestionSetQuestionQueryDO(queryVO);

        // 使用领域层执行分页查询
        PageRespDO<QuestionDO> questionDOPage = questionDomain.pageQueryQuestionSetQuestions(queryDO);
        if (questionDOPage == null) {
            return PageRespVO.ofEmpty();
        }

        return mgtQuestionApplication.getQuestionVOPageRespVO(questionDOPage);
    }

    /**
     * 创建题库
     *
     * @param createVO 题库信息
     * @return 题库 Id
     */
    public Long create(CreateQuestionSetVO createVO) {
        // 获取当前用户名
        String username = MgtUserContextHolder.getUsername();

        // 转换为 DO 对象
        QuestionSetDO questionSetDO = mgtQuestionSetVODOMapper.toQuestionSetDO(createVO);
        questionSetDO.setYear(ObjectUtils.defaultIfNull(createVO.getYear(), -1));
        questionSetDO.setArticleCode(StringUtils.defaultIfBlank(questionSetDO.getArticleCode(), StringUtils.EMPTY));
        questionSetDO.setChapterCode(StringUtils.defaultIfBlank(questionSetDO.getChapterCode(), StringUtils.EMPTY));
        questionSetDO.setSectionCode(StringUtils.defaultIfBlank(questionSetDO.getSectionCode(), StringUtils.EMPTY));
        questionSetDO.setCreateBy(username);
        questionSetDO.setUpdateBy(username);

        // 创建题库
        QuestionSetDO savedQuestionSet = questionSetDomain.createQuestionSet(questionSetDO);

        // 如果有需要绑定的题目，校验题目状态并添加关联
        List<Long> questionIds = createVO.getQuestionIds();
        if (CollectionUtils.isNotEmpty(questionIds)) {
            try {
                questionSetDomain.addQuestionsToQuestionSet(
                        savedQuestionSet.getId(),
                        new HashSet<>(questionIds),
                        username
                );
            } catch (Exception e) {
                log.error("题库绑定题目失败, id: {}", savedQuestionSet.getId(), e);
                throw new IllegalStateException("题库创建成功，但绑定题目失败：" + e.getMessage());
            }
        }

        return savedQuestionSet.getId();
    }

    /**
     * 更新题库信息
     *
     * @param id       题库 Id
     * @param updateVO 题库信息
     */
    public void update(Long id, UpdateQuestionSetVO updateVO) {
        // 获取当前用户名
        String username = MgtUserContextHolder.getUsername();

        // 转换为 DO 对象
        QuestionSetDO questionSetDO = mgtQuestionSetVODOMapper.toQuestionSetDO(updateVO);
        questionSetDO.setId(id);
        questionSetDO.setUpdateBy(username);

        // 更新题库
        QuestionSetDO updateQuestionSetDO = questionSetDomain.updateQuestionSet(questionSetDO);

        // 处理题目绑定关系
        List<Long> addQuestionIds = updateVO.getAddQuestionIds();
        List<Long> removeQuestionIds = updateVO.getRemoveQuestionIds();
        if (Boolean.TRUE.equals(updateQuestionSetDO.getMockExamsSupport())) {
            if (CollectionUtils.isNotEmpty(addQuestionIds) || CollectionUtils.isNotEmpty(removeQuestionIds)) {
                throw new IllegalStateException("已开启试卷模式，不能进行题目绑定/解绑，需要先关闭试卷");
            }
            return;
        }

        // 添加新题目
        if (CollectionUtils.isNotEmpty(addQuestionIds)) {
            try {
                questionSetDomain.addQuestionsToQuestionSet(
                        id,
                        new HashSet<>(addQuestionIds),
                        username
                );
            } catch (Exception e) {
                log.error("题库绑定题目失败, id: {}", id, e);
                throw new IllegalStateException("绑定题目失败：" + e.getMessage());
            }
        }

        // 移除题目
        if (CollectionUtils.isNotEmpty(removeQuestionIds)) {
            try {
                questionSetDomain.removeQuestionsFromQuestionSet(
                        id,
                        new HashSet<>(removeQuestionIds),
                        username
                );
            } catch (Exception e) {
                log.error("题库解绑题目失败, id: {}", id, e);
                throw new IllegalStateException("解绑题目失败：" + e.getMessage());
            }
        }
    }

    /**
     * 批量删除题库
     *
     * @param ids 题库 Id 列表
     */
    public void batchDelete(List<Long> ids) {
        String username = MgtUserContextHolder.getUsername();

        // 参数校验
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("题库 Id 列表不能为空");
        }
        if (ids.size() > MAX_BATCH_SIZE) {
            throw new IllegalArgumentException("单次批量操作不能超过 " + MAX_BATCH_SIZE + " 条");
        }

        // 执行批量删除
        questionSetDomain.batchDelete(ids, username);
    }

    /**
     * 设置题库为试卷
     *
     * @param id 题库 Id
     */
    public void setAsExam(Long id) {
        String username = MgtUserContextHolder.getUsername();

        // 设置为试卷
        questionSetDomain.setAsExam(id, username);
    }

    /**
     * 设置题库为非试卷
     *
     * @param id 题库 Id
     */
    public void setAsNonExam(Long id) {
        String username = MgtUserContextHolder.getUsername();

        // 设置为非试卷
        questionSetDomain.setAsNonExam(id, username);
    }

    /**
     * 根据科目代码获取证书代码
     *
     * @param subjectCode 科目代码
     * @return 证书代码
     */
    private String getCertificateCode(String subjectCode) {
        List<SubjectDO> subjects = certificateDomain.findSubjectsByCodes(Collections.singletonList(subjectCode));
        return subjects.isEmpty() ? "" : subjects.get(0).getCertificateCode();
    }

    /**
     * 补充证书名称和科目名称
     */
    private void fillCertificateAndSubjectName(List<QuestionSetVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }

        // 查询所有证书及其科目信息
        List<CertificateDO> certificatesWithSubjects = certificateDomain.findAllCertificatesWithSubjects();
        // 构建证书代码到证书名称的映射
        Map<String, String> certificateCodeToName = getCertificateCodeToName(certificatesWithSubjects);
        // 构建科目代码到科目名称的映射
        Map<String, String> subjectCodeToName = getSubjectCodeToName(certificatesWithSubjects);

        // 补充名称
        for (QuestionSetVO vo : voList) {
            vo.setCertificateName(certificateCodeToName.get(vo.getCertificateCode()));
            vo.setSubjectName(subjectCodeToName.get(vo.getSubjectCode()));
        }
    }

    private Map<String, String> getSubjectCodeToName(List<CertificateDO> certificatesWithSubjects) {
        return CollectionUtils.isEmpty(certificatesWithSubjects) ? Maps.newHashMap()
                : certificatesWithSubjects.stream()
                .flatMap(certificate -> certificate.getSubjects().stream())
                .collect(Collectors.toMap(SubjectDO::getSubjectCode, SubjectDO::getSubjectName));
    }

    private Map<String, String> getCertificateCodeToName(List<CertificateDO> certificatesWithSubjects) {
        return CollectionUtils.isEmpty(certificatesWithSubjects) ? Maps.newHashMap()
                : certificatesWithSubjects.stream()
                .collect(Collectors.toMap(CertificateDO::getCertificateCode, CertificateDO::getCertificateName));
    }

    /**
     * 查询并创建知识点题库
     *
     * @param queryVO 查询参数
     * @return 知识点题库树形结构
     */
    public List<KnowledgeQuestionSetNodeVO> queryAndCreateKnowledgeQuestionSet(
            QueryAndCreateKnowledgeQuestionSetVO queryVO) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 参数校验
            try {
                QuestionSetType type = QuestionSetType.valueOf(queryVO.getQuestionSetType());
                if (!QuestionSetType.isChapQuestionSetType(type)) {
                    throw new IllegalArgumentException("题库类型必须是章节练习或章节真题");
                }
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("无效的题库类型");
            }

            // 2. 获取知识点层级
            List<KnowledgeNodeDO> hierarchy = knowledgeDomain.getKnowledgeHierarchy(queryVO.getSubjectCode());
            if (CollectionUtils.isEmpty(hierarchy)) {
                return Collections.emptyList();
            }

            // 3. 组装待创建的题库信息
            List<QuestionSetDO> questionSetDOs = buildQuestionSetDOs(
                    hierarchy,
                    queryVO.getSubjectCode(),
                    queryVO.getQuestionSetType()
            );

            // 4. 获取或创建题库
            List<QuestionSetDO> createdQuestionSets = questionSetDomain.getOrCreateChapterQuestionSets(questionSetDOs);

            // 5. 构建不同层级的题库 VO 映射
            Map<String, QuestionSetVO> questionSetVOByArticleCode = new HashMap<>();
            Map<String, QuestionSetVO> questionSetVOByChapterCode = new HashMap<>();
            Map<String, QuestionSetVO> questionSetVOBySectionCode = new HashMap<>();

            for (QuestionSetDO questionSet : createdQuestionSets) {
                QuestionSetVO questionSetVO = mgtQuestionSetVODOMapper.toQuestionSetVO(questionSet);
                if (StringUtils.isNotBlank(questionSet.getSectionCode())) {
                    questionSetVOBySectionCode.put(questionSet.getSectionCode(), questionSetVO);
                } else if (StringUtils.isNotBlank(questionSet.getChapterCode())) {
                    questionSetVOByChapterCode.put(questionSet.getChapterCode(), questionSetVO);
                } else if (StringUtils.isNotBlank(questionSet.getArticleCode())) {
                    questionSetVOByArticleCode.put(questionSet.getArticleCode(), questionSetVO);
                }
            }

            // 6. 转换为树形结构
            return buildKnowledgeQuestionSetTree(
                    hierarchy,
                    questionSetVOByArticleCode,
                    questionSetVOByChapterCode,
                    questionSetVOBySectionCode
            );
        } finally {
            // 打印处理耗时
            long endTime = System.currentTimeMillis();
            log.info("查询并创建知识点题库完成，科目代码：{}，题库类型：{}，处理耗时：{}ms",
                    queryVO.getSubjectCode(),
                    queryVO.getQuestionSetType(),
                    endTime - startTime);
        }
    }

    /**
     * 组装待创建的题库信息
     *
     * @param nodes           知识点节点列表
     * @param subjectCode     科目代码
     * @param questionSetType 题库类型
     * @return 题库 DO 列表
     */
    private List<QuestionSetDO> buildQuestionSetDOs(
            List<KnowledgeNodeDO> nodes,
            String subjectCode,
            String questionSetType) {
        String username = MgtUserContextHolder.getUsername();
        String certificateCode = getCertificateCode(subjectCode);
        Map<String, KnowledgeNodeDO> nodeMap = KnowledgeNodeUtils.buildKnowledgeNodeMap(nodes);
        List<QuestionSetDO> questionSetDOs = new ArrayList<>();

        // 递归处理所有节点
        processNodes(nodes, nodeMap, subjectCode, certificateCode, questionSetType, username, questionSetDOs);

        return questionSetDOs;
    }

    /**
     * 递归处理节点
     */
    private void processNodes(
            List<KnowledgeNodeDO> nodes,
            Map<String, KnowledgeNodeDO> nodeMap,
            String subjectCode,
            String certificateCode,
            String questionSetType,
            String username,
            List<QuestionSetDO> questionSetDOs) {

        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }

        for (KnowledgeNodeDO node : nodes) {
            // 只处理叶子节点
            if (CollectionUtils.isEmpty(node.getChildren())) {
                QuestionSetDO questionSetDO = new QuestionSetDO();
                questionSetDO.setCertificateCode(certificateCode);
                questionSetDO.setSubjectCode(subjectCode);
                questionSetDO.setType(QuestionSetType.valueOf(questionSetType));
                questionSetDO.setYear(-1);
                questionSetDO.setCreateBy(username);
                questionSetDO.setUpdateBy(username);

                // 设置默认空值
                questionSetDO.setArticleCode(StringUtils.EMPTY);
                questionSetDO.setChapterCode(StringUtils.EMPTY);
                questionSetDO.setSectionCode(StringUtils.EMPTY);

                // 根据节点类型设置对应的代码
                KnowledgeNodeType nodeType = KnowledgeNodeType.fromValue(node.getType());
                switch (nodeType) {
                    case SECTION -> {
                        questionSetDO.setSectionCode(node.getCode());
                        // 获取章代码
                        String chapterCode = Optional.ofNullable(node.getParentCode())
                                .orElse(StringUtils.EMPTY);
                        if (StringUtils.isNotBlank(chapterCode)) {
                            questionSetDO.setChapterCode(chapterCode);
                            // 获取篇代码
                            String articleCode = Optional.ofNullable(nodeMap.get(chapterCode))
                                    .map(KnowledgeNodeDO::getParentCode)
                                    .orElse(StringUtils.EMPTY);
                            questionSetDO.setArticleCode(articleCode);
                        }
                    }
                    case CHAPTER -> {
                        questionSetDO.setChapterCode(node.getCode());
                        // 获取篇代码
                        String articleCode = Optional.ofNullable(node.getParentCode())
                                .orElse(StringUtils.EMPTY);
                        questionSetDO.setArticleCode(articleCode);
                    }
                    case ARTICLE -> questionSetDO.setArticleCode(node.getCode());
                }

                // 设置题库名称
                String name = Optional.ofNullable(nodeMap.get(node.getCode()))
                        .map(KnowledgeNodeDO::getName)
                        .orElse(StringUtils.EMPTY);
                if (StringUtils.isNotBlank(name)) {
                    questionSetDO.setName(QuestionSetType.valueOf(questionSetType).getDescription() + "-" + name);
                } else {
                    questionSetDO.setName(QuestionSetType.valueOf(questionSetType).getDescription());
                }

                questionSetDOs.add(questionSetDO);
            }

            // 递归处理子节点
            if (CollectionUtils.isNotEmpty(node.getChildren())) {
                processNodes(
                        node.getChildren(),
                        nodeMap,
                        subjectCode,
                        certificateCode,
                        questionSetType,
                        username,
                        questionSetDOs
                );
            }
        }
    }

    /**
     * 构建知识点题库树形结构
     *
     * @param nodes                      知识点节点列表
     * @param questionSetVOByArticleCode 篇代码到题库 VO 的映射
     * @param questionSetVOByChapterCode 章代码到题库 VO 的映射
     * @param questionSetVOBySectionCode 节代码到题库 VO 的映射
     * @return 知识点题库树形结构
     */
    private List<KnowledgeQuestionSetNodeVO> buildKnowledgeQuestionSetTree(
            List<KnowledgeNodeDO> nodes,
            Map<String, QuestionSetVO> questionSetVOByArticleCode,
            Map<String, QuestionSetVO> questionSetVOByChapterCode,
            Map<String, QuestionSetVO> questionSetVOBySectionCode) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }

        List<KnowledgeQuestionSetNodeVO> result = new ArrayList<>();
        for (KnowledgeNodeDO node : nodes) {
            KnowledgeQuestionSetNodeVO nodeVO = mgtQuestionSetVODOMapper.toKnowledgeQuestionSetNodeVO(node);

            // 根据节点类型设置对应的题库信息
            KnowledgeNodeType nodeType = KnowledgeNodeType.fromValue(node.getType());
            switch (nodeType) {
                case SECTION -> nodeVO.setQuestionSet(questionSetVOBySectionCode.get(node.getCode()));
                case CHAPTER -> nodeVO.setQuestionSet(questionSetVOByChapterCode.get(node.getCode()));
                case ARTICLE -> nodeVO.setQuestionSet(questionSetVOByArticleCode.get(node.getCode()));
            }

            // 递归处理子节点
            if (CollectionUtils.isNotEmpty(node.getChildren())) {
                nodeVO.setChildren(buildKnowledgeQuestionSetTree(
                        node.getChildren(),
                        questionSetVOByArticleCode,
                        questionSetVOByChapterCode,
                        questionSetVOBySectionCode
                ));
            }

            result.add(nodeVO);
        }

        return result;
    }

    /**
     * 查询题目关联的题库列表
     *
     * @param questionId 题目 Id
     * @return 题库列表
     */
    public List<QuestionSetVO> getQuestionSetsByQuestionId(Long questionId) {
        List<QuestionSetDO> questionSetDOs = questionSetDomain.getQuestionSetsByQuestionId(questionId);
        return mgtQuestionSetVODOMapper.toQuestionSetVOList(questionSetDOs);
    }

    /**
     * 为题目绑定题库
     *
     * @param bindVO 绑定请求参数
     */
    public void bindQuestionSets(QuestionSetBindVO bindVO) {
        String username = MgtUserContextHolder.getUsername();
        questionSetDomain.bindQuestionSets(bindVO.getQuestionId(), bindVO.getQuestionSetIds(), username);
    }

    /**
     * 为题目解绑题库
     *
     * @param unbindVO 解绑请求参数
     */
    public void unbindQuestionSets(QuestionSetUnbindVO unbindVO) {
        String username = MgtUserContextHolder.getUsername();
        questionSetDomain.unbindQuestionSets(unbindVO.getQuestionId(), unbindVO.getQuestionSetIds(), username);
    }
}