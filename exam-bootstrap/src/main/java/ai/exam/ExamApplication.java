package ai.exam;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@EnableScheduling
@SpringBootApplication(scanBasePackages = "ai.exam")
//@EnableAspectJAutoProxy
public class ExamApplication {

    static {
        clearProxySettings();
    }

    private static void clearProxySettings() {
        String[] proxyProperties = {
            "http.proxyPort",
            "https.proxyHost",
            "https.proxyPort",
            "http.nonProxyHosts",
            "socksProxyHost",
            "socksProxyPort"
        };

        for (String property : proxyProperties) {
            System.clearProperty(property);
        }
    }

    public static void main(String[] args) {

        SpringApplication.run(ExamApplication.class, args);
    }

}
