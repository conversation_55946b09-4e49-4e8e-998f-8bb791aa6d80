package ai.exam;

import ai.exam.domain.excel.ExcelField;
import lombok.Data;

@Data
public class QuestionExcelRow {
    @ExcelField(order = 1)
    private int id;

    @ExcelField(order = 2)
    private String questionType;

    @ExcelField(order = 3)
    private String questionName;

    @ExcelField(order = 4)
    private String questionAnswer;

    @ExcelField(order = 5)
    private String questionComplexity;

    @ExcelField(order = 6)
    private String questionAnalysis;

    @ExcelField(order = 7)
    private String questionOptionA;

    @ExcelField(order = 8)
    private String questionOptionB;

    @ExcelField(order = 9)
    private String questionOptionC;

    @ExcelField(order = 10)
    private String questionOptionD;

    @ExcelField(order = 11)
    private String questionOptionE;

    @ExcelField(order = 12)
    private String questionOptionF;

    @ExcelField(order = 13)
    private String questionOptionG;

    @ExcelField(order = 14)
    private String questionOptionH;

    @ExcelField(order = 15)
    private String questionOptionI;

    @ExcelField(order = 16)
    private String remark;

    @ExcelField(order = 17)
    private String questionNameError;

    @ExcelField(order = 18)
    private String questionAnalysisError;
}
