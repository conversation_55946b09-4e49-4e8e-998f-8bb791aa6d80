package ai.exam.domain.certificate.repository;

import ai.exam.domain.certificate.po.SubjectEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SubjectRepository extends JpaRepository<SubjectEntity, Long> {

    List<SubjectEntity> findByCertificateCode(String certificateCode);

    List<SubjectEntity> findByCertificateCodeAndDelFlag(String certificateCode, Integer delFlag);

    /**
     * 根据科目代码列表和删除标志查询科目
     *
     * @param subjectCodes 科目代码列表
     * @param delFlag      删除标志
     * @return 科目实体列表
     */
    List<SubjectEntity> findBySubjectCodeInAndDelFlag(List<String> subjectCodes, Integer delFlag);

    SubjectEntity findBySubjectCodeAndDelFlag(String subjectCode, Integer delFlag);
}