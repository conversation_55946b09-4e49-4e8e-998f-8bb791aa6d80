package ai.exam.domain.certificate;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CertificateDO {
    private Long id;
    private String certificateCode; // 证书代码，格式为YZ
    private String certificateName; // 证书名称
    private String certificateDesc; // 证书描述
    private List<SubjectDO> subjects; // 证书包含的科目列表
    private Integer createBy; // 创建人
    private LocalDateTime createdAt; // 创建时间
    private Integer updateBy; // 更新人
    private LocalDateTime updatedAt; // 更新时间
    private Integer delFlag; // 删除标识 0存在 其他删除
    private String remark; // 备注
    private List<MajorDO> majors; // 证书包含的专业列表
}

