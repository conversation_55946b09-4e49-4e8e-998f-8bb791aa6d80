package ai.exam.domain.diagnosis;

import ai.exam.domain.question.QuestionDO;

import java.util.List;

public interface DiagnosisDomain {
    List<QuestionDO> getDiagnosisQuestions(Long accountId, String subjectCode, List<String> masteredChapterCodes);
    String getDiagnosisResult(Long accountId, String subjectCode, List<QuestionAnswerDO> userAnswers);

    UserDiagnosisDO saveOrUpdateUserDiagnosis(UserDiagnosisDO userDiagnosisDO);

    UserDiagnosisDO getUserDiagnosisByAccountIdAndSubjectCode(Long accountId, String subjectCode);
}

