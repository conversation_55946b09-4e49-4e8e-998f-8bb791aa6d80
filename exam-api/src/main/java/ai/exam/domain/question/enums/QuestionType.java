package ai.exam.domain.question.enums;

import java.util.Arrays;

public enum QuestionType {
    CHOICE("CHOICE", 1, "单选题"),
    CHOICES("CHOICES", 2, "多选题"),
    INDEFINITE_CHOICE("INDEFINITE_CHOICE", 3, "不定项选择题"),
    TRUE_FALSE("TRUE_FALSE", 4, "判断题"),
    OPEN("OPEN", 5, "简答题"),
    CASE_ANALYSIS_ESSAY("CASE_ANALYSIS_ESSAY", 6, "案例分析-简答题"),
    CASE_ANALYSIS_CHOICES("CASE_ANALYSIS_CHOICES", 7, "案例分析-不定项选择题"),
    ;

    private final String name;
    private final String description;

    private final Integer order;

    QuestionType(String name, Integer order, String description) {
        this.name = name;
        this.order = order;
        this.description = description;
    }

    public static QuestionType getInstance(String name) {
        return Arrays.stream(QuestionType.values()).filter(x -> x.getName().equals(name)).findFirst().orElseThrow(() -> new NullPointerException("QuestionType.name传入值在枚举类中不存在"));
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Integer getOrder() {
        return order;
    }
}

