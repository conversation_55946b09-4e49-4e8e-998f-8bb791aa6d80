package ai.exam.domain.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 考试记分规则
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ExamsScopeScopeRuleDO {
	/**
	 * 题目类型
	 */
	private String questionType;

	/**
	 * 起始题号
	 */
	private Integer startNumber;

	/**
	 * 结束题号
	 */
	private Integer endNumber;

	/**
	 * 部分答对得分
	 */
	private BigDecimal partialScore;

	/**
	 * 全部答对得分
	 */
	private BigDecimal fullScore;

	/**
	 * 题目总数量
	 */
	private Integer questionCount;

	/**
	 * 需要答题数量
	 */
	private Integer requiredAnswerCount;
}