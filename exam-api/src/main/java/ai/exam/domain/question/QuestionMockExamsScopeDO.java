package ai.exam.domain.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/11/6 23:20
 * @description 题目库模拟考试记分规则
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionMockExamsScopeDO {

	/**
	 * 考试时长（分钟）
	 */
	private Integer duration;

	/**
	 * 总分
	 */
	private Integer totalScore;

	/**
	 * 及格分数
	 */
	private Integer passingScore;

	/**
	 * 记分规则
	 */
	private List<ExamsScopeScopeRuleDO> scopeRules;
}
