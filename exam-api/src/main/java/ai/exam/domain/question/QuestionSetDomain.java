package ai.exam.domain.question;

import ai.exam.domain.mgt.PageRespDO;

import java.util.List;
import java.util.Set;

/**
 * 题库领域服务接口
 */
public interface QuestionSetDomain {

    /**
     * 查询题库详情
     *
     * @param id 题库 Id
     * @return 题库详情信息
     */
    QuestionSetDO getQuestionSetDetail(Long id);

    /**
     * 创建题库
     *
     * @param questionSetDO 题库信息
     * @return 创建的题库信息
     */
    QuestionSetDO createQuestionSet(QuestionSetDO questionSetDO);

    /**
     * 更新题库信息
     *
     * @param questionSetDO 题库信息
     * @return 更新后的题库信息
     */
    QuestionSetDO updateQuestionSet(QuestionSetDO questionSetDO);

    /**
     * 批量删除题库
     *
     * @param ids      题库 Id 列表
     * @param updateBy 操作人
     */
    void batchDelete(List<Long> ids, String updateBy);

    /**
     * 查询题库信息
     *
     * @param id 题库 Id
     * @return 题库信息
     */
    QuestionSetDO getQuestionSet(Long id);

    /**
     * 添加题目到题库
     *
     * @param questionSetId 题库 Id
     * @param questionIds   题目 Id 列表
     * @param updateBy      操作人
     */
    void addQuestionsToQuestionSet(Long questionSetId, Set<Long> questionIds, String updateBy);

    /**
     * 从题库移除题目
     *
     * @param questionSetId 题库 Id
     * @param questionIds   题目 Id 列表
     * @param updateBy      操作人
     */
    void removeQuestionsFromQuestionSet(Long questionSetId, Set<Long> questionIds, String updateBy);

    /**
     * 设置题库为试卷
     *
     * @param questionSetId 题库 Id
     * @param updateBy      操作人
     */
    void setAsExam(Long questionSetId, String updateBy);

    /**
     * 设置题库为非试卷
     *
     * @param questionSetId 题库 Id
     * @param updateBy      操作人
     */
    void setAsNonExam(Long questionSetId, String updateBy);

    /**
     * 分页查询题库
     *
     * @param queryDO 查询条件
     * @return 分页结果
     */
    PageRespDO<QuestionSetDO> pageQuery(MgtQuestionSetQueryDO queryDO);

    /**
     * 根据条件查询题库 Id 列表
     *
     * @param chapterCode 章代码
     * @param sectionCode 节代码
     * @param type        题库类型
     * @return 题库 Id 列表
     */
    List<Long> findQuestionSetIds(String chapterCode, String sectionCode, String type);

    List<QuestionSetDO> findQuestionSetInfoByQuestionSetIds(List<Long> questionSetIds);

    /**
     * 根据知识点层级查询题库列表
     *
     * @param queryDO 查询条件
     * @return 题库列表，按创建时间升序排序
     */
    List<QuestionSetDO> findQuestionSetsByKnowledge(KnowledgeQuestionSetDetailQueryDO queryDO);

    /**
     * 根据科目代码、题库类型和外部 ID 查询题库列表
     *
     * @param subjectCode 学科代码
     * @param type        题库类型
     * @param externalId  外部 ID
     * @return 题库列表
     */
    List<QuestionSetDO> findQuestionSetsByExternalId(String subjectCode, String type, String externalId);

    /**
     * 获取或创建章节题库
     * <p>
     * 如果题库已存在则直接返回，不会重复创建
     *
     * @param questionSetDOs 待创建的题库列表
     * @return 题库列表
     */
    List<QuestionSetDO> getOrCreateChapterQuestionSets(List<QuestionSetDO> questionSetDOs);

    /**
     * 查询题目关联的题库列表
     *
     * @param questionId 题目 Id
     * @return 题库列表
     */
    List<QuestionSetDO> getQuestionSetsByQuestionId(Long questionId);

    /**
     * 为题目绑定题库
     *
     * @param questionId 题目 Id
     * @param questionSetIds 待绑定的题库 Id 列表
     * @param updateBy 操作人
     */
    void bindQuestionSets(Long questionId, Set<Long> questionSetIds, String updateBy);

    /**
     * 为题目解绑题库
     *
     * @param questionId 题目 Id
     * @param questionSetIds 待解绑的题库 Id 列表
     * @param updateBy 操作人
     */
    void unbindQuestionSets(Long questionId, Set<Long> questionSetIds, String updateBy);
}