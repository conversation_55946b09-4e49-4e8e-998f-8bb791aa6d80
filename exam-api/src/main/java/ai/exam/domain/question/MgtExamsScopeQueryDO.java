package ai.exam.domain.question;

import ai.exam.domain.mgt.PageReqDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 考试记分规则查询参数 DO 类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MgtExamsScopeQueryDO extends PageReqDO {

	/**
	 * 考试记分规则 Id 列表
	 */
	private List<Long> ids;

	/**
	 * 证书代码列表
	 */
	private List<String> certificateCodes;

	/**
	 * 科目代码列表
	 */
	private List<String> subjectCodes;
}