package ai.exam.domain.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 案例分析题子题目 DO
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SubQuestionInfoDO {

    /**
     * 题目内容
     */
    private String content;

    /**
     * 选项
     */
    private Map<String, String> options;

    /**
     * 答案
     */
    private String answer;

    /**
     * 解析
     */
    private String explanation;
}