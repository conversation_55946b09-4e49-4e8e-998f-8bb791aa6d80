package ai.exam.domain.question.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 题目状态枚举
 */
@Getter
@AllArgsConstructor
public enum QuestionStatus {

    PENDING_SUBMIT(0, "待提交审核"),
    REVIEWING(1, "审核中"),
    APPROVED(2, "审核通过"),
    REJECTED(3, "审核不通过");

    /**
     * 状态值
     */
    private final Integer value;
    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态值获取枚举实例
     *
     * @param value 状态值
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果找不到对应的枚举值
     */
    public static QuestionStatus fromValue(Integer value) {
        return Optional.ofNullable(value)
                .flatMap(val -> Arrays.stream(QuestionStatus.values())
                        .filter(status -> status.value.equals(val))
                        .findFirst())
                .orElseThrow(() -> new IllegalArgumentException("未知的题目状态值: " + value));
    }
}
