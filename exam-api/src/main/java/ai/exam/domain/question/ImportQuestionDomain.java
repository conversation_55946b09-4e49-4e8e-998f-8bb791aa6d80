package ai.exam.domain.question;

import java.util.List;
import java.util.Set;

/**
 * 导入题目领域接口
 */
public interface ImportQuestionDomain {

    /**
     * 批量导入题目
     *
     * @param questionDOs 待导入的题目列表
     * @return 导入结果，包含新建、更新和未变更的题目列表
     */
    ImportQuestionResultDO importQuestions(List<QuestionDO> questionDOs);

    /**
     * 添加题目到题库，如果题目已存在关联关系则更新
     * 只校验题目是否存在，不校验审核状态
     *
     * @param questionSetId 题库 Id
     * @param questionIds 题目 Id 集合
     * @param updateBy 操作人
     */
    void addQuestionsToQuestionSet(Long questionSetId, Set<Long> questionIds, String updateBy);
}
