package ai.exam.domain.question;

import ai.exam.domain.mgt.PageReqDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Set;

/**
 * 题目查询参数 DO 类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MgtQuestionQueryDO extends PageReqDO {

    /**
     * 题目 Id 列表
     */
    private List<Long> ids;

    /**
     * 证书代码列表
     */
    private List<String> certificateCodes;

    /**
     * 科目代码列表
     */
    private List<String> subjectCodes;

    /**
     * 题目类型列表
     */
    private List<String> types;

    /**
     * 题目内容（模糊查询）
     */
    private String content;

    /**
     * 状态列表：0-待提交审核、1-审核中、2-审核通过、3-审核不通过
     */
    private List<Integer> statuses;

    /**
     * 题库类型列表
     */
    private List<String> questionSetTypes;

    /**
     * 题库 Id 列表
     */
    private Set<Long> questionSetIds;

    /**
     * 需要排除的题库 Id 列表
     */
    private Set<Long> excludeQuestionSetIds;

    /**
     * 指定题库 ID，判断题目是否关联此题库
     */
    private Long targetQuestionSetId;
}