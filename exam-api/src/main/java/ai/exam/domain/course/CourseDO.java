package ai.exam.domain.course;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CourseDO {
    private Long id;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Boolean delFlag = false;

    private String name;
    private String description;
    private Long teacherId;
    private String videoLink;
    private Long videoDuration;
    private String handoutLink;
}