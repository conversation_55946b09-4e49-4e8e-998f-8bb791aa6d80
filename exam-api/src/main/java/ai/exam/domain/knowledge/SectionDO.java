package ai.exam.domain.knowledge;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SectionDO {
    private Long id;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 更新者
     */
    private String updateBy;
    private String sectionCode; // 节代码，格式为YZ-GL-CHAP01-SEC01
    private String sectionName; // 节名称
    /**
     * 排序号，值越小越靠前
     */
    private Integer sortNum;
    private String chapterCode; // 所属章代码
    private List<Long> sliceIds; // 知识切片ID列表
    private List<Long> questionIds; // 题目ID列表
    private Integer delFlag; // 删除标识 0存在 其他删除
    private String remark; // 备注
    private QuestionSumDO questionSumDO; // 题目汇总信息
}

