package ai.exam.domain.knowledge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 知识点层级类型
 */
@Getter
@AllArgsConstructor
public enum KnowledgeNodeType {

	ARTICLE("article", "篇"),
	CHAPTER("chapter", "章"),
	SECTION("section", "节");

	/**
	 * 状态值
	 */
	private final String value;
	/**
	 * 状态描述
	 */
	private final String description;

	/**
	 * 根据类型值获取枚举实例
	 *
	 * @param value 类型值
	 * @return 对应的枚举实例
	 * @throws IllegalArgumentException 如果找不到对应的枚举值
	 */
	public static KnowledgeNodeType fromValue(String value) {
		return Optional.ofNullable(value)
				.flatMap(val -> Arrays.stream(KnowledgeNodeType.values())
						.filter(type -> type.value.equals(val))
						.findFirst())
				.orElseThrow(() -> new IllegalArgumentException("未知的知识点层级类型值: " + value));
	}
}