package ai.exam.domain.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProductTypeEnum {

    /**
     * 用于打包商品 / 嵌套商品
     */
    PRODUCT("PRODUCT", "商品"),

    COURSE_PACKAGE("COURSE_PACKAGE", "课程包"),

    COURSE("COURSE", "课程"),

    QUESTION_SET("QUESTION_SET", "题库"),
    ;

    private final String code;
    private final String description;

}
