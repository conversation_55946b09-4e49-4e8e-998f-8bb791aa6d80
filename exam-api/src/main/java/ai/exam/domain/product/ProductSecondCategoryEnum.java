package ai.exam.domain.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProductSecondCategoryEnum {

    ZHUAN_YE_QUAN_KE("专业全科", 100),
    GONG_GONG_DAN_KE("公共单科", 200),
    ZHUAN_YE_DAN_KE("专业单科", 300),
    ;

    private final String value;
    private final int sort;

    public static int getSortByValue(String secondCategory) {
        for (ProductSecondCategoryEnum value : values()) {
            if (value.getValue().equals(secondCategory)) {
                return value.getSort();
            }
        }
        return Integer.MAX_VALUE;
    }
}
/*

 */