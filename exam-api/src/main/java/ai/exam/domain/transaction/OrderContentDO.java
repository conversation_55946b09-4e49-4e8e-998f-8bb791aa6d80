package ai.exam.domain.transaction;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderContentDO {
    private Long id;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Boolean delFlag;

    private Long orderId;

    private Long productId;
    private BigDecimal price;
    private Integer quantity;
    private String productName;
    private String productDescription;
    private String certificateCode;
    private String majorCode;
    private String subjectCode;
    private String coverImage;
    private String productType;
    private String secondCategory;
    private String tags;
    private Boolean premium;
    private Boolean popular;
    private Boolean freeTrial;
    private String difficulty;
    private String learningFormat;
    private Integer validityPeriod;
    private BigDecimal originalPrice;
    private String detailImages;
    private String couponConditions;
    private String purchaseInstructions;
    private Long videoDuration;
}