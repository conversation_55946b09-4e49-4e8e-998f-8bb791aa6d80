package ai.exam.domain.transaction;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderDO {
    private Long id;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Boolean delFlag;

    private String orderNo;
    private Long userId;
    private BigDecimal totalPrice;
    private BigDecimal discountAmount;
    private BigDecimal paymentAmount;
    private String paymentMethod;
    private LocalDateTime paymentTime;
    private String orderStatus;
    private LocalDateTime expirationTime;
    private Long couponId;
    private String refundStatus;
    private LocalDateTime refundTime;
    private BigDecimal refundAmount;
    private String remark;

    public boolean isTimeout() {
        return LocalDateTime.now().isAfter(expirationTime);
    }
}