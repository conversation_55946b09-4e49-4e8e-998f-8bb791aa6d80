package ai.exam.domain.transaction;

import java.util.List;

public interface OrderDomain {

    /**
     * 创建订单
     *
     * @param orderDO         订单信息
     * @param orderContentDOs 订单内容信息
     */
    void createOrder(OrderDO orderDO, List<OrderContentDO> orderContentDOs);

    /**
     * 调起支付
     */
    String createPayment(OrderDO orderDO);

    /**
     * 查询支付状态
     */
    void queryPaymentStatus(OrderDO orderDO);

    /**
     * 获取所有待支付订单
     */
    List<OrderDO> getAllPendingPaymentOrders();

    /**
     * 更新订单状态
     */
    void updateOrderStatus(OrderDO orderDO);

    /**
     * 获取订单内容
     */
    List<OrderContentDO> getOrderContents(String orderNo);

    /**
     * 获取订单状态
     */
    String getOrderStatus(String orderNo);

    /**
     * 根据用户ID获取订单列表
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    List<OrderDO> getOrdersByUserId(Long userId);

    /**
     * 根据订单ID批量获取订单内容
     */
    List<OrderContentDO> batchGetOrderContentsByOrderIds(List<Long> orderIds);

    /**
     * 根据订单号获取订单信息
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    OrderDO getOrderByOrderNo(String orderNo);
}