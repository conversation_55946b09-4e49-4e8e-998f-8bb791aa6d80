#!/bin/bash

# 设置输入文件名
# 此文件中包含本次要更新的多份文件的完整内容，每份文件的内容以文件路径注释行（如 `// /path/to/file.java`）开头
INPUT_FILE="updated-file-code.java.txt"

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo "错误：输入文件 $INPUT_FILE 不存在。"
    exit 1
fi

# 初始化变量
current_file=""

# 读取输入文件并处理每个代码块
while IFS= read -r line || [ -n "$line" ]; do
    # 检查是否是文件路径注释行（以 // ./ 开头）
    if [[ $line == "// /"* ]]; then
        # 提取文件路径
        file_path=$(echo "$line" | sed 's/^\/\/ //')

        # 创建目录（如果不存在）
        mkdir -p "$(dirname "$file_path")"

        # 更新当前文件路径
        current_file="$file_path"

        echo "处理文件：$current_file"

        # 清空或创建文件
        > "$current_file"
    elif [ -n "$current_file" ]; then
        # 将当前行（包括空行）写入到当前文件
        echo "$line" >> "$current_file"
    fi
done < "$INPUT_FILE"

echo "代码更新完成。"
