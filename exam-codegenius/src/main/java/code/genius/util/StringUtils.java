package code.genius.util;

import java.util.List;

public class StringUtils {
    public static String removeSubstrings(String str, List<int[]> intervals) {
        StringBuilder sb = new StringBuilder(str);
        // 按照区间的起始位置从大到小排序
        intervals.sort((a, b) -> Integer.compare(b[0], a[0]));

        for (int[] interval : intervals) {
            int begin = interval[0];
            int end = interval[1];
            if (begin >= 0 && end < sb.length() && begin <= end) {
                sb.delete(begin, end + 1);
            }
        }
        return sb.toString();
    }

}
