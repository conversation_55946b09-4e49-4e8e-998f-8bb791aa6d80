package code.genius.gencode;


import org.eclipse.jdt.core.JavaCore;
import org.eclipse.jdt.core.dom.AST;
import org.eclipse.jdt.core.dom.ASTParser;
import org.eclipse.jdt.core.dom.CompilationUnit;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ASTBuilder {

    public static final String MAVEN_CLASS_PATH = "/target/classes";
    public static final String GRADLE_CLASS_PATH = "/build/classes";
    public static final String SRC_PATH = "/src/main/java";
    public static final String MAVEN_PROJECT_FILE = "pom.xml";
    public static final String GRADLE_PROJECT_FILE = "build.gradle";
    private final List<String> classPathList;
    private final List<String> sources;
    private final List<String> encoding;
    private final File rootDir;
    private final List<String> modules;


    public ASTBuilder(String repoPath) {
        this.classPathList = new ArrayList<>();
        this.sources = new ArrayList<>();
        this.encoding = new ArrayList<>();
        this.rootDir = new File(repoPath);
        this.modules = new ArrayList<>();
        searchModules();
        collectProjectContext();
    }


    public CompilationUnit createCompilationUnit(String fileName, String fileText) {
        Map<String, String> options = JavaCore.getOptions();
        JavaCore.setComplianceOptions(JavaCore.VERSION_1_8, options);
        ASTParser astParser = ASTParser.newParser(AST.JLS8);
        astParser.setResolveBindings(true);
        astParser.setKind(ASTParser.K_COMPILATION_UNIT);
        astParser.setBindingsRecovery(true);
        astParser.setCompilerOptions(options);
        astParser.setEnvironment(classPathList.toArray(new String[0]), sources.toArray(new String[0]), encoding.toArray(new String[0]), false);

        astParser.setUnitName(fileName);
        astParser.setSource(fileText.toCharArray());
        return (CompilationUnit) astParser.createAST(null);

    }


    private void collectProjectContext() {
        modules.forEach(modulePath -> {
            addProjectContext(modulePath, MAVEN_CLASS_PATH);
            addProjectContext(modulePath, GRADLE_CLASS_PATH);
        });
    }

    private void addProjectContext(String modulePath, String classPath) {
        File targetDir = new File(modulePath + classPath);
        if (targetDir.exists()) {
            classPathList.add(modulePath + classPath);
            sources.add(modulePath + SRC_PATH);
            encoding.add("UTF-8");
        }
    }


    private void searchModules() {
        searchModules(rootDir, rootDir);
    }

    private void searchModules(File rootDir, File currentDir) {
        File[] files = currentDir.listFiles();
        if (files != null) {
            for (File file : files) {
                searchModule(rootDir, currentDir, file);
            }
        }
    }

    private void searchModule(File rootDir, File currentDir, File file) {
        if (file.isDirectory()) {
            searchModules(rootDir, file);
        } else if (mavenOrGradleModuleFile(file)) {
            modules.add(currentDir.getAbsolutePath());
        }
    }

    private static boolean mavenOrGradleModuleFile(File file) {
        return mavenModuleFile(file) || gradleModuleFile(file);
    }

    private static boolean gradleModuleFile(File file) {
        return file.getName().equals(GRADLE_PROJECT_FILE);
    }

    private static boolean mavenModuleFile(File file) {
        return file.getName().equals(MAVEN_PROJECT_FILE);
    }

}
