package code.genius.gencode.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 表示一个完整的可编译项目
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JavaProject {

    /**
     * java 项目根目录
     */
    private String projectPath;
    /**
     * 当前项目中所有的 Java 文件源代码信息
     */
    private List<JavaFile> javaFiles;

    private Map<String, JavaFile> javaFileMap;

    /**
     * 当前项目中所有的 Java 方法信息
     */
    private Map<String, JavaMethod> allJavaMethodMap;

    public JavaProject(String projectPath) {
        this.projectPath = projectPath;
        javaFiles = new ArrayList<>();
        allJavaMethodMap = new ConcurrentHashMap<>();
        javaFileMap = new ConcurrentHashMap<>();
    }

    public void addJavaFile(JavaFile javaFile) {
        if (javaFile == null) {
            return;
        }

        javaFiles.add(javaFile);
        javaFileMap.put(javaFile.getFullClassName(), javaFile);
        javaFile.setJavaProject(this);
    }

    public JavaFile getJavaFile(String fullClassName) {
        if (fullClassName == null) {
            return null;
        }

        return javaFileMap.get(fullClassName);
    }

    @Override
    public String toString() {

        return projectPath;
    }

    public void addJavaMethod(JavaMethod javaMethod) {
        if (javaMethod != null) {
            allJavaMethodMap.put(javaMethod.getMethodId(), javaMethod);
        }
    }

    public void addJavaMethods(List<JavaMethod> javaMethods) {
        if (javaMethods != null) {
            for (JavaMethod javaMethod : javaMethods) {
                addJavaMethod(javaMethod);
            }
        }
    }

    public List<JavaFile> getSuperJavaFiles(String fullClassName) {
        JavaFile javaFile = getJavaFileMap().get(fullClassName);
        if (javaFile == null) {
            return new ArrayList<>();
        }

        return javaFile.getSuperJavaFiles();
    }
}
