package ai.exam.domain.account;

import ai.exam.domain.account.mapper.UserPermissionsEntityDOMapper;
import ai.exam.domain.account.po.UserPermissionsEntity;
import ai.exam.domain.account.repository.UserPermissionsRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UserPermissionsDomainImpl implements UserPermissionsDomain {

    @Resource
    private UserPermissionsRepository userPermissionsRepository;

    @Resource
    private UserPermissionsEntityDOMapper userPermissionsEntityDOMapper;

    @Override
    public Map<Long, UserPermissionsDO> batchQueryByPermissionsType(Long userId, String permissionsType, List<Long> permissionsContentIds) {
        // 批量查询用户权限，只查询未删除的记录
        List<UserPermissionsEntity> userPermissionsEntities = userPermissionsRepository.findByUserIdAndPermissionsTypeAndPermissionsContentIdInAndDelFlag(userId, permissionsType, permissionsContentIds, false);

        // 转换为DO对象并按permissionsContentId分组
        return userPermissionsEntities.stream()
                .map(userPermissionsEntityDOMapper::toUserPermissionsDO)
                .collect(Collectors.toMap(
                        UserPermissionsDO::getPermissionsContentId,
                        userPermissionsDO -> userPermissionsDO
                ));
    }

    @Override
    public List<UserPermissionsDO> fetchAllPermissionsContentByPermissionsType(Long userId, String permissionsType) {
        // 查询用户指定类型的所有未删除权限
        List<UserPermissionsEntity> userPermissionsEntities = userPermissionsRepository.findByUserIdAndPermissionsTypeAndDelFlag(
                userId,
                permissionsType,
                false
        );

        // 转换为DO对象返回
        return userPermissionsEntityDOMapper.toUserPermissionsDO(userPermissionsEntities);
    }

    @Override
    public UserPermissionsDO getPermissionsByAccountIdAndPermissionsTypeAndProductId(Long accountId, String permissionsType, Long productId) {
        // 查询用户的指定权限
        UserPermissionsEntity permission = userPermissionsRepository.findByUserIdAndPermissionsTypeAndPermissionsContentIdAndDelFlag(
                accountId,
                permissionsType,
                productId,
                false
        );

        return userPermissionsEntityDOMapper.toUserPermissionsDO(permission);
    }

    @Override
    public void insertPermissions(List<UserPermissionsDO> userPermissionsDOs) {
        if (userPermissionsDOs == null || userPermissionsDOs.isEmpty()) {
            return;
        }

        // 将DO对象转换为实体对象
        List<UserPermissionsEntity> newEntities = userPermissionsDOs.stream()
                .map(userPermissionsEntityDOMapper::toUserPermissionsEntity)
                .peek(entity -> entity.setDelFlag(false))  // 设置删除标记为false
                .toList();

        // 查询是否已存在权限，如果存在则更新过期时间，不存在则插入
        for (UserPermissionsEntity newEntity : newEntities) {
            try {
                UserPermissionsEntity existEntity = userPermissionsRepository.findByUserIdAndPermissionsTypeAndPermissionsContentIdAndDelFlag(newEntity.getUserId(), newEntity.getPermissionsType(), newEntity.getPermissionsContentId(), false);
                if (existEntity != null) {
                    existEntity.setValidityPeriod(newEntity.getValidityPeriod());
                    userPermissionsRepository.save(existEntity);
                    continue;
                }

                userPermissionsRepository.save(newEntity);
            } catch (Exception e) {
                log.error("[发放用户权限]失败，userPermissionsDO：{}", newEntity, e);
            }
        }
    }

}