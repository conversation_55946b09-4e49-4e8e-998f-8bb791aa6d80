package ai.exam.domain.account;

import ai.exam.domain.account.mapper.AccountEntityDOMapper;
import ai.exam.domain.account.po.AccountEntity;
import ai.exam.domain.account.repository.AccountRepository;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Optional;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Component
public class AccountDomainImpl implements AccountDomain {

    @Resource
    private AccountRepository accountRepository;

    @Resource
    private AccountEntityDOMapper accountEntityDOMapper;

    @Override
    public AccountDO createByWechat(String openId, String unionId) {
        Optional<AccountEntity> accountEntityOptional = accountRepository.findByOpenId(openId);
        AccountEntity accountEntity = null;
        if (accountEntityOptional.isPresent()) {
            return accountEntityDOMapper.toAccountDO(accountEntityOptional.get());
        } else {
            // 先检查是否有已经使用相同手机号注册的账户
            // 由于这里没有手机号信息，我们需要先创建临时账户
            accountEntity = AccountEntity.builder()
                    .openId(openId)
                    .unionId(unionId)
                    .build();
            return accountEntityDOMapper.toAccountDO(accountRepository.save(accountEntity));
        }
    }

    @Override
    public AccountDO updateByWechat(String openId, String unionId, String nickname, String avatarUrl, String phone) {
        Optional<AccountEntity> accountEntityOptional = accountRepository.findByOpenId(openId);
        AccountEntity accountEntity = null;
        
        // 检查手机号是否已经被其他账户使用
        if (StringUtils.isNotBlank(phone)) {
            Optional<AccountEntity> existingAccountByPhone = accountRepository.findByPhone(phone);
            // 如果找到了账户，且不是当前openId的账户，说明这个手机号已经被其他账户使用了
            if (existingAccountByPhone.isPresent() && 
                (accountEntityOptional.isEmpty() || !existingAccountByPhone.get().getId().equals(accountEntityOptional.get().getId()))) {
                // 如果是web端创建的账户(有手机号但无openId)，则更新它，添加openId信息
                AccountEntity existingAccount = existingAccountByPhone.get();
                if (StringUtils.isBlank(existingAccount.getOpenId())) {
                    // 更新账户信息
                    existingAccount.setOpenId(openId);
                    existingAccount.setUnionId(unionId);
                    
                    // 只有在现有值为空时才更新这些字段
                    if (StringUtils.isNotBlank(nickname) && StringUtils.isBlank(existingAccount.getNickname())) {
                        existingAccount.setNickname(nickname);
                    }
                    if (StringUtils.isNotBlank(avatarUrl) && StringUtils.isBlank(existingAccount.getAvatarUrl())) {
                        existingAccount.setAvatarUrl(avatarUrl);
                    }
                    
                    // 如果当前openId已经关联了另一个账户，则将该账户标记为删除状态
                    if (accountEntityOptional.isPresent()) {
                        AccountEntity oldAccount = accountEntityOptional.get();
                        accountRepository.delete(oldAccount);
                    }
                    
                    return accountEntityDOMapper.toAccountDO(accountRepository.save(existingAccount));
                }
            }
        }
        
        if (accountEntityOptional.isPresent()) {
            accountEntity = accountEntityOptional.get();
            if (StringUtils.isNotBlank(nickname)) {
                accountEntity.setNickname(nickname);
            }
            // 微信得头像地址会变协议
            if (StringUtils.isNotBlank(avatarUrl) && StringUtils.isBlank(accountEntity.getAvatarUrl())) {
                accountEntity.setAvatarUrl(avatarUrl);
            }

            if (StringUtils.isNotBlank(phone)) {
                accountEntity.setPhone(phone);
            }

        } else {
            accountEntity = AccountEntity.builder()
                    .openId(openId)
                    .unionId(unionId)
                    .nickname(nickname)
                    .avatarUrl(avatarUrl)
                    .phone(phone)
                    .build();
        }
        return accountEntityDOMapper.toAccountDO(accountRepository.save(accountEntity));
    }

    @Override
    public Optional<Long> getIdByWechat(String openId, String unionId) {
        return accountRepository.findByOpenId(openId).map(AccountEntity::getId);
    }

    @Override
    public Optional<AccountDO> getById(Long id) {
        return accountRepository.findById(id).map(accountEntityDOMapper::toAccountDO);
    }

    @Override
    public AccountDO getAccountByPhoneOrCreateOne(String phone) {
        // 查询现有账户
        Optional<AccountEntity> accountOptional = accountRepository.findByPhone(phone);

        if (accountOptional.isPresent()) {
            AccountEntity accountEntity = accountOptional.get();
            // 检查是否是通过小程序登录创建但尚未绑定手机号的账户
            if (StringUtils.isNotBlank(accountEntity.getOpenId())) {
                // 这是同一个用户，已经通过微信小程序创建过账户，直接返回
                return accountEntityDOMapper.toAccountDO(accountEntity);
            }
            // 如果是纯手机号注册的账户，直接返回
            return accountEntityDOMapper.toAccountDO(accountEntity);
        }

        // 创建新账户
        AccountEntity accountEntity = AccountEntity.builder()
                .phone(phone)
                .nickname("用户" + phone.substring(Math.max(0, phone.length() - 4))) // 使用手机号后4位作为默认昵称
                .avatarUrl("https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/default_avatar.png")
                .build();
        accountRepository.save(accountEntity);
        return accountEntityDOMapper.toAccountDO(accountEntity);
    }

    @Override
    public AccountDO updateAccount(Long accountId, String nickname, String avatarUrl, String gender) {
        AccountEntity accountEntity = accountRepository.findById(accountId)
                .orElseThrow(() -> new RuntimeException("Account not found"));

        if (StringUtils.isNotBlank(nickname)) {
            accountEntity.setNickname(nickname);
        }
        if (StringUtils.isNotBlank(avatarUrl)) {
            accountEntity.setAvatarUrl(avatarUrl);
        }
        if (StringUtils.isNotBlank(gender)) {
            accountEntity.setGender(gender);
        }

        accountEntity = accountRepository.save(accountEntity);
        return accountEntityDOMapper.toAccountDO(accountEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AccountDO> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return accountRepository.findAllById(ids).stream()
                .map(accountEntityDOMapper::toAccountDO)
                .collect(Collectors.toList());
    }
}
