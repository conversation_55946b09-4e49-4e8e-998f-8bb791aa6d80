package ai.exam.domain.banner.mapper;

import ai.exam.domain.banner.BannerDO;
import ai.exam.domain.banner.po.BannerEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface BannerEntityDOMapper {
    //@Mapping(target = "imageName", source = "entity.imageName")
    //@Mapping(target = "imageUrl", ignore = true)
    BannerDO toBannerDO(BannerEntity entity);

    List<BannerDO> toBannerDOList(List<BannerEntity> entities);
}