package ai.exam.domain.banner.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_protocol")
@SuperBuilder
@NoArgsConstructor
public class ProtocolEntity extends BaseEntity {

    @Column(columnDefinition = "varchar(50) COMMENT '协议类型'")
    private String protocolType;

    @Column(columnDefinition = "longtext COMMENT '协议内容'")
    private String content;

    @Column(columnDefinition = "bit(1) COMMENT '是否生效'")
    private Boolean isEffective;

    @Column(columnDefinition = "int(11)")
    private Integer createBy;

    @Column(columnDefinition = "int(11)")
    private Integer updateBy;
}