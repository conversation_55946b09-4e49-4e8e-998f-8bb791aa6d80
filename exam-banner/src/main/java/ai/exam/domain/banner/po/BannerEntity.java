package ai.exam.domain.banner.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_banner")
@SuperBuilder
@NoArgsConstructor
public class BannerEntity extends BaseEntity {

    @Column(columnDefinition = "varchar(255) COMMENT '图片名称'")
    private String imageName;

    @Column(columnDefinition = "varchar(255) COMMENT '链接url'")
    private String linkUrl;

    @Column(columnDefinition = "int(11)")
    private Integer createBy;

    @Column(columnDefinition = "int(11)")
    private Integer updateBy;

    @Column(columnDefinition = "int(1) DEFAULT '0' COMMENT '删除标识 0存在 其他删除'")
    private Integer delFlag;

    @Column(columnDefinition = "longtext COMMENT '备注'")
    private String remark;

    @Column(columnDefinition = "varchar(10) COMMENT '类型，all-通用，web-官网，mini-小程序'")
    private String type;
}