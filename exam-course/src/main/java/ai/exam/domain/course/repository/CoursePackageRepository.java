package ai.exam.domain.course.repository;

import ai.exam.domain.course.po.CoursePackageEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CoursePackageRepository extends JpaRepository<CoursePackageEntity, Long> {

    /**
     * 根据ID列表和删除标志查询课程包
     *
     * @param ids ID列表
     * @param delFlag 删除标志
     * @return 课程包列表
     */
    List<CoursePackageEntity> findByIdInAndDelFlag(List<Long> ids, Boolean delFlag);
}