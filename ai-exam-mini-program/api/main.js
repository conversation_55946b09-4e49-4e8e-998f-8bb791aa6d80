import http from '../utils/http.js'

export default {
  // 获取openid
  accountLogin(code) {
    return http.get('/account/login?code=' + code)
  },
  // 注册
  accountRegister(data) {
    return http.post('/account/register', data)
  },
  // 获取协议内容
  homeProtocols (protocolTypes) {
    return http.get('/home/<USER>' + protocolTypes)
  },
  // 记录学习时长
  trackStudyDuration() {
    return http.post('/studyhub/track_study_duration')
  }
}
