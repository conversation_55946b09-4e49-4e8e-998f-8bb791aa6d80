<template>
	<view>
    <view class="box">
      <view class="next-content">
        <view class="title">个性化学情诊断</view>
        <view class="memo">
          为了帮助您以更高效率完成备考、达成学习目标，请先帮助小I全面了解您的学习背景、当前知识水平和学习目标，从而为您量身定制专屈的学习计划和复习方案。
        </view>
        <view class="tips">
          <view class="item">
            <u-icon name="checkmark-circle-fill" color="#81B336" size="56rpx"></u-icon>
            <view class="memo">
              <view>精确评估</view>
              <view>全面了解您的优势和薄弱环节</view>
            </view>
          </view>
          <view class="item">
            <u-icon name="checkmark-circle-fill" color="#81B336" size="56rpx"></u-icon>
            <view class="memo">
              <view>精确评估</view>
              <view>全面了解您的优势和薄弱环节</view>
            </view>
          </view>
          <view class="item">
            <u-icon name="checkmark-circle-fill" color="#81B336" size="56rpx"></u-icon>
            <view class="memo">
              <view>AI提升效率</view>
              <view>
                AI 解析重点考点，把“厚”书变“薄”<br />
                边学边练边标记，让学习变“轻”<br />
                AI智能筛选，精准练题
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="next-btn" @click="toAIDiagnosis">
      <text>立即开始学情诊断</text>
      <view class="border">
        <u-icon name="arrow-right" color="#000000" :bold="true" size="50rpx"></u-icon>
      </view>
    </view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
       
      }
		},
		methods: {
      toAIDiagnosis() {
        uni.navigateTo({
          url: '/pages/AIDiagnosis/index'
        })
      },
		}
	}
</script>

<style lang="less" scoped>
@import './index.less';
</style>
