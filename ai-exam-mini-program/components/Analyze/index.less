.select {
  display: flex;
  justify-content: flex-end;
  padding: 10rpx 20rpx;
  gap: 20rpx;

  .select-box {
    padding: 10rpx;
    font-size: 32rpx;
    border: 2rpx solid #000000;
    display: flex;
    justify-content: space-between;

    text {
      padding: 0 10rpx;
    }
  }
}

.box {
  width: 700rpx;
  margin: 20rpx auto;
  box-shadow: 0 0 10px 0px #575757;

  .content-none {
    text-align: center;
    padding: 160rpx 0;
    font-size: 32rpx;
    line-height: 60rpx;
  }

  .content-analyze {
    box-sizing: border-box;
    padding: 50rpx 40rpx 150rpx;
    display: flex;
    flex-direction: column;
    gap: 50rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
    }

    .memo {
      font-size: 26rpx;
      line-height: 40rpx;
    }
  }

  .next-content {
    display: flex;
    flex-direction: column;
    padding: 40rpx 0;
    align-items: center;
    gap: 50rpx;

    .title {
      font-size: 36rpx;
      font-weight: bold;
    }

    .memo {
      width: 92%;
      font-size: 26rpx;
      line-height: 50rpx;
    }

    .tips {
      width: 100%;
      box-sizing: border-box;
      padding-left: 100rpx;

      .item {
        display: flex;
        gap: 20rpx;
        align-items: flex-start;
        margin-bottom: 20rpx;

        .memo {
          font-size: 26rpx;
        }
      }
    }
  }
}

.next-btn {
  margin-top: 50rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  color: #4397E5;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;

  .border {
    border: 6rpx solid #000000;
    border-radius: 50%;
    padding: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}