<template>
  <view>
    <view class="wrong" v-if="hasData">
      <scroll-view scroll-x class="tabs">
        <view class="tab-container">
          <view class="tab-item" v-for="(tab, index) in tabList" :key="index" :class="{ active: currentTab === index }"
            @tap="handleTabChange(index)">
            {{ tab.name }}
          </view>
        </view>
      </scroll-view>

      <view v-if="currentTabType === 'ZJLX'" class="content" :style="{ height }">
        <view class="chapter-list">
          <TreeNode v-for="(item, index) in chapterExerciseList" :key="index" :node="item" :index="index" :level="0"
            :max-level="4" @node-click="handleNodeClickByZJLX"></TreeNode>
        </view>
      </view>

      <view v-if="currentTabType === 'EXAM'" class="content" :style="{ height }">
        <view class="chapter-list">
          <TreeNode v-for="(item, index) in chapterRealList" :key="index" :node="item" :index="index" :level="0"
            :max-level="4" @node-click="handleNodeClickByEXAM"></TreeNode>
        </view>
      </view>

      <view v-if="currentTabType === 'MOCK_LXMS'" class="content" :style="{ height }">
        <view class="item" v-for="(item, index) in mockPracticePapers" :key="index"
          @click="toWrongQuestions('MOCK_LXMS', item.questionSetId)">
          <view class="title">{{ item.name }}</view>
          <view class="right">
            <text>{{ item.count }}</text>
            <u-icon name="arrow-right-double" size="44rpx"></u-icon>
          </view>
        </view>
      </view>

      <view v-if="currentTabType === 'ZTYLLXMS'" class="content" :style="{ height }">
        <view class="item" v-for="(item, index) in realPracticePapers" :key="index"
          @click="toWrongQuestions('ZTYLLXMS', item.questionSetId)">
          <view class="title">{{ item.name }}</view>
          <view class="right">
            <text>{{ item.count }}</text>
            <u-icon name="arrow-right-double" size="44rpx"></u-icon>
          </view>
        </view>
      </view>
    </view>

    <view class="image-none" v-else>
      <image src="https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/2.png"></image>
      <view>暂无数据</view>
    </view>
  </view>
</template>

<script>
import TreeNode from '@/components/treeNode/index'
export default {
  components: {
    TreeNode
  },
  data() {
    return {
      showFlag: [],
      currentTab: 0,
      baseTabList: [
        { name: '章节练习', type: 'ZJLX' },
        { name: '章节真题', type: 'EXAM' },
        { name: '模拟考试', type: 'MOCK_LXMS' },
        { name: '真题演练', type: 'ZTYLLXMS' }
      ]
    }
  },
  props: {
    subjectCode: {
      type: String,
      default: ''
    },
    chapterWrongList: {
      type: Array,
      default: []
    },
    mockPracticePapers: {
      type: Array,
      default: []
    },
    realPracticePapers: {
      type: Array,
      default: []
    }
  },
  computed: {
    hasData() {
      return (this.chapterWrongList?.length > 0) ||
        (this.mockPracticePapers?.length > 0) ||
        (this.realPracticePapers?.length > 0)
    },
    chapterExerciseList() {
      const zjlxData = this.chapterWrongList?.find(item => item.source === 'ZJLX')
      return zjlxData ? zjlxData.groups : []
    },
    chapterRealList() {
      const examData = this.chapterWrongList?.find(item => item.source === 'EXAM')
      return examData ? examData.groups : []
    },
    tabList() {
      return this.baseTabList.filter(tab => {
        switch (tab.type) {
          case 'ZJLX':
            return this.chapterExerciseList?.length > 0
          case 'EXAM':
            return this.chapterRealList?.length > 0
          case 'MOCK_LXMS':
            return this.mockPracticePapers?.length > 0
          case 'ZTYLLXMS':
            return this.realPracticePapers?.length > 0
          default:
            return false
        }
      })
    },
    currentTabType() {
      return this.tabList[this.currentTab]?.type
    }
  },
  watch: {
    tabList: {
      immediate: true,
      handler(newVal) {
        if (newVal.length > 0 && !newVal[this.currentTab]) {
          this.currentTab = 0
        }
      }
    }
  },
  methods: {
    getUrlByZJLXOrEXAM(type, code, mode) {
      let chapterCode = ''
      let sectionCode = ''
      if (type === 'section') {
        sectionCode = code
      } else if (type === 'chapter') {
        chapterCode = code
      } else {
        uni.showToast({
          title: "请选择章/节查看错题！",
          icon: "none",
        });
        return;
      }
      return '/pages/WrongQuestions/index?mode=' + mode + '&subjectCode=' + this.subjectCode + '&sectionCode=' + sectionCode + '&chapterCode=' + chapterCode;
    },
    // 章节练习，章节真题
    handleNodeClickByZJLX(item) {
      const data = item.node
      let url = this.getUrlByZJLXOrEXAM(data.type, data.code, 'ZJLX')
      uni.navigateTo({
        url: url
      })
    },
    handleNodeClickByEXAM(item) {
      const data = item.node
      let url = this.getUrlByZJLXOrEXAM(data.type, data.code, 'EXAM')
      uni.navigateTo({
        url: url
      })
    },
    // 模拟考试，真题演练
    toWrongQuestions(mode, questionSetId) {
      uni.navigateTo({
        url: '/pages/WrongQuestions/index?mode=' + mode + '&subjectCode=' + this.subjectCode + '&questionSetId=' + questionSetId
      })
    },
    handleTabChange(index) {
      this.currentTab = index
      this.showFlag = []
    },
    showFlagChange(index) {
      const flag = this.showFlag.findIndex(e => e === index)
      if (flag > -1) {
        this.showFlag.splice(flag, 1)
      } else {
        this.showFlag.push(index)
      }
    }
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>