<template>
    <view class="circle-container">
        <canvas canvas-id="progressCanvas" class="progress-canvas"></canvas>
    </view>
</template>

<script>
export default {
    props: {
        progressPercentage: {
            type: Number,
            default: 0
        },
        startColor: {
            type: String,
            default: '#2463eb'
        },
        endColor: {
            type: String,
            default: '#0ed3cf'
        }
    },
    data() {
        return {
        }
    },
    mounted() {
        this.drawProgressCircle()
    },
    methods: {
        drawProgressCircle() {
            const ctx = uni.createCanvasContext('progressCanvas', this)
            const canvasSize = 100
            const centerX = canvasSize / 2
            const centerY = canvasSize / 2
            const radius = (canvasSize / 2) - 6

            const lineWidth = 12

            // 绘制背景圆
            ctx.beginPath()
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
            ctx.setStrokeStyle('#e0e0e0')
            ctx.setLineWidth(lineWidth)
            ctx.stroke()

            // 创建渐变色
            const gradient = ctx.createLinearGradient(0, 0, canvasSize, canvasSize)
            gradient.addColorStop(0, this.startColor)  // 蓝色
            gradient.addColorStop(1, this.endColor)  // 青色

            // 绘制进度圆
            const endAngle = (this.progressPercentage / 100) * 2 * Math.PI - Math.PI / 2
            ctx.beginPath()
            ctx.arc(centerX, centerY, radius, -Math.PI / 2, endAngle)
            ctx.setStrokeStyle(gradient)
            ctx.setLineWidth(lineWidth)
            ctx.setLineCap('butt')
            ctx.stroke()

            ctx.draw()
        }
    },
    watch: {
        progressPercentage() {
            this.drawProgressCircle()
        }
    }
}
</script>
<style scoped lang="less">
.circle-container {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .progress-canvas {
        width: 100px;
        height: 100px;
    }
}
</style>
