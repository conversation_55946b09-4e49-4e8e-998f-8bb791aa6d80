.tree-node {
  .node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 10rpx;
    background: #fff;
    border-bottom: 1rpx solid #f5f5f5;

    &:active {
      background: #f8f8f8;
    }

    .node-content {
      display: flex;
      align-items: center;
      flex: 1;

      .node-prefix {
        font-weight: normal;
        margin-right: 10rpx;
        color: #333;
      }

      .node-title {
        font-size: 32rpx;
        color: #333;
        flex: 1;
      }
    }

    .node-right {
      display: flex;
      align-items: center;
      color: #999;

      text {
        margin-right: 10rpx;
      }

      .expand-icon {
        margin-left: 10rpx;
      }
    }
  }

  .child-nodes {
    padding-left: 20rpx;
    background: transparent;

    .tree-node {
      margin-bottom: 0;

      .node-header {
        background: transparent;

        &:active {
          background: #f0f0f0;
        }

        .node-content {
          .node-prefix {
            font-weight: normal;
          }

          .node-title {
            font-weight: normal;
          }
        }
      }

      .child-nodes {
        background: transparent;
      }
    }
  }

  &.first-level {
    .node-header {
      .node-content {
        .node-title {
          font-size: 32rpx;
          font-weight: normal;
        }
      }
    }
  }

  &.last-level {
    .node-header {
      background: #fff;
      
      .node-content {
        .node-title {
          color: #999;
        }
      }
    }
  }
}
