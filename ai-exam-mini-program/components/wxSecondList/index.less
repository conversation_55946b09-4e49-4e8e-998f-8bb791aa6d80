.wx-second-list {
    display: flex;
    width: 100%;
    height: 600rpx;
    background-color: #fff;
    overflow: hidden;
    
    .left-wrapper {
      width: 240rpx;
      height: 100%;
      background-color: #f5f5f5;
      
      .left-item {
        position: relative;
        display: flex;
        align-items: center;
        height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #333;
        
        &.active {
          background-color: #fff;
          color: var(--theme-color, #007AFF);
          font-weight: 500;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6rpx;
            height: 36rpx;
            background-color: var(--theme-color, #007AFF);
            border-radius: 0 4rpx 4rpx 0;
          }
        }
        
        .item-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    
    .right-wrapper {
      flex: 1;
      height: 100%;
      
      .right-item {
        display: flex;
        align-items: center;
        height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #333;
        
        &.active {
          color: var(--theme-color, #007AFF);
          font-weight: 500;
        }
        
        .item-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }