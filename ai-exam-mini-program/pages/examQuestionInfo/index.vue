<template>
	<view style="height: 100vh; overflow: hidden">
		<u-navbar placeholder @leftClick="leftClick">
			<view slot="center" class="nav-title">{{ mode === 'EXAM' ? '章节真题' : '章节练习' }}</view>
		</u-navbar>
		<view v-show="!loading">
			<view class="page" style="padding: 20rpx 0">
				<view style="
            padding-left: 25rpx;
            padding-right: 25rpx;
            box-sizing: border-box;
          ">

					<view class="scroll-indicator" v-if="list.length">
						<view class="indicator-container">
							<view class="page-count">
								{{ indicatorIndex + 1 }} / {{ list.length }}
							</view>

							<view class="progress-bar">
								<view class="progress-inner"
									:style="{ width: `${(indicatorIndex + 1) / list.length * 100}%` }"></view>
							</view>

							<view class="stats-left">
								<u-icon name="checkmark-circle" size="50rpx" color="#9AE137"></u-icon>
								<text>{{ completedQuestions.filter((e) => e.isRight).length }}</text>
								<u-icon name="close-circle" size="50rpx" color="#D30309"></u-icon>
								<text>{{ completedQuestions.filter((e) => !e.isRight).length }}</text>
							</view>
						</view>
					</view>


				</view>

				<view class="scroll-box">
					<view class="content" @touchstart="scrollTouchstart" @touchend="scrollTouchend" :style="{
            left: -indicatorIndex * 750 + 'rpx',
            width: 750 * list.length + 'rpx'
          }" style="box-sizing: border-box; gap: 0rpx">
						<view v-for="(item, index) in list" :key="index" style="
                width: 750rpx;
                padding: 0 25rpx 25rpx;
                box-sizing: border-box;
              ">
							<view v-if="forceRefresh" class="item" style="width: 100%">
								<view class="title">
									<text>{{ '0' + (index + 1) }}</text>
									<view class="type">{{
                    item.questionType === 'CHOICE'
                      ? '单选题'
                      : item.questionType === 'CHOICES'
                        ? '多选题'
                        : item.questionType === 'TRUE_FALSE'
                          ? '判断题'
                          : item.questionType === 'INDEFINITE_CHOICE'
                            ? '不定项选择题'
                            : '简答题'
                  }}</view>
								</view>
								<mpHtml class="topic" :content="item.questionContent"></mpHtml>
								<TopicOptions :options="item.options" :questionType="item.questionType"
									:answer="item.answer.split('')" :disabled="wrong || item.analysis.rightAnswer || item.rightAnswer"
									@selectedAnswer="(answer) => selectedAnswer(answer, index)">
								</TopicOptions>

								<view class="btn-submit" v-if="!item.analysis.rightAnswer && !item.rightAnswer"
									@click="submitAnswer(item.questionType, item.answer, item.questionId, index)">{{ item.questionType !==
                    'OPEN' ? '提交答案' : '查看答案' }}</view>
								<view v-else>
									<view v-show="answerFlag">
										<view class="answer" v-if="item.questionType !== 'OPEN'">
											<view>
												<text>【正确答案】</text>
												<mpHtml style="color: rgb(37, 81, 246)" class="analysis"
													:content="item.rightAnswer || item.analysis.rightAnswer"></mpHtml>
											</view>
											<view>
												<text>【你的答案】</text>
												<text :style="{
                          color: item.analysis.isRight ? '#A2EF4D' : '#D30309'
                        }">{{ desc(item.answer) || '未作答' }}</text>
											</view>
										</view>
										<view v-else>
											<view style="margin: 20rpx;"><text>【正确答案】</text></view>
											<mpHtml style="color: rgb(37, 81, 246)"
												:content="item.rightAnswer || item.analysis.rightAnswer">
											</mpHtml>
										</view>
										<mpHtml class="analysis" v-if="item.analysis.length" :content="item.analysis">
										</mpHtml>
										<mpHtml class="analysis" v-else :content="item.analysis.analysis"></mpHtml>
										<view class="btn-next">
											<view v-if="indicatorIndex" @click="indicatorIndexChange('reduce')">上一题
											</view>
											<view v-if="indicatorIndex < list.length - 1"
												@click="indicatorIndexChange('add')">下一题</view>
											<view v-if="indicatorIndex === list.length - 1 && !wrong" @click="toResult">
												点击查看结果</view>
										</view>
									</view>
								</view>
							</view>
						</view>

					</view>
				</view>
				<view class="collect">
					<Collect v-if="list.length && !wrong" :correct="completedQuestions.filter((e) => e.isRight).length"
						:mistake="completedQuestions.filter((e) => !e.isRight).length" :collectFlag="favoriteRecordVOS.includes(list[indicatorIndex].questionId)
              " :list="list" :questionId="list[indicatorIndex].questionId" :questionSetType="mode"
						@questionFavorite="chapterQuestionsQuestionFavorite" @indicatorIndexChange="handleIndexChange"
						mode="PRACTICE"></Collect>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import homeApi from '@/api/home'
	import Collect from '@/components/collect/index'
	import TopicOptions from '@/components/topicOptions/index'
	import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
	import studyTimer from '@/mixins/study-timer'

	export default {
		mixins: [studyTimer],
		components: {
			Collect,
			TopicOptions,
			mpHtml
		},
		data() {
			return {
				indicatorIndex: 0,
				startClientX: 0,
				list: [],
				subjectCode: null,
				sectionCode: null,
				favoriteRecordVOS: [],
				completedQuestions: [],
				batchNum: null,
				needShow: false,
				wrong: false,
				scrollLeft: 0,
				loading: true,
				answerFlag: true,
				mode: '',
				forceRefresh: true
			}
		},
		watch: {
			indicatorIndex(newIndex, oldIndex) {
				homeApi.commonLocation({
					exerciseMode: this.mode,
					sectionCode: this.sectionCode,
					chapterCode: this.chapterCode,
					questionId: this.list[newIndex].questionId
				})
				this.$nextTick(() => {
					this.scrollLeft = newIndex >= 16 ? (newIndex - 15) * 36 + 'rpx' : 0
				})
			}
		},
		onLoad(option) {
			this.subjectCode = option.subjectCode
			this.chapterCode = option.chapterCode
			this.sectionCode = option.sectionCode
			this.batchNum = option.batchNum
			this.wrong = option.wrong
			this.mode = option.mode
			if (!this.wrong) {
				this.generateBatchNum()
			}
		},
		onShow() {
			const indicatorIndex = uni.getStorageSync('indicatorIndex')
			if (typeof indicatorIndex === 'number') {
				this.indicatorIndex = indicatorIndex
				uni.setStorageSync('indicatorIndex', '')
				return
			}
			if (this.wrong) {
				this.chapterQuestionsWrongQuestions()
			} else {
				if (this.mode === 'EXAM') {
					this.chapterQuestionsExamQuestionInfoReal(this.sectionCode, this.chapterCode, this.subjectCode, true)
				} else {
					this.chapterQuestionsExamQuestionInfo(this.sectionCode, this.chapterCode, this.subjectCode, true)
				}
			}
		},
		methods: {
			handleIndexChange(index) {
				this.indicatorIndex = index
				this.list = this.list.map((item, index) => {
					return {
						...item,
						location: index === this.indicatorIndex
					}
				})
			},
			itemBackgroundColor(item) {
				if (item === this.indicatorIndex) {
					return '#000000'
				} else if ((this.list[item].answer && this.list[item].analysis.rightAnswer) || this.list[item]
					.userAnswer) {
					return '#A2EF4D'
				} else {
					return '#E0E0E0'
				}
			},

			async chapterQuestionsWrongQuestions() {
				let res = {}
				if (this.mode === 'EXAM') {
					res = await homeApi.chapterQuestionsWrongQuestionsReal(this.batchNum, this.sectionCode, this
						.chapterCode)
				} else {
					res = await homeApi.chapterQuestionsWrongQuestions(this.batchNum, this.sectionCode, this
						.chapterCode)
				}
				this.list = res.map((e) => ({
					...e,
					answer: e.userAnswer
				}))
				setTimeout(() => {
					this.loading = false
				}, 2000)
			},

			indicatorIndexChange(type) {
				if (type === 'add' && this.indicatorIndex < this.list.length - 1) {
					this.indicatorIndex++
				} else if (type === 'reduce' && this.indicatorIndex) {
					this.indicatorIndex--
				}
				this.list = this.list.map((item, index) => {
					return {
						...item,
						location: index === this.indicatorIndex
					}
				})
				// 判断下一题是否简答题，如果是则走下面这块，否则就不动
				if (this.list[this.indicatorIndex].questionType === 'OPEN') {
					this.forceRefresh = false
					this.$nextTick(() => {
						this.forceRefresh = true
					})
				}
			},

			desc(answer) {
				return typeof answer === 'string' ?
					answer :
					answer.join().replace(/,/g, '')
			},

			selectedAnswer(answer, index) {
				const json = {
					...this.list[index],
					answer
				}
				this.$set(this.list, index, json)
			},

			leftClick() {
				if (this.needShow) {
					// this.toResult()
					const {
						sectionCode,
						batchNum,
						subjectCode,
						mode,
						chapterCode
					} = this
					uni.redirectTo({
						url: '/pages/Result/index?sectionCode=' +
							sectionCode +
							'&batchNum=' +
							batchNum +
							'&subjectCode=' +
							subjectCode +
							'&chapterCode=' +
							chapterCode +
							'&mode=' + mode
					})
				} else {
					uni.navigateBack()
				}
			},

			generateBatchNum() {
				if (this.mode === 'EXAM') {
					homeApi.generateBatchNum('EXAM').then((e) => {
						this.batchNum = e.batchNum
					})
				} else {
					homeApi.generateBatchNum('ZJLX').then((e) => {
						this.batchNum = e.batchNum
					})
				}
			},

			chapterQuestionsQuestionFavorite() {
				const {
					sectionCode,
					chapterCode,
					list,
					indicatorIndex,
					subjectCode,
					favoriteRecordVOS
				} = this
				const favorite = !favoriteRecordVOS.includes(
					list[indicatorIndex].questionId
				)
				const question = list[indicatorIndex]
				let tempChapterCode = chapterCode
				let tempSectionCode = sectionCode
				if (tempSectionCode) {
					tempChapterCode = ''
				}
				const params = {
					subjectCode: subjectCode,
					questionId: question.questionId,
					favorite: favorite,
					exerciseMode: this.mode,
					chapterCode: tempChapterCode,
					sectionCode: tempSectionCode,
					questionSetId: ''
				}
				homeApi.bookQuestionFavorite(params).then(() => {
					if (this.mode === 'EXAM') {
						this.chapterQuestionsExamQuestionInfoReal(this.sectionCode, this.chapterCode, this
							.subjectCode)
					} else {
						this.chapterQuestionsExamQuestionInfo(this.sectionCode, this.chapterCode, this.subjectCode)
					}
				})
			},

			submitAnswer(type, answer, questionId, index) {
				if (!answer && type !== 'OPEN') {
					uni.showToast({
						title: '请作答',
						duration: 2000,
						icon: 'none'
					})
					return
				}
				this.answerFlag = false
				const {
					subjectCode,
					sectionCode,
					chapterCode,
					batchNum
				} = this

				if (this.mode === 'EXAM') {
					homeApi
						.chapterQuestionsSubmitAnswerReal({
							subjectCode,
							sectionCode,
							chapterCode,
							answer,
							questionId,
							batchNum
						})
						.then((e) => {
							this.needShow = true
							this.chapterQuestionsExamQuestionInfoReal(sectionCode, chapterCode, subjectCode)
						})
				} else {
					homeApi
						.chapterQuestionsSubmitAnswer({
							subjectCode,
							sectionCode,
							chapterCode,
							answer,
							questionId,
							batchNum
						})
						.then((e) => {
							this.needShow = true
							this.chapterQuestionsExamQuestionInfo(sectionCode, chapterCode, subjectCode)
						})
				}
			},

			chapterQuestionsExamQuestionInfoReal(sectionCode, chapterCode, subjectCode, flag) {
				if (flag) {
					uni.showLoading()
				}
				homeApi
					.chapterQuestionsExamQuestionInfoReal(sectionCode, chapterCode, subjectCode)
					.then((res) => {
						if (!this.list.length) {
							this.list = res.allQuestions.map((e) => {
								const questionContentFlag = e.questionContent.indexOf('img')
								if (questionContentFlag > -1) {
									e.questionContent =
										'<br>' +
										e.questionContent.slice(0, questionContentFlag + 3) +
										' width="100%" height="100%"' +
										e.questionContent.slice(
											questionContentFlag + 3,
											e.questionContent.length
										)
								}
								for (let i in e.options) {
									const index = e.options[i].indexOf('img')
									if (index > -1) {
										e.options[i] =
											e.options[i].slice(0, index + 3) +
											' width="30rpx" height="30rpx"' +
											e.options[i].slice(index + 3, e.options[i].length)
									}
								}
								const options = {}
								const arr = Object.keys(e.options).sort()
								for (let k = 0; k < arr.length; k++) {
									options[arr[k]] = e.options[arr[k]]
								}
								e.options = options
								return {
									...e,
									answer: '',
									analysis: {}
								}
							})
							this.indicatorIndex = res.allQuestions.findIndex((e) => e.location)
						}
						this.favoriteRecordVOS = res.favoriteRecordVOS.map(
							(e) => e.questionId
						)
						this.completedQuestions = res.completedQuestions
						this.completedQuestions.forEach((e) => {
							for (let i in e) {
								if (typeof e[i] === 'string') {
									const index = e[i].indexOf('img')
									if (index > -1) {
										e[i] =
											'<br />' +
											e[i].slice(0, index + 3) +
											' width="100%" height="100%"' +
											e[i].slice(index + 3, e[i].length)
									}
								}
							}
							const index = this.list.findIndex(
								(i) => i.questionId === e.questionId
							)
							this.$set(this.list, index, {
								...this.list[index],
								analysis: e,
								answer: e.selectedOption
							})
						})
						setTimeout(() => {
							this.loading = false
							if (flag) {
								uni.hideLoading()
							}
						}, 1500)
						setTimeout(() => {
							this.answerFlag = true
						}, 600)
					})
			},

			chapterQuestionsExamQuestionInfo(sectionCode, chapterCode, subjectCode, flag) {
				if (flag) {
					uni.showLoading()
				}
				homeApi
					.chapterQuestionsExamQuestionInfo(sectionCode, chapterCode, subjectCode)
					.then((res) => {
						if (!this.list.length) {
							this.list = res.allQuestions.map((e) => {
								const questionContentFlag = e.questionContent.indexOf('img')
								if (questionContentFlag > -1) {
									e.questionContent =
										'<br>' +
										e.questionContent.slice(0, questionContentFlag + 3) +
										' width="100%" height="100%"' +
										e.questionContent.slice(
											questionContentFlag + 3,
											e.questionContent.length
										)
								}
								for (let i in e.options) {
									const index = e.options[i].indexOf('img')
									if (index > -1) {
										e.options[i] =
											e.options[i].slice(0, index + 3) +
											' width="30rpx" height="30rpx"' +
											e.options[i].slice(index + 3, e.options[i].length)
									}
								}
								const options = {}
								const arr = Object.keys(e.options).sort()
								for (let k = 0; k < arr.length; k++) {
									options[arr[k]] = e.options[arr[k]]
								}
								e.options = options
								return {
									...e,
									answer: '',
									analysis: {}
								}
							})
							this.indicatorIndex = res.allQuestions.findIndex((e) => e.location)
						}
						this.favoriteRecordVOS = res.favoriteRecordVOS.map(
							(e) => e.questionId
						)
						this.completedQuestions = res.completedQuestions
						this.completedQuestions.forEach((e) => {
							for (let i in e) {
								if (typeof e[i] === 'string') {
									const index = e[i].indexOf('img')
									if (index > -1) {
										e[i] =
											'<br />' +
											e[i].slice(0, index + 3) +
											' width="100%" height="100%"' +
											e[i].slice(index + 3, e[i].length)
									}
								}
							}
							const index = this.list.findIndex(
								(i) => i.questionId === e.questionId
							)
							this.$set(this.list, index, {
								...this.list[index],
								analysis: e,
								answer: e.selectedOption
							})
						})
						setTimeout(() => {
							this.loading = false
							if (flag) {
								uni.hideLoading()
							}
						}, 1500)
						setTimeout(() => {
							this.answerFlag = true
						}, 600)
					})
			},


			toResult() {
				const {
					sectionCode,
					chapterCode,
					indicatorIndex,
					batchNum,
					subjectCode
				} = this
				if (indicatorIndex === this.list.length - 1) {
					if (this.mode === 'EXAM') {
						homeApi
							.chapterQuestionsExamPracticeInfoReal(sectionCode, chapterCode, batchNum)
							.then((res) => {
								if (res.needShow) {
									uni.redirectTo({
										url: '/pages/Result/index?sectionCode=' +
											sectionCode +
											'&batchNum=' +
											batchNum +
											'&subjectCode=' +
											subjectCode +
											'&mode=EXAM' +
											'&chapterCode=' +
											chapterCode
									})
								} else {
									uni.showToast({
										title: '本次未做答',
										duration: 2000,
										icon: 'none'
									})
								}
							})
					} else {
						homeApi
							.chapterQuestionsExamPracticeInfo(sectionCode, chapterCode, batchNum)
							.then((res) => {
								if (res.needShow) {
									uni.redirectTo({
										url: '/pages/Result/index?sectionCode=' +
											sectionCode +
											'&batchNum=' +
											batchNum +
											'&subjectCode=' +
											subjectCode +
											'&mode=ZJLX' +
											'&chapterCode=' +
											chapterCode
									})
								} else {
									uni.showToast({
										title: '本次未做答',
										duration: 2000,
										icon: 'none'
									})
								}
							})
					}
				}
			},

			scrollTouchstart(e) {
				this.startClientX = e.changedTouches[0].clientX
			},

			scrollTouchend(e) {
				if (e.changedTouches[0].clientX > this.startClientX + 80) {
					if (this.indicatorIndex === 0) {
						return
					}
					this.indicatorIndex--
				} else if (e.changedTouches[0].clientX < this.startClientX - 80) {
					if (this.indicatorIndex === this.list.length - 1) {
						return
					}
					this.indicatorIndex++
				}
				this.list = this.list.map((item, index) => {
					return {
						...item,
						location: index === this.indicatorIndex
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	@import './index.less';
</style>