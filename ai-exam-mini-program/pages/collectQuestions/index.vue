<template>
	<view v-show="!loading">
		<view class="page" style="padding: 20rpx 0">
			<view style="padding-left: 25rpx; padding-right: 25rpx; box-sizing: border-box">

				<view class="scroll-indicator" v-if="list.length">
					<view class="indicator-container">
						<view class="page-count">
							{{ indicatorIndex + 1 }} / {{ list.length }}
						</view>
						<view class="progress-bar">
							<view class="progress-inner"
								:style="{ width: `${(indicatorIndex + 1) / list.length * 100}%` }"></view>
						</view>

						<view class="stats-left" v-if="list.length">
							<text class="iconfont iciconfont icon-shoucang"
								:class="list[indicatorIndex].collectFlag ? 'cor-FFD23D' : 'cor-5D5D5D'"
								@click="bookQuestionFavorite(!list[indicatorIndex].collectFlag)"></text>
							<text @click="bookQuestionFavorite(!list[indicatorIndex].collectFlag)">收藏</text>
						</view>
					</view>
				</view>

			</view>
			<view class="scroll-box">
				<view class="content" @touchstart="scrollTouchstart" @touchend="scrollTouchend"
					:style="{ left: -indicatorIndex * 750 + 'rpx' }" style="box-sizing: border-box; gap: 0rpx">
					<view v-for="(item, index) in list" :key="index"
						style="width: 750rpx; padding: 0 25rpx 25rpx; box-sizing: border-box">
						<view class="item" style="width: 100%">
							<view class="title">
								<text>{{ '0' + (index + 1) }}</text>
								<view class="type">{{
                  item.questionType === 'CHOICE'
                    ? '单选题'
                    : item.questionType === 'CHOICES'
                      ? '多选题'
                      : item.questionType === 'TRUE_FALSE'
                        ? '判断题'
                        : item.questionType === 'INDEFINITE_CHOICE'
                          ? '不定项选择题'
                          : '简答题'
                }}</view>
							</view>
							<mpHtml class="topic" :content="item.questionContent"></mpHtml>
							<TopicOptions :options="item.options" :questionType="item.questionType"
								:answer="item.answer.split('')"
								@selectedAnswer="(answer) => selectedAnswer(answer, index)"
								:disabled="item.analysis.rightAnswer"></TopicOptions>
							<view class="btn-submit" v-if="!item.analysis.rightAnswer"
								@click="submitFavoriteAnswer(item.answer, item.questionId)">
								{{ item.questionType !== 'OPEN' ? '提交答案' : '查看答案' }}
							</view>
							<view v-else>
								<view v-show="answerFlag">
									<view class="answer" v-if="item.questionType !== 'OPEN'">
										<view>
											<text>【正确答案】</text>
											<mpHtml style="color: rgb(37, 81, 246)" class="analysis"
												:content="item.rightAnswer || item.analysis.rightAnswer"></mpHtml>
										</view>
										<view>
											<text>【你的答案】</text>
											<text :style="{
                        color: item.analysis.isRight ? '#A2EF4D' : '#D30309'
                      }">{{ desc(item.answer) || '未作答' }}</text>
										</view>
									</view>
									<view v-else>
										<view style="margin: 20rpx;"><text>【正确答案】</text></view>
										<mpHtml style="color: rgb(37, 81, 246)"
											:content="item.rightAnswer || item.analysis.rightAnswer">
										</mpHtml>
									</view>
									<mpHtml class="analysis" :content="item.analysis.analysis"></mpHtml>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script>
	import homeApi from '@/api/home'
	import TopicOptions from '@/components/topicOptions/index'
	import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

	export default {
		components: {
			TopicOptions,
			mpHtml
		},
		data() {
			return {
				indicatorIndex: 0,
				startClientX: 0,
				list: [],
				subjectCode: null,
				chapterCode: '',
				sectionCode: '',
				questionSetId: '',
				mode: '',
				scrollLeft: 0,
				loading: true,
				answerFlag: true
			}
		},
		watch: {
			indicatorIndex(newIndex, oldIndex) {
				this.$nextTick(() => {
					this.scrollLeft = newIndex >= 16 ? (newIndex - 15) * 36 + 'rpx' : 0
				})
			}
		},
		onLoad(option) {
			this.mode = option.mode || ''
			this.subjectCode = option.subjectCode
			this.chapterCode = option.chapterCode || ''
			this.sectionCode = option.sectionCode || ''
			this.questionSetId = option.questionSetId || ''
		},
		onShow() {
			if (this.mode) {
				this.bookFavoriteQuestions()
				return
			}
			this.bookFavoriteQuestionsSubjectCode()
		},
		methods: {
			itemBackgroundColor(item) {
				if (item === this.indicatorIndex) {
					return '#000000'
				} else if (
					(this.list[item].answer && this.list[item].analysis.rightAnswer) ||
					this.list[item].userAnswer
				) {
					return '#A2EF4D'
				} else {
					return '#E0E0E0'
				}
			},
			selectedAnswer(answer, index) {
				const json = {
					...this.list[index],
					answer
				}
				this.$set(this.list, index, json)
			},
			bookQuestionFavorite(flag) {
				const {
					list,
					indicatorIndex
				} = this
				const params = {
					questionId: list[indicatorIndex].questionId,
					favorite: flag,
				}
				homeApi.bookQuestionFavorite(params).then((res) => {
					const json = this.list[indicatorIndex]
					this.$set(this.list, indicatorIndex, {
						...json,
						collectFlag: flag
					})
				})
			},
			desc(answer) {
				return typeof answer === 'string' ?
					answer :
					answer.join().replace(/,/g, '')
			},
			submitFavoriteAnswer(answer, questionId) {
				if (!answer) {
					uni.showToast({
						title: '请作答',
						duration: 2000,
						icon: 'none'
					})
					return
				}
				this.answerFlag = false
				const {
					subjectCode,
					indicatorIndex
				} = this
				homeApi
					.submitFavoriteAnswer(answer, questionId, subjectCode)
					.then((e) => {
						const index = e.analysis.indexOf('img')
						if (index > -1) {
							e.analysis =
								'<br />' +
								e.analysis.slice(0, index + 3) +
								' width="100%" height="100%"' +
								e.analysis.slice(index + 3, e.analysis.length)
						}
						const json = this.list[indicatorIndex]
						this.$set(this.list, indicatorIndex, {
							...json,
							analysis: e
						})
						setTimeout(() => {
							this.answerFlag = true
						}, 600)
					})
			},

			bookFavoriteQuestionsSubjectCode() {
				uni.showLoading()
				const {
					subjectCode
				} = this
				homeApi.bookFavoriteQuestionsSubjectCode(subjectCode).then((res) => {
					this.list = res.map((e) => {
						const questionContentFlag = e.questionContent.indexOf('img')
						if (questionContentFlag > -1) {
							e.questionContent =
								'<br>' +
								e.questionContent.slice(0, questionContentFlag + 3) +
								' width="100%" height="100%"' +
								e.questionContent.slice(
									questionContentFlag + 3,
									e.questionContent.length
								)
						}
						for (let i in e.options) {
							const index = e.options[i].indexOf('img')
							if (index > -1) {
								e.options[i] =
									e.options[i].slice(0, index + 3) +
									' width="30rpx" height="30rpx"' +
									e.options[i].slice(index + 3, e.options[i].length)
							}
						}
						const options = {}
						const arr = Object.keys(e.options).sort()
						for (let k = 0; k < arr.length; k++) {
							options[arr[k]] = e.options[arr[k]]
						}
						e.options = options
						return {
							...e,
							answer: '',
							collectFlag: true
						}
					})
					setTimeout(() => {
						uni.hideLoading()
						this.loading = false
					}, 1500)
				})
			},

			bookFavoriteQuestions() {
				uni.showLoading()
				const {
					subjectCode,
					chapterCode,
					sectionCode,
					questionSetId,
					mode
				} = this
				homeApi.bookFavoriteQuestions(subjectCode, chapterCode, sectionCode, questionSetId, mode).then((res) => {
					this.list = res.map((e) => {
						const questionContentFlag = e.questionContent.indexOf('img')
						if (questionContentFlag > -1) {
							e.questionContent =
								'<br>' +
								e.questionContent.slice(0, questionContentFlag + 3) +
								' width="100%" height="100%"' +
								e.questionContent.slice(
									questionContentFlag + 3,
									e.questionContent.length
								)
						}
						for (let i in e.options) {
							const index = e.options[i].indexOf('img')
							if (index > -1) {
								e.options[i] =
									e.options[i].slice(0, index + 3) +
									' width="30rpx" height="30rpx"' +
									e.options[i].slice(index + 3, e.options[i].length)
							}
						}
						const options = {}
						const arr = Object.keys(e.options).sort()
						for (let k = 0; k < arr.length; k++) {
							options[arr[k]] = e.options[arr[k]]
						}
						e.options = options
						return {
							...e,
							answer: '',
							collectFlag: true
						}
					})
					setTimeout(() => {
						uni.hideLoading()
						this.loading = false
					}, 1500)
				})
			},

			scrollTouchstart(e) {
				this.startClientX = e.changedTouches[0].clientX
			},

			scrollTouchend(e) {
				if (e.changedTouches[0].clientX > this.startClientX + 80) {
					if (this.indicatorIndex === 0) {
						return
					}
					this.indicatorIndex--
				} else if (e.changedTouches[0].clientX < this.startClientX - 80) {
					if (this.indicatorIndex === this.list.length - 1) {
						return
					}
					this.indicatorIndex++
				}
				this.list = this.list.map((item, index) => {
					return {
						...item,
						location: index === this.indicatorIndex
					}
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	@import './index.less';
</style>