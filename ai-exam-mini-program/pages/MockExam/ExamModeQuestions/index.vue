<template>
  <view class="page" style="padding: 20rpx 0">
    <u-navbar :autoBack="true" placeholder>
      <view slot="center" class="nav-title">考试模式</view>
    </u-navbar>
    <view v-show="!loading">
      <view style="padding-left: 25rpx; padding-right: 25rpx; box-sizing: border-box">

        <view class="scroll-indicator" v-if="list.length">
          <view class="indicator-container">
            <view class="page-count">
              {{ indicatorIndex + 1 }} / {{ list.length }}
            </view>
            <view class="progress-bar">
              <view class="progress-inner" :style="{ width: `${(indicatorIndex + 1) / list.length * 100}%` }"></view>
            </view>
            <view class="stats-left" @click="stopTimer">
              <text>{{ remaining }}</text>
            </view>
          </view>
        </view>

      </view>
      <view class="scroll-box">
        <view class="content" @touchstart="scrollTouchstart" @touchend="scrollTouchend" :style="{
          left: -indicatorIndex * 750 + 'rpx',
          width: 750 * list.length + 'rpx',
        }" style="box-sizing: border-box; gap: 0rpx">
          <view v-for="(item, index) in list" :key="index"
            style="width: 750rpx; padding: 0 25rpx 25rpx; box-sizing: border-box">
            <view class="item" style="width: 100%">
              <view class="title">
                <text>{{ "0" + (index + 1) }}</text>
                <view class="type">{{
                  item.questionType === 'CHOICE'
                    ? '单选题'
                    : item.questionType === 'CHOICES'
                      ? '多选题'
                      : item.questionType === 'TRUE_FALSE'
                        ? '判断题'
                        : item.questionType === 'INDEFINITE_CHOICE'
                          ? '不定项选择题'
                          : '简答题'
                }}</view>
              </view>
              <mpHtml class="topic" :content="item.questionContent"></mpHtml>
              <TopicOptions v-if="answers.length" :options="item.options" :questionType="item.questionType"
                :answer="answers[indicatorIndex].answer.split('')"
                @selectedAnswer="(answer) => selectedAnswer(answer, index)"></TopicOptions>
              <view class="btn-submit" @click="submitAnswer(index)">{{
                index === list.length - 1 ? "提交答卷" : "下一题"
              }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="collect">
        <Collect v-if="list.length" :collectFlag="favoriteRecordVOS.includes(list[indicatorIndex].questionId)"
          :list="list" :questionId="list[indicatorIndex].questionId" questionSetType="MOCK_EXAMS"
          @questionFavorite="questionFavorite" @indicatorIndexChange="handleIndexChange" mode="EXAM"></Collect>
      </view>
    </view>
    <u-modal showCancelButton :show="modalShow" @cancel="modalShow = false" @confirm="modalConfirm">
      <view class="slot-content">
        {{
          answers.filter((e) => !e.answer).length
            ? `还有${answers.filter((e) => !e.answer).length}题未作答，确定交卷吗？`
            : "作答完毕，提交答卷"
        }}
      </view>
    </u-modal>

    <u-modal showCancelButton :show="modalTimeShow" @cancel="modalTimeShow = false" @confirm="modalTimeConfirm">
      <view class="slot-content">
        {{ isPaused ? '是否继续考试计时?' : '是否暂停考试计时?' }}
      </view>
    </u-modal>
  </view>
</template>

<script>
import Collect from '@/components/collect'
import homeApi from "@/api/home";
import TopicOptions from "@/components/topicOptions/index";
import mpHtml from "mp-html/dist/uni-app/components/mp-html/mp-html";
import studyTimer from '@/mixins/study-timer'

export default {
  mixins: [studyTimer],
  components: {
    Collect,
    TopicOptions,
    mpHtml,
  },
  data() {
    return {
      modalTimeShow: false,
      isPaused: false,
      timer: null,
      indicatorIndex: 0,
      startClientX: 0,
      list: [],
      answers: [],
      questionSetId: null,
      subjectCode: null,
      modalShow: false,
      scrollLeft: 0,
      loading: true,
      favoriteRecordVOS: [],
      examinationDuration: null,
      remaining: null,
      createTime: null
    };
  },
  computed: {
    enableAlertBeforeUnloadMessage() {
      return `还有${this.answers.filter((e) => !e.answer).length}题未作答，确定离开吗？`;
    },
  },
  watch: {
    indicatorIndex(newIndex, oldIndex) {
      this.$nextTick(() => {
        this.scrollLeft = newIndex >= 16 ? (newIndex - 15) * 36 + "rpx" : 0;
      });
    },
    answers: {
      handler(newAnswer, oldAnswer) {
        wx.enableAlertBeforeUnload({
          message: this.enableAlertBeforeUnloadMessage,
        });
      },
      deep: true,
    },
  },
  onLoad(option) {
    this.questionSetId = option.questionSetId;
    this.subjectCode = option.subjectCode;
    this.examinationDuration = (Number(option.examinationDuration) || 60) * 60
  },
  onShow() {
    const indicatorIndex = uni.getStorageSync('indicatorIndex')
    if (typeof indicatorIndex === 'number') {
      this.indicatorIndex = indicatorIndex
      uni.setStorageSync('indicatorIndex', '')
      return
    }
    this.testPracticeExamModeQuestions(this.questionSetId);
    this.getTime();
    this.createTime = this.formatDateTime()
  },
  methods: {
    stopTimer() {
      this.modalTimeShow = true
    },
    modalTimeConfirm() {
      this.isPaused = !this.isPaused
      if (this.isPaused) {
        clearInterval(this.timer);
        this.timer = null;
      } else {
        this.getTime();
      }
      this.modalTimeShow = false
    },
    getTime() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        if (!this.isPaused) {
          this.countDown();
        }
      }, 1000);
    },
    //倒计时
    countDown() {
      var dj = this.examinationDuration;
      this.remaining = this.djs(this.examinationDuration);
      if (dj <= 0) {
        this.remaining = '已结束';
        // 关闭定时器
        clearInterval(this.timer);
        this.modalConfirm()
      } else {
        var ddf = this.djs(dj); //格式化过后的时间
        this.remaining = ddf;
        //当前时间减去时间
        dj = dj - 1;
        this.examinationDuration = dj;
      }
    },
    //得到的秒换算成时分秒
    djs(ms) {
      var h = parseInt(ms / (60 * 60));
      var m = parseInt((ms % (60 * 60)) / 60);
      var s = (ms % (60 * 60)) % 60;
      if (h < 10) {
        h = '0' + h;
      }
      if (m < 10) {
        m = '0' + m;
      }
      if (s < 10) {
        s = '0' + s;
      }
      return h + ':' + m + ':' + s;
    },
    handleIndexChange(index) {
      this.indicatorIndex = index
      this.list = this.list.map((item, index) => {
        return { ...item, location: index === this.indicatorIndex }
      })
    },
    questionFavorite() {
      const { list, indicatorIndex, subjectCode, favoriteRecordVOS } = this
      const favorite = !favoriteRecordVOS.includes(
        list[indicatorIndex].questionId
      )
			const question = list[indicatorIndex]
			const params = {
				subjectCode: subjectCode,
				questionId: question.questionId,
				favorite: favorite,
				exerciseMode: 'MOCK_EXAMS',
				questionSetId: question.questionSetId
			}
			homeApi.bookQuestionFavorite(params).then(() => {
				if (favorite) {
            this.favoriteRecordVOS.push(list[indicatorIndex].questionId)
          } else {
            const index = this.favoriteRecordVOS.findIndex(id => id === list[indicatorIndex].questionId)
            if (index > -1) {
              this.favoriteRecordVOS.splice(index, 1)
            }
          }
			});
    },
    itemBackgroundColor(item) {
      if (item === this.indicatorIndex) {
        return "#000000";
      } else if (this.answers[item].answer) {
        return "#A2EF4D";
      } else {
        return "#E0E0E0";
      }
    },

    selectedAnswer(answer, index) {
      const json = { ...this.answers[index], answer };
      this.$set(this.answers, index, json);
      this.$set(this.list[index], 'isAnswered', !!answer)
    },

    scrollTouchstart(e) {
      this.startClientX = e.changedTouches[0].clientX;
    },

    scrollTouchend(e) {
      if (e.changedTouches[0].clientX > this.startClientX + 80) {
        if (this.indicatorIndex === 0) {
          return;
        }
        this.indicatorIndex--;
      } else if (e.changedTouches[0].clientX < this.startClientX - 80) {
        if (this.indicatorIndex === this.list.length - 1) {
          return;
        }
        this.indicatorIndex++;
      }
      this.list = this.list.map((item, index) => {
        return { ...item, location: index === this.indicatorIndex }
      })
    },

    testPracticeExamModeQuestions(questionSetId) {
      uni.showLoading();
      homeApi.testMockExamModeQuestions(questionSetId).then((res) => {
        this.answers = res.map((e) => ({
          questionId: e.questionId,
          answer: "",
        }));
        this.list = res.map((e) => {
          const questionContentFlag = e.questionContent.indexOf("img");
          if (questionContentFlag > -1) {
            e.questionContent =
              "<br>" +
              e.questionContent.slice(0, questionContentFlag + 3) +
              ' width="100%" height="100%"' +
              e.questionContent.slice(questionContentFlag + 3, e.questionContent.length);
          }
          for (let i in e.options) {
            const index = e.options[i].indexOf("img");
            if (index > -1) {
              e.options[i] =
                e.options[i].slice(0, index + 3) +
                ' width="30rpx" height="30rpx"' +
                e.options[i].slice(index + 3, e.options[i].length);
            }
          }
          const options = {};
          const arr = Object.keys(e.options).sort();
          for (let k = 0; k < arr.length; k++) {
            options[arr[k]] = e.options[arr[k]];
          }
          e.options = options;
          return e;
        });
        setTimeout(() => {
          this.loading = false;
          uni.hideLoading();
        }, 2000);
        wx.enableAlertBeforeUnload({
          message: this.enableAlertBeforeUnloadMessage,
        });
      });
    },

    // 添加格式化日期的函数
    formatDateTime() {
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    modalConfirm() {
      const { questionSetId, subjectCode, answers } = this;
      homeApi
        .testMockSubmitExamAnswer({
          questionSetId,
          subjectCode,
          answers,
          createTime: this.createTime
        })
        .then((res) => {
          uni.redirectTo({
            url:
              "/pages/MockExam/ExamResult/index?resultData=" +
              JSON.stringify({ ...res, subjectCode }),
          });
        });

      // 关闭定时器
      clearInterval(this.timer);
      this.timer = null;
    },

    submitAnswer(index) {
      this.list = this.list.map((item, index) => {
        return { ...item, location: index === this.indicatorIndex + 1 }
      })
      if (index < this.list.length - 1) {
        this.indicatorIndex++;
        return;
      }
      this.modalShow = true;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./index.less";
</style>
