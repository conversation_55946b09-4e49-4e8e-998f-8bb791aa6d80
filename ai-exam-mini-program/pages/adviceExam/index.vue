<template>
	<view class="container">
		<u-navbar :autoBack="true" bgColor="transparent" placeholder />
		<view class="greeting">
			<view><text>{{ nickname }} 您好，</text></view>
			<view v-if="showSuggest">
				<view><text>这段时间备考辛苦了~ 针对您的做题记录和错题记录已为您生成<text class="greeting_tips">专属备考建议</text>，请查收～</text></view>
				<view v-if="subjectPassRateType != '3'"><text
						class="greeting_zhuyi">注意：备考建议仅根据章节练习冲刺答题情况进行章节相关分析！</text></view>
			</view>
			<view v-else>
				<view><text>这段时间备考辛苦了~ </text></view>
				<view><text>抱歉！当前本科目暂时没有题目，我们无法对您的学习情况进行评估，并提供针对性的备考建议，我们将快马加鞭完善我们的题库，敬请期待！</text></view>
				<view><text>请您继续加油，每一次努力都会为你积累更多经验。一步一个脚印，相信自己，您一定能迎来成功！</text></view>
			</view>
		</view>

		<view class="card" v-if="showSuggest && subjectPassRateType != '3'">
			<view class="card-header card-title">
				<text class="iconfonts icon-yinletiaodongzhuangtai m-r-10"></text>
				<text>盲区扫描</text>
			</view>
			<view class="card-content">
				<view class="radar-chart" :style="{ height: chartWidth + 220 + 'rpx' }">
					<radar-canvas v-if="loadCharts" :data="chartData" :labels="chartLabels" :width="chartWidth"
						:height="chartWidth" :center-text="centerText" :center-text-color="centerTextColor" />
				</view>
				<view class="advice">
					<text>根据练题记录，预计您本科目考试通过率</text>
					<text :class="passRateColorClass" style="font-size:34rpx;">{{ passRateText }}</text>。
				</view>
				<view class="motivation">
					<text>{{ motivationalMessage }}</text>
				</view>
				<view class="recommendation">
					<text>建议您强化如下学习内容：</text>
				</view>
				<view v-for="(chapter, index) in chapterRecommendations" :key="index" class="chapter-recommendation">
					<view class="chapter-header" @tap="toggleChapter(index)">
						<text class="chapter-name">{{ truncateText(chapter.chapterName, 13) }}</text>
						<view class="section-links">
							<text :class="{
								'text-green': chapter.chartGraspType == '0',
								'text-orange': chapter.chartGraspType == '1',
								'text-red': chapter.chartGraspType == '2',
								'text-red': !['0', '1', '2'].includes(chapter.chartGraspType)
							}">
								{{ chapter.chartGraspDesc }}
							</text>
							<text
								v-if="onlyChart || !chapter.noHighSectionVOList || chapter.noHighSectionVOList.length == 0"
								class="link"
								@tap="navigateToRealQuestions(chapter.chapterCode, chapter.chapterName, '')">真题</text>
						</view>
					</view>
					<view
						v-if="expandedChapters[index] && !onlyChart && chapter.noHighSectionVOList && chapter.noHighSectionVOList.length > 0"
						class="chapter-content">
						<view v-for="(section, sectionIndex) in chapter.noHighSectionVOList" :key="sectionIndex"
							class="section">
							<text>{{ truncateText(section.sectionName, 11) }}</text>
							<view class="section-links">
								<text :class="{
									'text-green': section.sectionGraspType == '0',
									'text-orange': section.sectionGraspType == '1',
									'text-red': section.sectionGraspType == '2',
									'text-red': !['0', '1', '2'].includes(section.sectionGraspType)
								}">
									{{ section.sectionGraspDesc }}
								</text>
								<text class="link"
									@tap="navigateToRealQuestions(chapter.chapterCode, chapter.chapterName, section.sectionCode)">真题</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="card" v-if="showSuggest">
			<view class="card-header card-title">
				<text class="iconfonts icon-shuqian m-r-10"></text>
				<text>学习情况</text>
			</view>
			<view class="card-content">
				<view class="section-title">
					<text class="iconfonts icon-bianji1 m-r-10"></text>
					<text>练题情况分析</text>
				</view>
				<view v-for="(item, index) in exerciseData" :key="index" class="exercise-item">
					<view class="exercise-type">
						<!-- <text :class="{
							'iconfonts icon-zhangjielianxi m-r-10': item.type == '10',
							'iconfonts icon-linianzhenti m-r-10': item.type == '50',
							'iconfonts icon-kaoshi m-r-10': item.type == '40',
							'iconfonts icon-clipboard m-r-10': !['10', '40', '50'].includes(item.type)
						}" ></text> -->
						<text>{{ subjectName[item.type] }}</text>
					</view>
					<view class="progress-bars">
						<view class="progress-item">
							<text>做题进度</text>
							<progress :percent="item.answerRate" stroke-width="4" activeColor="#4686f4" />
							<view class="progress-labels">
								<text>{{ item.answerCount }}/{{ item.totalCount }}</text>
								<text>{{ item.answerRate.toFixed(2) }}%</text>
							</view>
						</view>
						<view class="progress-item">
							<text>错误率</text>
							<progress :percent="item.wrongRate" stroke-width="4"
								:activeColor="getErrorRateColor(item.wrongType)" />
							<view class="progress-labels">
								<text>{{ item.wrongCount }}/{{ item.answerCount }}</text>
								<text>{{ item.wrongRate.toFixed(2) }}%</text>
							</view>
						</view>
					</view>
					<view style="text-align: center;margin-top: 30rpx;">
						<text :class="['remarks', getRemarksByErrorRate(item.wrongType).color]">
							{{ getRemarksByErrorRate(item.wrongType).text }}
						</text>
					</view>
				</view>
				<view v-if="subjectPassRateType != '3'">
					<view class="section-title" style="color:#fa7272;margin-top: 50rpx;margin-bottom: 30rpx;">
						<text class="iconfonts icon-cuotifenxi m-r-10"></text>
						<text>错题分析</text>
					</view>
					<view v-if="passRateType == 0" class="error-analysis-intro">
						<text>当前本科目的整体错误率为：{{ Math.floor(passRate) }}%，您可点击练习错题进行针对性复习，攻克相关难点。</text>
					</view>
					<view v-else-if="passRateType == 1" class="error-analysis-intro">
						<text>当前本科目没有错题，太棒了，知识点都在掌握中，继续保持！</text>
						<text @tap.stop="skipStudy()" class="text-blue">去练习。</text>
					</view>
					<view v-else-if="passRateType == 2" class="error-analysis-intro">
						<text>当前本科目没有错题，做题能帮你更好的巩固知识，快去试试吧！</text>
						<text @tap.stop="skipStudy()" class="text-blue">去练习。</text>
					</view>
					<view v-for="(chapter, index) in chapterAnalysis" :key="index" class="chapter-analysis">
						<view class="chapter-header" @tap="toggleErrorChapter(index)">
							<text class="chapter-name">{{ truncateText(chapter.chapterName, 12) }}</text>
							<view class="chapter-status">
								<text :class="{
									'text-green': chapter.chartWrongType == '2',
									'text-orange': chapter.chartWrongType == '1',
									'text-red': chapter.chartWrongType == '0',
									'text-red': !['0', '1', '2'].includes(chapter.chartWrongType)
								}">
									{{ chapter.chartWrongDesc }}
								</text>
								<text class="practice-link"
									v-if="onlyChart || !chapter.sectionWrongRateInfoVOList || chapter.sectionWrongRateInfoVOList.length == 0"
									@tap.stop="practiceErrors(chapter.chapterCode, '')">错题</text>
							</view>
						</view>
						<view v-if="expandedErrorChapters[index] && !onlyChart" class="chapter-content">
							<view v-for="(section, sectionIndex) in chapter.sectionWrongRateInfoVOList"
								:key="sectionIndex" class="section">
								<text>{{ truncateText(section.sectionName, 11) }}</text>
								<view class="section-status">
									<text :class="{
										'text-green': section.sectionWrongType == '2',
										'text-orange': section.sectionWrongType == '1',
										'text-red': section.sectionWrongType == '0',
										'text-red': !['0', '1', '2'].includes(section.sectionWrongType)
									}">
										{{ section.sectionWrongDesc }}
									</text>
									<text class="practice-link"
										@tap.stop="practiceErrors('', section.sectionCode)">错题</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import homeApi from "@/api/home";
import radarCanvas from "@/components/radarCanvas/radarCanvas.vue";
import { mapState } from "vuex";
import studyTimer from '@/mixins/study-timer'
export default {
	mixins: [studyTimer],
	components: {
		radarCanvas,
	},
	data() {
		return {
			subjectCode: '',
			nickname: '学员',
			chartWidth: 340,
			chartData: [],
			chartLabels: [],
			centerText: "",
			centerTextColor: "",
			subjectPassRateDesc: '',
			subjectPassRateType: -1,
			loadCharts: false,
			passRateText: '',
			passRateType: -1,
			passRate: -1,
			exerciseData: [],
			subjectName: {
				"10": "章节精炼",
				"20": "每日一题",
				"30": "综合题",
				"40": "模拟考试",
				"50": "历年真题"
			},
			chapterAnalysis: [],
			chapterRecommendations: [],
			expandedChapters: {},
			expandedErrorChapters: {},
			// sectionId: -1,
			// wrongType: -1,
			showSuggest: true,
			onlyChart: false,
		};
	},
	computed: {
		...mapState("main", ["userInfo", "token"]),
		passRateColorClass() {
			return this.textColorClass(this.subjectPassRateType);
		},
		motivationalMessage() {
			if (this.passRateText == '较高') {
				return '继续保持这个节奏，你离成功只有一步之遥。坚持每天的努力，胜利就在前方';
			} else if (this.passRateText == '一般') {
				return '离成功只有一步之遥！在薄弱的阶段上多花一些时间，你会发现自己在进步的路上越走越远。我们一直在这里支持你！';
			} else if (this.passRateText == '偏低') {
				return '不必气馁，成功的道路从来都不平坦！虽然目前有些挑战，但每一次的努力都会为你积累更多的经验。一步一个脚印，相信自己，你一定能突破！';
			}
		},
	},
	onLoad(option) {
		this.subjectCode = option.subjectCode
		this.certificateCode = option.certificateCode
	},
	onReady() {
		// 获取屏幕宽度
		const systemInfo = uni.getSystemInfoSync();
		// 设置图表宽度为屏幕宽度的90%（您可以调整这个百分比）
		this.chartWidth = systemInfo.screenWidth * 0.9;
		this.getAdviceExam()
	},
	methods: {
		textColorClassDesc(type) {
			if (type == '2') return 'text-green';
			if (type == '1') return 'text-orange';
			if (type == '0') return 'text-red';
			return 'text-blue';
		},
		textColorDesc(type) {
			if (type == '2') return '#7ec6b8';
			if (type == '1') return '#efa25a';
			if (type == '0') return '#fa7272';
			return '#4686f4';
		},
		textColorClass(type) {
			if (type == '0') return 'text-green';
			if (type == '1') return 'text-orange';
			if (type == '2') return 'text-red';
			return 'text-blue';
		},
		textColor(type) {
			if (type == '0') return '#7ec6b8';
			if (type == '1') return '#efa25a';
			if (type == '2') return '#fa7272';
			return '#4686f4';
		},
		truncateText(text, maxLength = 12) {
			if (text.length > maxLength) {
				return text.slice(0, maxLength) + '...';
			}
			return text;
		},
		getAdviceExam() {
			this.loadCharts = false;
			homeApi.getAdviceExam(this.subjectCode).then((data) => {
				this.loadCharts = true;
				this.nickname = data.nickName || '学员'
				if (data.chartRadarList) {
					data.chartRadarList.forEach((item, index) => {
						this.chartData.push(item.radarValue);
						this.chartLabels.push(item.label);
					});
				}
				this.centerText = data.subjectPassRateDesc
				this.centerTextColor = this.textColor(data.subjectPassRateType)
				this.subjectPassRateType = data.subjectPassRateType
				this.passRateText = data.subjectPassRateDesc
				this.chapterRecommendations = data.noHighChartVOList;
				this.exerciseData = data.answerQuestionVO
				this.passRate = data.wrongRate;
				this.passRateType = data.errorAnalysisType;
				this.chapterAnalysis = data.chartWrongRateInfoVOList
				this.onlyChart = data.onlyChart
				if (data.chartRadarList) {
					this.$nextTick(() => {
						this.loadCharts = true;
					});
				}
				this.$nextTick(() => {
					if ((!this.chartData || this.chartData.length == 0) &&
						(!this.chapterRecommendations || this.chapterRecommendations.length == 0) &&
						(!this.exerciseData || this.exerciseData.length == 0) &&
						(!this.chapterAnalysis || this.chapterAnalysis.length == 0)) {
						this.showSuggest = false;
					}
				});
				this.$nextTick(() => {
					this.$forceUpdate();
				});
			}).catch(() => {
				this.loadCharts = false;
			})
		},
		getErrorRateColor(wrongType) {
			return this.textColorDesc(wrongType)
		},
		getRemarksByErrorRate(wrongType) {
			if (wrongType == '0') {
				return {
					text: '错误率较高，抓紧练习吧',
					color: 'text-red'
				};
			} else if (wrongType == '1') {
				return {
					text: '错误率一般，需继续强化',
					color: 'text-orange'
				};
			} else if (wrongType == '2') {
				return {
					text: '错误率较低，掌握良好',
					color: 'text-green'
				};
			} else {
				return {
					text: '还未做题，抓紧开始吧',
					color: 'text-red'
				};
			}
		},
		toggleChapter(index) {
			this.$set(this.expandedChapters, index, !this.expandedChapters[index]);
		},
		toggleErrorChapter(index) {
			this.$set(this.expandedErrorChapters, index, !this.expandedErrorChapters[index]);
		},
		navigateToRealQuestions(chapterCode, chapterName, sectionCode) {
			if (!sectionCode) {
				uni.navigateTo({
					url: '/pages/hierarchy/index?chapterCode=' + chapterCode + '&subjectCode=' + this.subjectCode + '&chapterName=' + chapterName + '&mode=EXAM&sectionCode=' + sectionCode
				})
				return
			}
			uni.navigateTo({
				url: '/pages/examQuestionInfo/index?mode=EXAM&chapterCode=&sectionCode=' + sectionCode + '&subjectCode=' + this.subjectCode
			})
		},
		practiceErrors(chapterCode, sectionCode) {
			uni.navigateTo({
				url: '/pages/WrongQuestions/index?mode=EXAM&subjectCode=' + this.subjectCode + '&sectionCode=' + sectionCode + '&examRecordId=null&mockExamRecordId=null&chapterCode=' + chapterCode
			})
		},
		skipStudy() {
			uni.navigateTo({
				url: `/pages/hierarchy/index?mode=EXAM&subjectCode=${this.subjectCode}&certificateCode=${this.certificateCode}`
			})
		},
	},
};
</script>
<style lang="less">
@import './index.less';
</style>