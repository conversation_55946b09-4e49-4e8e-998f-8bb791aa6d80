<template>
  <view class="page">
  
    <u-navbar :autoBack="false" bgColor="transparent" placeholder>
      <view slot="left"></view>
      <view slot="center" class="nav-title">建筑考试宝</view>
    </u-navbar>
    <view class="select">
      <view class="select-box" @click="show = !show">
        <text>{{ name }}</text>
        <u-icon name="arrow-down" size="36rpx"></u-icon>
      </view>
      <!-- <view class="select-box" @click="show2 = !show2">
        <text>{{ name2 }}</text>
        <u-icon name="arrow-down" size="36rpx"></u-icon>
      </view> -->
    </view>
    <view class="swiper-content">
      <u-swiper
        :list="bannerList"
        radius="16rpx"
        indicatorMode="line"
        circular
        @change="(e) => (current = e.current)"
      ></u-swiper>
    </view>
    <view class="indicator">
      <view
        v-for="index in bannerList.length"
        :key="index"
        :class="{ selected: current === index }"
      ></view>
    </view>
    <view class="list-content">
      <view
        class="title"
        v-for="(item, index) in listData"
        :key="index"
        @click="
          toPage(
            item.toPage,
            item.toBar,
            item.subjectCode,
            item.expect,
            item.currentType
          )
        "
      >
        <image
          :src="item.icon"
          :style="{ height: item.height, width: item.width }"
        ></image>
        {{ item.title }}
        <mpHtml class="memo" :content="item.memo"></mpHtml>
      </view>
    </view>
    <u-picker
      :show="show"
      :columns="columns"
      @cancel="show = false"
      @confirm="pickerConfirm"
      keyName="certificateName"
    ></u-picker>
    <u-picker
      :show="show2"
      :columns="columns2"
      @cancel="show2 = false"
      @confirm="pickerConfirm2"
      keyName="subjectName"
    ></u-picker>
  </view>
</template>

<script>
import homeApi from '@/api/home'
import { mapState } from 'vuex'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

export default {
  components: {
    mpHtml
  },
  data() {
    return {
      show: false,
      columns: [[]],
      name: '',
      current: 0,
      listData: [
        {
          title: 'AI诊断',
          memo: '精准学情诊断<br />开启个性化备考学习',
          toPage: '/pages/personalise/index',
          icon: 'https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/home/<USER>',
          height: '85rpx',
          width: '100rpx'
        },
        {
          title: 'AI智学',
          memo: 'AI提炼重点考点<br />把“厚书”变“薄”',
          toBar: '/pages/type/index',
          icon: 'https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/home/<USER>',
          height: '80rpx',
          width: '120rpx',
          currentType: 1
        },
        {
          title: 'AI智练',
          memo: 'AI智能筛选，精准练题<br />告别冗余，专注核心',
          toBar: '/pages/type/index',
          icon: 'https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/home/<USER>',
          height: '97rpx',
          width: '66rpx',
          currentType: 2
        },
        {
          title: 'AI备考建议',
          memo: '针对性查漏补缺<br />让学习成果稳步增长',
          toPage: '/pages/ExamPreparation/index',
          icon: 'https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/home/<USER>',
          height: '85rpx',
          width: '83rpx'
        },
        {
          title: 'AI助教',
          memo: '7*24小时<br />贴身学习好帮手',
          icon: 'https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/home/<USER>',
          height: '95rpx',
          width: '73rpx',
          expect: true
        },
        {
          title: '错题收藏',
          memo: '精准复盘针对性复习<br />掌握正确的解题思路',
          // toPage: '/pages/WrongCollect/index?subjectCode=' + this.subjectCode,
          icon: 'https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/home/<USER>',
          height: '88rpx',
          width: '70rpx',
          subjectCode: true
        }
      ],
      bannerList: [],
      certificateCode: '',
      subjectCode: '',
      show2: false,
      name2: '',
      columns2: [[]]
    }
  },
  onShareAppMessage() {
    return {
      title: '',
      imageUrl: '',
      path: '',
      success: function () {
        uni.showToast({
          title: '分享成功',
          icon: 'none'
        })
      }
    }
  },
  onShareTimeline() {
    return {
      title: '',
      imageUrl: '',
      path: '',
      success: function () {
        uni.showToast({
          title: '分享成功',
          icon: 'none'
        })
      }
    }
  },
  computed: {
    ...mapState('main', ['userInfo', 'token'])
  },
  onLoad() {
    if (
      (!uni.getStorageSync('token') || !uni.getStorageSync('phone')) &&
      !uni.getStorageSync('toLogin')
    ) {
      uni.navigateTo({
        url: '/pages/login/index'
      })
    }
    this.homeBanners()
    this.commonCertificates()
  },
  methods: {
    expect() {
      uni.showToast({
        title: '敬请期待',
        duration: 2000,
        icon: 'none'
      })
    },

    toPage(page, bar, subjectCode, expect, currentType) {
      if (currentType) {
        uni.setStorageSync('currentType', currentType)
      }
      if (expect) {
        this.expect()
      }
      if (subjectCode) {
        this.show2 = true
      }
      if (page) {
        uni.navigateTo({
          url: page
        })
      }
      if (bar) {
        uni.switchTab({
          url: bar
        })
      }
    },

    commonCertificates() {
      homeApi.commonCertificates().then((res) => {
        this.columns[0] = res
        this.name = res[0].certificateName
        this.certificateCode = res[0].certificateCode
        this.columns2[0] = res[0].subjects
        // this.name2 = res[0].subjects[0].subjectName
        // this.subjectCode = res[0].subjects[0].subjectCode
        // console.log('this.subjectCode', this.subjectCode)
      })
    },

    homeBanners() {
      homeApi.homeBanners().then((res) => {
        this.bannerList = res.map((e) => e.linkUrl)
      })
    },

    pickerConfirm(data) {
      this.name = data.value[0].certificateName
      this.certificateCode = data.value[0].certificateCode
      this.show = false
      this.columns2[0] = data.value[0].subjects
      this.name2 = data.value[0].subjects[0].subjectName
      this.subjectCode = data.value[0].subjects[0].subjectCode
    },

    pickerConfirm2(data) {
      this.name2 = data.value[0].subjectName
      this.subjectCode = data.value[0].subjectCode
      this.show2 = false
      uni.navigateTo({
        url: '/pages/WrongCollect/index?subjectCode=' + this.subjectCode
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>
