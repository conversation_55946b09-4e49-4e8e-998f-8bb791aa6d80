.page {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #6696FA 0%, rgba(255, 255, 255, 0.2) 100%);
  overflow: hidden;
  padding:0 33rpx 53rpx;
  box-sizing: border-box;

  .item {
    background-color: #FFFFFF;
    border-radius: 16rpx;
    width: 100%;
    height: 150rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 35rpx;
    box-sizing: border-box;
    margin-bottom: 28rpx;

    .title {
      width: 550rpx;
      font-size: 32rpx;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .info {
      color: #5E92FF;
      font-size: 25rpx;
      margin-top: 26rpx;
    }

    image {
      width: 22rpx;
      height: 40rpx;
    }
  }
}