<template>
  <view class="page" style="padding: 20rpx 0">
    <u-navbar placeholder @leftClick="leftClick">
      <view slot="center" class="nav-title">{{
        wrongFlag ? '' : '练习模式'
      }}</view>
    </u-navbar>
    <view v-show="!loading">
      <view style="padding-left: 25rpx; padding-right: 25rpx; box-sizing: border-box">

        <view class="scroll-indicator" v-if="list.length">
          <view class="indicator-container">

            <view class="page-count">
              {{ indicatorIndex + 1 }} / {{ list.length }}
            </view>


            <view class="progress-bar">
              <view class="progress-inner" :style="{ width: `${(indicatorIndex + 1) / list.length * 100}%` }"></view>
            </view>

            <view class="stats-left">
              <u-icon name="checkmark-circle" size="50rpx" color="#9AE137"></u-icon>
              <text>{{ completedQuestions.filter((e) => e.isRight).length }}</text>
              <u-icon name="close-circle" size="50rpx" color="#D30309"></u-icon>
              <text>{{ completedQuestions.filter((e) => !e.isRight).length }}</text>
            </view>

          </view>
        </view>

      </view>
      <view class="scroll-box">
        <view class="content" :style="{
          left: -indicatorIndex * 750 + 'rpx',
          width: 750 * list.length + 'rpx'
        }" style="box-sizing: border-box; gap: 0rpx" @touchstart="scrollTouchstart" @touchend="scrollTouchend">
          <view v-for="(item, index) in list" :key="index"
            style="width: 750rpx; padding: 0 25rpx 25rpx; box-sizing: border-box">
            <view v-if="forceRefresh" class="item" style="width: 100%">
              <view class="title">
                <text>{{ '0' + (index + 1) }}</text>
                <view class="type">{{
                  item.questionType === 'CHOICE'
                    ? '单选题'
                    : item.questionType === 'CHOICES'
                      ? '多选题'
                      : item.questionType === 'TRUE_FALSE'
                        ? '判断题'
                        : item.questionType === 'INDEFINITE_CHOICE'
                          ? '不定项选择题'
                          : '简答题'
                }}</view>
              </view>
              <mpHtml class="topic" lazy-load :content="item.questionContent"></mpHtml>
              <view v-if="!wrongFlag">
                <TopicOptions :options="item.options" :questionType="item.questionType" :answer="item.answer.split('')"
                  @selectedAnswer="(answer) => selectedAnswer(answer, index)" :disabled="item.analysis.rightAnswer"></TopicOptions>
                <view class="btn-submit" v-if="!item.analysis.rightAnswer" @click="submitAnswer(item, index)">{{
                  item.questionType !== 'OPEN' ? '提交答案' : '查看答案' }}
                </view>
                <view v-else>
                  <view v-show="answerFlag">
                    <view class="answer" v-if="item.questionType !== 'OPEN'">
                      <view>
                        <text>【正确答案】</text>
                        <mpHtml style="color: rgb(37, 81, 246)" class="analysis"
                          :content="item.rightAnswer || item.analysis.rightAnswer"></mpHtml>
                      </view>
                      <view>
                        <text>【你的答案】</text>
                        <text :style="{
                          color: item.analysis.isRight ? '#A2EF4D' : '#D30309'
                        }">{{ desc(item.answer) || '未作答' }}</text>
                      </view>
                    </view>
                    <view v-else>
                      <view style="margin: 20rpx;"><text>【正确答案】</text></view>
                      <mpHtml style="color: rgb(37, 81, 246)" :content="item.rightAnswer || item.analysis.rightAnswer">
                      </mpHtml>
                    </view>
                    <mpHtml class="analysis" lazy-load :content="item.analysis.analysis"></mpHtml>
                    <view class="btn-next">
                      <view v-if="indicatorIndex" @click="indicatorIndexChange('reduce')">上一题</view>
                      <view v-if="indicatorIndex < list.length - 1" @click="indicatorIndexChange('add')">下一题</view>
                      <view v-if="indicatorIndex === list.length - 1" @click="toResult">点击查看结果</view>
                    </view>
                  </view>
                </view>
              </view>
              <view v-else>
                <TopicOptions :options="item.options" :questionType="item.questionType"
                  :answer="item.userAnswer.split('')" :disabled="wrongFlag"></TopicOptions>
                <view class="answer" v-if="item.questionType !== 'OPEN'">
                  <view>
                    <text>【正确答案】</text>
                    <text style="color: rgb(37, 81, 246)">{{
                      item.rightAnswer
                    }}</text>
                  </view>
                  <view>
                    <text>【你的答案】</text>
                    <text :style="{ color: item.isRight ? '#A2EF4D' : '#D30309' }">{{ desc(item.userAnswer) || '未作答'
                      }}</text>
                  </view>
                </view>
                <view v-else>
                  <view style="margin: 20rpx;"><text>【正确答案】</text></view>
                  <mpHtml style="color: rgb(37, 81, 246)" :content="item.rightAnswer"></mpHtml>
                </view>
                <mpHtml lazy-load class="analysis" :content="item.analysis"></mpHtml>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="collect">
        <Collect v-if="list.length && !wrongFlag" :correct="completedQuestions.filter((e) => e.isRight).length"
          :mistake="completedQuestions.filter((e) => !e.isRight).length" :collectFlag="favoriteRecordVOS.includes(list[indicatorIndex].questionId)
            " :list="list" :questionId="list[indicatorIndex].questionId" questionSetType="ZTYLLXMS"
          @questionFavorite="questionFavorite" @indicatorIndexChange="handleIndexChange" mode="PRACTICE"></Collect>

      </view>
    </view>
  </view>
</template>

<script>
import Collect from '@/components/collect'
import homeApi from '@/api/home'
import TopicOptions from '@/components/topicOptions/index'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import studyTimer from '@/mixins/study-timer'

export default {
  mixins: [studyTimer],
  components: {
    Collect,
    TopicOptions,
    mpHtml
  },
  data() {
    return {
      indicatorIndex: 0,
      startClientX: 0,
      list: [],
      questionSetId: null,
      subjectCode: null,
      wrongFlag: null,
      favoriteRecordVOS: [],
      completedQuestions: [],
      batchNum: null,
      needShow: false,
      scrollLeft: 0,
      loading: true,
      answerFlag: true,
      forceRefresh: true
    }
  },
  watch: {
    indicatorIndex(newIndex, oldIndex) {
      homeApi.commonLocation({
        exerciseMode: 'ZTYLLXMS',
        questionSetId: this.questionSetId,
        questionId: this.list[newIndex].questionId
      })
      this.$nextTick(() => {
        this.scrollLeft = newIndex >= 16 ? (newIndex - 15) * 36 + 'rpx' : 0
      })
    }
  },
  onLoad(option) {
    this.questionSetId = option.questionSetId
    this.subjectCode = option.subjectCode
    this.wrongFlag = option.wrongFlag
    this.batchNum = option.batchNum
    this.generateBatchNum()
  },
  onShow() {
    const indicatorIndex = uni.getStorageSync('indicatorIndex')
    if (typeof indicatorIndex === 'number') {
      this.indicatorIndex = indicatorIndex
      uni.setStorageSync('indicatorIndex', '')
      return
    }
    if (this.wrongFlag) {
      this.wrongQuestions()
    } else {
      this.testPracticeQuestions(this.questionSetId, this.subjectCode, true)
    }
  },
  methods: {
    handleIndexChange(index) {
      this.indicatorIndex = index
      this.list = this.list.map((item, index) => {
        return { ...item, location: index === this.indicatorIndex }
      })
    },
    itemBackgroundColor(item) {
      if (item === this.indicatorIndex) {
        return '#000000'
      } else if ((this.list[item].answer && this.list[item].analysis.rightAnswer) || this.list[item].userAnswer) {
        return '#A2EF4D'
      } else {
        return '#E0E0E0'
      }
    },

    generateBatchNum() {
      homeApi.generateBatchNum('ZTYLLXMS').then((e) => {
        this.batchNum = e.batchNum
      })
    },

    indicatorIndexChange(type) {
      if (type === 'add' && this.indicatorIndex < this.list.length - 1) {
        this.indicatorIndex++
      } else if (type === 'reduce' && this.indicatorIndex) {
        this.indicatorIndex--
      }
      this.list = this.list.map((item, index) => {
        return { ...item, location: index === this.indicatorIndex }
      })
      // 判断下一题是否简答题，如果是则走下面这块，否则就不动
      if (this.list[this.indicatorIndex].questionType === 'OPEN') {
        this.forceRefresh = false
        this.$nextTick(() => {
          this.forceRefresh = true
        })
      }
    },

    selectedAnswer(answer, index) {
      const json = { ...this.list[index], answer }
      this.$set(this.list, index, json)
    },

    leftClick() {
      if (this.needShow) {
        const { questionSetId, batchNum, subjectCode } = this
        uni.redirectTo({
          url:
            '/pages/Result/index?questionSetId=' +
            questionSetId +
            '&batchNum=' +
            batchNum +
            '&subjectCode=' +
            subjectCode
        })
      } else {
        uni.navigateBack()
      }
    },

    scrollTouchstart(e) {
      this.startClientX = e.changedTouches[0].clientX
    },

    scrollTouchend(e) {
      if (e.changedTouches[0].clientX > this.startClientX + 80) {
        if (this.indicatorIndex === 0) {
          return
        }
        this.indicatorIndex--
      } else if (e.changedTouches[0].clientX < this.startClientX - 80) {
        if (this.indicatorIndex === this.list.length - 1) {
          return
        }
        this.indicatorIndex++
      }
      this.list = this.list.map((item, index) => {
        return { ...item, location: index === this.indicatorIndex }
      })
    },

    desc(answer) {
      return typeof answer === 'string'
        ? answer
        : answer.join().replace(/,/g, '')
    },

    questionFavorite() {
      const { list, indicatorIndex, subjectCode, favoriteRecordVOS } = this
      const favorite = !favoriteRecordVOS.includes(
        list[indicatorIndex].questionId
      )
      const question = list[indicatorIndex]
      const params = {
        subjectCode: subjectCode,
        questionId: question.questionId,
        favorite: favorite,
        exerciseMode: 'ZTYLLXMS',
        questionSetId: question.questionSetId
      }
      homeApi.bookQuestionFavorite(params).then(() => {
        this.testPracticeQuestions(this.questionSetId, this.subjectCode)
      });
    },

    submitAnswer(item, answerIndex) {
      const { subjectCode, questionSetId, batchNum } = this
      if (!item.answer && item.questionType !== 'OPEN') {
        uni.showToast({
          title: '请作答',
          duration: 2000,
          icon: 'none'
        })
        return
      }
      this.answerFlag = false
      const answer = item.answer
      homeApi
        .testPracticeSubmitAnswer({
          subjectCode,
          questionSetId,
          answer,
          questionId: item.questionId,
          batchNum
        })
        .then((e) => {
          const index = e.analysis.indexOf('img')
          if (index > -1) {
            e.analysis =
              '<br />' +
              e.analysis.slice(0, index + 3) +
              ' width="100%" height="100%"' +
              e.analysis.slice(index + 3, e.analysis.length)
          }
          this.needShow = true
          this.list[answerIndex].analysis = e
          this.testPracticeQuestions(this.questionSetId, this.subjectCode)
        })
    },

    toResult() {
      const { questionSetId, indicatorIndex, batchNum, subjectCode } = this
      if (indicatorIndex === this.list.length - 1) {
        homeApi
          .testPracticeExamPracticeInfo(questionSetId, batchNum)
          .then((res) => {
            if (res.needShow) {
              uni.redirectTo({
                url:
                  '/pages/Result/index?questionSetId=' +
                  questionSetId +
                  '&batchNum=' +
                  batchNum +
                  '&subjectCode=' +
                  subjectCode
              })
            } else {
              uni.showToast({
                title: '本次未做答',
                duration: 2000,
                icon: 'none'
              })
            }
          })
      }
    },

    wrongQuestions() {
      uni.showLoading()
      const { questionSetId, batchNum } = this
      homeApi.wrongQuestions(questionSetId, batchNum).then((res) => {
        this.list = res.map((e) => {
          const questionContentFlag = e.questionContent.indexOf('img')
          if (questionContentFlag > -1) {
            e.questionContent =
              '<br>' +
              e.questionContent.slice(0, questionContentFlag + 3) +
              ' width="100%" height="100%"' +
              e.questionContent.slice(
                questionContentFlag + 3,
                e.questionContent.length
              )
          }
          const index = e.analysis.indexOf('img')
          if (index > -1) {
            e.analysis =
              '<rb>' +
              e.analysis.slice(0, index + 3) +
              ' width="100%" height="100%"' +
              e.analysis.slice(index + 3, e.analysis.length)
          }
          for (let i in e.options) {
            const index = e.options[i].indexOf('img')
            if (index > -1) {
              e.options[i] =
                e.options[i].slice(0, index + 3) +
                ' width="30rpx" height="30rpx"' +
                e.options[i].slice(index + 3, e.options[i].length)
            }
          }
          const options = {}
          const arr = Object.keys(e.options).sort()
          for (let k = 0; k < arr.length; k++) {
            options[arr[k]] = e.options[arr[k]]
          }
          e.options = options
          return { ...e }
        })
        setTimeout(() => {
          this.loading = false
          uni.hideLoading()
        }, 2000)
      })
    },

    testPracticeQuestions(questionSetId, subjectCode, flag) {
      if (flag) {
        uni.showLoading()
      }
      homeApi.testPracticeQuestions(questionSetId, subjectCode).then((res) => {
        if (!this.list.length) {
          this.list = res.allQuestions.map((e) => {
            const questionContentFlag = e.questionContent.indexOf('img')
            if (questionContentFlag > -1) {
              e.questionContent =
                '<br>' +
                e.questionContent.slice(0, questionContentFlag + 3) +
                ' width="100%" height="100%"' +
                e.questionContent.slice(
                  questionContentFlag + 3,
                  e.questionContent.length
                )
            }
            for (let i in e.options) {
              const index = e.options[i].indexOf('img')
              if (index > -1) {
                e.options[i] =
                  e.options[i].slice(0, index + 3) +
                  ' width="30rpx" height="30rpx"' +
                  e.options[i].slice(index + 3, e.options[i].length)
              }
            }
            const options = {}
            const arr = Object.keys(e.options).sort()
            for (let k = 0; k < arr.length; k++) {
              options[arr[k]] = e.options[arr[k]]
            }
            e.options = options
            return { ...e, answer: '', analysis: {} }
          })
          this.indicatorIndex = res.allQuestions.findIndex((e) => e.location)
        }
        this.favoriteRecordVOS = res.favoriteRecordVOS.map((e) => e.questionId)
        this.completedQuestions = res.completedQuestions
        this.completedQuestions.forEach((e) => {
          for (let i in e) {
            if (typeof e[i] === 'string') {
              const index = e[i].indexOf('img')
              if (index > -1) {
                e[i] =
                  '<br />' +
                  e[i].slice(0, index + 3) +
                  ' width="100%" height="100%"' +
                  e[i].slice(index + 3, e[i].length)
              }
            }
          }
          const index = this.list.findIndex(
            (i) => i.questionId === e.questionId
          )
          this.$set(this.list, index, {
            ...this.list[index],
            analysis: e,
            answer: e.selectedOption
          })
        })
        setTimeout(() => {
          this.loading = false
          if (flag) {
            uni.hideLoading()
          }
        }, 1500)
        setTimeout(() => {
          this.answerFlag = true
        }, 600)
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>
