<template>
    <view class="feedback-container">
        <u-navbar placeholder :autoBack="true">
            <view slot="center" class="nav-title">纠错列表</view>
        </u-navbar>
        <view class="feedback-list">
            <view class="feedback-item" v-for="(item, index) in feedbackList" :key="index" @click="goToDetail(item)">
                <view class="content">
                    <text class="feedback-text">{{item.beforeQuestionInfo.content}}</text>
                </view>
                <view class="footer">
                    <text class="time">纠错类型：{{ item.feedbackTypeDesc }}</text>
                    <text class="status" :class="item.status === 1 ? 'processed' : ''">
                        {{ item.status === 1 ? '已回复' : '未回复' }}
                    </text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import homeApi from "@/api/home";
export default {
    components: {
        mpHtml
    },
    data() {
        return {
            feedbackList: []
        }
    },
    onShow() {
        this.queryFeedbackList()
    },
    methods: {
        queryFeedbackList() {
            homeApi.queryFeedbackList().then(res => {
                this.feedbackList = res
                console.log(this.feedbackList)
            })
        },
        goToDetail(item) {
            // 将对象转为字符串进行传递
            const itemStr = encodeURIComponent(JSON.stringify(item))
            uni.navigateTo({
                url: `/pages/FeedbackDetail/index?detail=${itemStr}`
            })
        }
    }
}
</script>

<style lang="less">
@import './index.less';
</style>
