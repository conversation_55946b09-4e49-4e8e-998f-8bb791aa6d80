.feedback-container {
//   min-height: 100vh;
  background-color: #f5f5f5;
  

  // 顶部标题栏样式
  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    // height: 88rpx;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    
    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
  }
  
  // 反馈列表样式
  .feedback-list {
    padding: 20rpx 20rpx 20rpx;
    
    .feedback-item {
      margin-bottom: 20rpx;
      background-color: #ffffff;
      border-radius: 12rpx;
      padding: 24rpx;
      
      // 内容区域样式
      .content {
        .feedback-text {
          font-size: 28rpx;
          color: #333333;
          line-height: 1.6;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        // 图片网格样式
        .image-grid {
          display: flex;
          flex-wrap: wrap;
          margin-top: 16rpx;
          
          .feedback-image {
            width: 200rpx;
            height: 200rpx;
            margin-right: 10rpx;
            margin-bottom: 10rpx;
            border-radius: 8rpx;
            
            &:nth-child(3n) {
              margin-right: 0;
            }
          }
        }
      }
      
      // 底部信息栏样式
      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;
        padding-top: 16rpx;
        border-top: 1rpx solid #eeeeee;
        
        .time {
          font-size: 24rpx;
          color: #999999;
        }
        
        .status {
          font-size: 24rpx;
          color: #ff6b00;
          
          &.processed {
            color: #52c41a;
          }
        }
      }
    }
  }
}