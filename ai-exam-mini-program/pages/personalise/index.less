.container {
  min-height: 100vh;
  background-color: #fff8f1;
  padding: 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.card {
  margin: 30rpx auto;
  width: 93%;
  background-color: #fff;
  border: 2rpx solid #ffe4cc;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-content {
  padding: 48rpx 60rpx;
  padding-bottom: 50rpx;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  width: 100%;
  padding-top: 30rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #451a03;
}

.description {
  margin-top: 30rpx;
  font-size: 30rpx;
  color: #4b5563;
  line-height: 1.8;
  text-align: left;
}

.feature-list {
  width: 100%;
  margin-top: 48rpx;
}

.feature-item {
  margin-bottom: 48rpx;
  text-align: left;
}

.feature-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #7c2d12;
  
  .iconfont3 {
    color: #f97316;
    font-size: 40rpx;
    font-weight: normal;
  }
}

.feature-desc {
  font-size: 28rpx;
  color: #4b5563;
  margin-top: 15rpx;
}

.submit-btn {
  margin-top: 60rpx;
  width: 93%;
  background-color: #f97316;
  color: #fff;
  padding: 6rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 16rpx;
  border: none;
  text-align: center;

  &:active {
    background-color: #ea580c;
  }
}