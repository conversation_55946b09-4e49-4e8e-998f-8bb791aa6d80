.page {
  display: flex;
  flex-direction: column;
  padding: 20rpx 25rpx;
  box-sizing: border-box;
  overflow-x: hidden;
  width: 100vw;
  height: 100vh;

  .scroll-indicator {
    display: flex;
    padding: 30rpx;
    justify-content: space-between;
    align-items: center;

    scroll-view {
      width: calc(100% - 100rpx);
    }

    .indicator-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20rpx;
      width: 100%;
    }
    
    .stats-left {
      display: flex;
      align-items: center;
      gap: 10rpx;
    }
    
    .progress-bar {
      flex: 1;
      height: 10rpx;
      background-color: #eee;
      border-radius: 5rpx;
      margin: 0 30rpx;
      overflow: hidden;
    }
    
    .progress-inner {
      height: 100%;
      background-color: #9AE137;
      border-radius: 5rpx;
      transition: width 0.3s;
    }
    
    .page-count {
      font-size: 32rpx;
      white-space: nowrap;
    }
  }

  .indicator {
    align-items: center;
    display: flex;
    justify-content: space-between;

    .item {
      height: 4rpx;
      border-radius: 2rpx;
    }
  }

  .scroll-box {
    width: 100%;
    height: calc(100vh - 360rpx);
    position: relative;
  }

  .content {
    position: absolute;
    top: 0;
    width: calc(725rpx * 8 - 25rpx);
    display: flex;
    gap: 25rpx;
    transition: .3s;

    .item {
      width: 700rpx;
      height: calc(100vh - 360rpx);
      padding: 30rpx;
      box-sizing: border-box;
      box-shadow: 0 0 10px 0px #efefef;
      border-radius: 16rpx;
      overflow: hidden;
      overflow-y: auto;
      position: relative;

      .title {
        font-size: 32rpx;
        display: flex;
        align-items: center;
        gap: 20rpx;

        .type {
          width: 250rpx;
          line-height: 42rpx;
          text-align: center;
          background-color: #E0E0E0;
          border-radius: 25rpx;
          font-size: 28rpx;
        }
      }

      .topic {
        font-size: 32rpx;
        line-height: 50rpx;
        margin: 30rpx 0;
      }

      .btn-submit {
        margin: 60rpx auto 30rpx;
        width: 200rpx;
        text-align: center;
        line-height: 70rpx;
        font-size: 30rpx;
        background-color: #5E92FF;
        color: #FFFFFF;
        border-radius: 10rpx;
      }

      .answer {
        display: flex;
        justify-content: space-around;
        padding: 20rpx;

        view {
          width: 260rpx;
          height: 120rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-size: 30rpx;
          flex-direction: column;
          box-shadow: 0 0 10px 0px #a8a8a8;
          border-radius: 16rpx;
        }
      }

      .analysis {
        font-size: 28rpx;
        line-height: 60rpx;
      }

      .btn-next {
        margin: 100rpx auto 30rpx;
        text-align: center;
        font-size: 28rpx;
        text-align: center;
        width: 350rpx;
        line-height: 60rpx;
        border-radius: 30rpx;
        border: 2rpx solid rgb(37,81,246);
      }
    }
  }
}