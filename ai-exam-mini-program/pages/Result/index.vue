<template>
  <view class="diagnostic-result">
    <u-navbar :autoBack="true" bgColor="transparent" placeholder>
      <view slot="center" class="nav-title">本次练题情况</view>
    </u-navbar> 
        <!-- 数据概览卡片 -->
    <view class="overview-card">
      <view class="grid">
        <view class="grid-item">
          <view class="header">
            <text class="iconfont3 shijian" icon-refresh style="font-size: 52rpx;"></text>
            <text class="label">测试用时</text>
          </view>
          <text class="value">{{ result.practiceTime }}</text>
        </view>
        <view class="grid-item">
          <view class="header">
            <text class="iconfont3 duohangwenben" icon-refresh style="font-size: 44rpx;"></text>
            <text class="label">答题总数</text>
          </view>
          <text class="value">{{ result.totalQuestionCount }}题</text>
        </view>
        <view class="grid-item">
          <view class="header" @click="toWrongQuestions(result.wrongQuestionCount)"> 
            <text class="iconfont3 wodecuoti" icon-refresh style="font-size: 39rpx;"></text>
            <text class="label">错题数量</text>
          </view>
          <view class="value-row" @click="toWrongQuestions(result.wrongQuestionCount)">
            <text class="value">{{ result.wrongQuestionCount }}题</text>
            <text class="iconfont icon-iconqianjin arrow-icon"></text>
          </view>
        </view>
        <view class="grid-item">
          <view class="header">
            <text class="iconfont3 a-zhengqueshuai2x" icon-refresh style="font-size: 36rpx;"></text>
            <text class="label">正确率</text>
          </view>
          <text class="value correct-rate">{{ result.accuracy }}</text>
        </view>
      </view>
      <view class="progress-wrapper">
        <progress 
          :percent="Number(result.accuracy.replace('%', ''))"
          stroke-width="12" 
          activeColor="#F97316"
          backgroundColor="#FED7AA"
          border-radius="10"
        />
      </view>
    </view>

    <view class="footer">
       <view class="err" >
        <button class="start-btn1" @tap="toWrongCollect"  >
            <text>去错题本</text>
      </button>
      </view>
     <view class="confirm" > 
      <image src="https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/ai-icon.png"></image>
       <button class="start-btn" @tap="toRightRate">
          <text>提升正确率</text>
      </button> 
    </view>
    <view class="footer-text">
      <text>每一次努力都是成功的的见证，坚持就有收获，朝着上岸继续冲呀！</text>
    </view>
</view>
</view>
</template>

<script>
import homeApi from "@/api/home"
import studyTimer from '@/mixins/study-timer'

export default {
  mixins: [studyTimer],
  data() {
    return {
      questionSetId: null,
      batchNum: null,
      result: {},
      sectionCode: '',
      chapterCode: '',
      subjectCode: null,
      aiExerciseRecordId: null,
      mode: null
    }
  },
  onLoad(option) {
    this.questionSetId = option.questionSetId
    this.batchNum = option.batchNum
    this.sectionCode = option.sectionCode
    this.chapterCode = option.chapterCode
    this.subjectCode = option.subjectCode
    this.aiExerciseRecordId = option.aiExerciseRecordId
    this.mode = option.mode
  },
  onShow() {
    if (this.mode === 'ZJLX') {
      this.chapterQuestionsExamPracticeInfo()
    } else if (this.mode === 'EXAM') {
      this.chapterQuestionsExamPracticeInfoReal()
    } else if (this.questionSetId) {
      this.testPracticeExamPracticeInfo(this.questionSetId, this.batchNum)
    } else if (this.sectionCode) {
      this.examPracticeInfo(this.sectionCode, this.chapterCode, this.batchNum)
    } else if (this.aiExerciseRecordId) {
      this.exerciseAiPracticeInfo()
    }
  },
  methods: {
    toRightRate() {
      uni.showToast({
        title: '研发中，敬请期待！',
        icon: "none",
      })
    },
    chapterQuestionsExamPracticeInfo() {
      homeApi.chapterQuestionsExamPracticeInfo(this.sectionCode, this.chapterCode, this.batchNum).then(res => {
        this.result = res
      })
    },

    chapterQuestionsExamPracticeInfoReal() {
      homeApi.chapterQuestionsExamPracticeInfoReal(this.sectionCode, this.chapterCode, this.batchNum).then(res => {
        this.result = res
      })
    },

    exerciseAiPracticeInfo() {
      homeApi.exerciseAiPracticeInfo(this.aiExerciseRecordId, this.batchNum).then(res => {
        this.result = res
      })
    },

    toWrongCollect() {
      uni.navigateTo({
        url: '/pages/WrongCollect/index?subjectCode=' + this.subjectCode + '&mode=' + this.mode
      })
    },

    toWrongQuestions(wrongQuestionCount) {
      if (!wrongQuestionCount) {
        return
      }
      if (this.mode === 'ZJLX') {
        uni.navigateTo({
          url: '/pages/AIPracticeWrong/index?subjectCode=' + this.subjectCode + '&sectionCode=' + this.sectionCode + '&batchNum=' + this.batchNum + '&mode=ZJLX' + '&chapterCode=' + this.chapterCode
        })
      } else if (this.mode === 'EXAM') {
        uni.navigateTo({
          url: '/pages/AIPracticeWrong/index?subjectCode=' + this.subjectCode + '&sectionCode=' + this.sectionCode + '&batchNum=' + this.batchNum + '&mode=EXAM' + '&chapterCode=' + this.chapterCode
        })
      } else if (this.aiExerciseRecordId) {
        uni.navigateTo({
          url: '/pages/AIPracticeWrong/index?subjectCode=' + this.subjectCode + '&aiExerciseRecordId=' + this.aiExerciseRecordId + '&batchNum=' + this.batchNum
        })
      } else if (this.questionSetId) {
        uni.navigateTo({
          url: '/pages/PracticeMode/index?subjectCode=' + this.subjectCode + '&questionSetId=' + this.questionSetId + '&batchNum=' + this.batchNum + '&wrongFlag=true'
        })
      } else if (this.sectionCode || this.chapterCode) {
        uni.navigateTo({
          url: '/pages/AIPracticeWrong/index?subjectCode=' + this.subjectCode + '&sectionCode=' + this.sectionCode + '&batchNum=' + this.batchNum + '&chapterCode=' + this.chapterCode
        })
      }
    },

    testPracticeExamPracticeInfo(questionSetId, batchNum) {
      homeApi.testPracticeExamPracticeInfo(questionSetId, batchNum).then(res => {
        this.result = res
      })
    },

    examPracticeInfo(sectionCode, chapterCode, batchNum) {
      homeApi.examPracticeInfo(sectionCode, chapterCode, batchNum).then(res => {
        this.result = res
      })
    }
  },
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>