<template>
  <view class="page">
    <image
      src="https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/login/logo.png"
    ></image>
    <button
      plain
      class="login-btn"
      :open-type="XCXXY && YSXY ? 'getPhoneNumber' : ''"
      @getphonenumber="getPhoneNumber"
    >
      登录授权
    </button>
    <view class="protocol" @click="XYChange">
      <view class="radio">
        <view v-if="XCXXY && YSXY"></view>
      </view>
      我已阅读并同意
      <text @click.stop="yhProtocols">《用户协议》</text>
      和
      <text @click.stop="homeProtocols">《隐私协议》</text>
    </view>
  </view>
</template>

<script>
import mainApi from "@/api/main";
import { mapState, mapMutations } from "vuex";

export default {
  data() {
    return {};
  },
  computed: {
    ...mapState("main", ["YSXY", "XCXXY", "token", "openId"]),
  },
  onShow() {
    uni.setStorageSync("toLogin", true);
  },
  methods: {
    ...mapMutations("main", [
      "setYSXY",
      "setXCXXY",
      "setToken",
      "setUserInfo",
      "setOpenId",
    ]),

    XYChange() {
      if (this.YSXY && this.XCXXY) {
        this.setYSXY(false);
        this.setXCXXY(false);
      } else {
        this.setYSXY(true);
        this.setXCXXY(true);
      }
    },

    wxLogin(flag, phoneCode) {
      try {
        wx.login({
          success: (data) => {
            console.log("code", data);
            mainApi.accountLogin(data.code).then((res) => {
              this.setToken(res.token);
              this.setOpenId(res.openId);
              uni.setStorageSync("token", res.token);
              uni.setStorageSync("openId", res.openId);
              const { nickname, gender, avatarUrl, id, phone } = res;
              this.setUserInfo({ nickname, gender, avatarUrl, id, phone });
              if (flag) {
                const { openId } = res;
                mainApi.accountRegister({ openId, code: phoneCode }).then((phoneRes) => {
                  this.setUserInfo({ phone: phoneRes.phone });
                  uni.setStorageSync("phone", phoneRes.phone);
                  uni.switchTab({
                    url: "/pages/index/index",
                  });
                });
                return;
              }
              uni.switchTab({
                url: "/pages/index/index",
              });
            });
          },
          fail: () => {},
        });
      } catch (err) {
        uni.showToast({
          title: err,
          icon: "none",
        });
      }
    },

    yhProtocols() {
      uni.navigateTo({
        url: "/pages/Protocol/yh",
      });
    },

    homeProtocols() {
      uni.navigateTo({
        url: "/pages/Protocol/index",
      });
    },

    getPhoneNumber(data) {
      try {
        this.wxLogin(data.detail.errMsg === "getPhoneNumber:ok", data.detail.code);
      } catch (err) {
        console.log("失败", err);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "./index.less";
</style>
