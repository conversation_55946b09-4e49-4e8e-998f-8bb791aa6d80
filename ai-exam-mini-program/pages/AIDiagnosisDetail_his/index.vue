<template>
  <view class="page">
    <u-navbar :autoBack="true" bgColor="transparent" placeholder>
      <view slot="center" class="nav-title">AI个性化学情诊断</view>
    </u-navbar>
    <view class="content">
      <view class="number">{{ data.diagnosisAccuracy }}</view>
      <view class="memo">本次诊断正确率</view>
      <view class="detail">
        <view>
          <view class="value">{{ data.diagnosisTime }}</view>
          <view class="key">诊断时间</view>
        </view>
        <view>
          <view class="value">{{ data.wrongCount }}道</view>
          <view class="key">我的错题</view>
        </view>
        <view>
          <view class="value">{{ data.totalCount }}道</view>
          <view class="key">本次答题数</view>
        </view>
      </view>
    </view>
    <view class="box">
      <view class="content-analyze">
        <view class="title">备考建议</view>
        <view class="memo">
          <text decode="true">{{ data.conclusion }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      data: {

      }
    }
  },
  onLoad(option) {
    this.data = JSON.parse(option.data)
  }
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>