<template>
    <view class="detail-container">
        <u-navbar placeholder :autoBack="true">
            <view slot="center" class="nav-title">纠错详情</view>
        </u-navbar>

        <!-- 题目内容 -->
        <view v-if="!detail.status || detail.status === 0" class="card question-section">
            <view class="type-tag">{{
                detail.beforeQuestionInfo.type === 'CHOICE'
                    ? '单选题'
                    : detail.beforeQuestionInfo.type === 'CHOICES'
                        ? '多选题'
                        : detail.beforeQuestionInfo.type === 'TRUE_FALSE'
                            ? '判断题'
                            : detail.beforeQuestionInfo.type === 'INDEFINITE_CHOICE'
                                ? '不定项选择题'
                                : '简答题'
            }}</view>
            <mpHtml class="topic" :content="detail.beforeQuestionInfo.content"></mpHtml>
            <view>
                <TopicOptions :options="detail.beforeQuestionInfo.options"
                    :questionType="detail.beforeQuestionInfo.type" :disabled="true"></TopicOptions>
            </view>
			<view class="analysis">
			    <text>【正确答案】</text>
                <mpHtml :content="detail.beforeQuestionInfo.answer" v-if="detail.beforeQuestionInfo.type === 'OPEN'"></mpHtml>
                <text v-else>{{ detail.beforeQuestionInfo.answer }}</text>
			</view>
            <view class="analysis">
                <text>【解析】</text>
                <mpHtml :content="detail.beforeQuestionInfo.explanation"></mpHtml>
            </view>
        </view>
        <view v-else class="card question-section">
            <view class="type-tag">{{
                detail.afterQuestionInfo.type === 'CHOICE'
                    ? '单选题'
                    : detail.afterQuestionInfo.type === 'CHOICES'
                        ? '多选题'
                        : detail.afterQuestionInfo.type === 'TRUE_FALSE'
                            ? '判断题'
                            : detail.afterQuestionInfo.type === 'INDEFINITE_CHOICE'
                                ? '不定项选择题'
                                : '简答题'
            }}</view>
            <mpHtml class="topic" :content="detail.afterQuestionInfo.content"></mpHtml>
            <view>
                <TopicOptions :options="detail.afterQuestionInfo.options" :questionType="detail.afterQuestionInfo.type"
                    :disabled="true"></TopicOptions>

            </view>
			<view class="analysis">
			    <text>【正确答案】</text>
                <mpHtml :content="detail.afterQuestionInfo.answer" v-if="detail.afterQuestionInfo.type === 'OPEN'"></mpHtml>
                <text v-else>{{ detail.afterQuestionInfo.answer }}</text>
			</view>
            <view class="analysis">
                <text>【解析】</text>
                <mpHtml :content="detail.afterQuestionInfo.explanation"></mpHtml>
            </view>
        </view>

        <!-- 纠错信息 -->
        <view class="card feedback-section">
            <view class="feedback-item">
                <text class="label">纠错类型：</text>
                <text>{{ detail.feedbackTypeDesc }}</text>
            </view>
            <view class="feedback-item">
                <text class="label">纠错内容：</text>
                <text>{{ detail.content }}</text>
            </view>
            <view class="feedback-item">
                <text class="label">提交时间：</text>
                <text>{{ detail.createTime }}</text>
            </view>
            <view class="feedback-item">
                <text class="label">处理状态：</text>
                <text :class="detail.status === 1 ? 'processed' : ''">
                    {{ detail.status === 1 ? '已回复' : '未回复' }}
                </text>
            </view>
            <view v-if="detail.status === 1" class="feedback-item">
                <text class="label">回复时间：</text>
                <text>{{ detail.updateTime }}</text>
            </view>
            <view v-if="detail.status === 1" class="feedback-item">
                <text class="label">回复内容：</text>
                <text>{{ detail.feedbackResultContent || detail.feedbackResultTypeDesc }}</text>
            </view>
        </view>
    </view>
</template>
<script>
import TopicOptions from '@/components/topicOptions/index'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

export default {
    components: {
        TopicOptions,
        mpHtml
    },
    data() {
        return {
            detail: {}
        }
    },
    onLoad(options) {
        if (options.detail) {
            this.detail = JSON.parse(decodeURIComponent(options.detail))
        }
    }
}
</script>
<style lang="less">
@import './index.less';
</style>
