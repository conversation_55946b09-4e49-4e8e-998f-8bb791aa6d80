<template>
	<view>
		<u-navbar :autoBack="true" bgColor="transparent" placeholder />
		<view class="notes" v-if="notesList.length">
			<view class="title">我的笔记</view>
			<view class="content">
				<view class="item" v-for="(item, index) in notesList" :key="index" @click="viewNoteDetails(item.id)">
					<view :class="{'note-title': true}">{{ (index + 1) + '、' + item.summary }}</view>
					<view class="right">
						<text>{{ item.updateTime }}</text>
						<u-icon name="arrow-right" size="44rpx"></u-icon>
					</view>
				</view>
			</view>
		</view>
		<view class="image-none" v-else>
			<!-- <image src="https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-mini-program/static/note-empty.png"></image> -->
			<view>暂无笔记</view>
		</view>
	</view>
</template>

<script>
	import homeApi from "@/api/home";
	import studyTimer from '@/mixins/study-timer'
	export default {
		mixins: [studyTimer],
		data() {
			return {
				showFlag: [],
				notesList: [],
			}
		},
		onShow() {
			this.loadNoteList()
		},
		methods: {
			loadNoteList() {
				homeApi.getNoteList('').then((res) => {
					this.notesList = res;
				});
			},
			viewNoteDetails(noteId) {
				uni.navigateTo({
					url: '/pages/note/detail/index?noteId=' + noteId
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	@import './index.less';
</style>