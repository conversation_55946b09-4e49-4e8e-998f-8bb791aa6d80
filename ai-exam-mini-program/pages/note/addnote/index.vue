<template>
    <view class="page">
        <u-navbar placeholder :autoBack="true">
            <view slot="center" class="nav-title">添加笔记</view>
        </u-navbar>
        <view class="note-container">
            <!-- 笔记输入区域 -->
            <textarea class="note-input" v-model="noteContent" placeholder="请输入笔记内容" maxlength="-1" />

            <!-- 保存按钮 -->
            <button class="save-btn" @tap="saveNote">保存</button>
        </view>
    </view>
</template>

<script>
import homeApi from "@/api/home";

export default {
    data() {
        return {
            noteContent: '', // 笔记内容
            subjectCode: '',
            questionId: '',
            questionSetType: '',
        }
    },
    onLoad(option) {
        this.questionId = option.questionId
        this.questionSetType = option.questionSetType
    },
    onShow() {
        this.subjectCode = uni.getStorageSync('typeSubjectCode').code
    },
    methods: {
        // 保存笔记方法
        saveNote() {
            if (!this.noteContent.trim()) {
                uni.showToast({
                    title: '请输入笔记内容',
                    icon: 'none'
                })
                return
            }
            const noteVO = {
                questionId: this.questionId,
                note: this.noteContent.trim(),
                questionSetType: this.questionSetType,
                subjectCode: this.subjectCode,
                courseId: -1,
                noteSource: 'question',
                productId: -1,
            };
            homeApi.takeNotes(noteVO);
            uni.showToast({
                title: '保存成功',
                icon: 'success'
            })
            uni.navigateBack()
        }
    }
}
</script>

<style scoped>
@import './index.less';
</style>