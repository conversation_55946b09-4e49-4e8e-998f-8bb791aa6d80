<template>
  <view class="page">
    <mpHtml :content="protocols"></mpHtml>
  </view>
</template>

<script>
import mainApi from '@/api/main'
import { mapMutations } from 'vuex'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

export default {
  components: {
    mpHtml
  },
  data() {
    return {
      protocols: null,
      protocolTypes: null
    }
  },
  onLoad(option) {
    this.protocolTypes = option.protocolTypes
    this.homeProtocols()
  },
  onUnload() {
    this.setYSXY(true)
  },
  methods: {
    ...mapMutations('main', ['setYSXY']),

    homeProtocols() {
      mainApi.homeProtocols(['XCXYSXY']).then(res => {
        this.protocols = res['XCXYSXY'].content.replace('<body>', '').replace('</body>', '')
      })
    }
  },
}
</script>

<style lang="less" scoped>
@import './index.less';
</style>