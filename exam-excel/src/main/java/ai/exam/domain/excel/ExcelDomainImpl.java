package ai.exam.domain.excel;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Component
public class ExcelDomainImpl implements ExcelDomain {

    @Override
    public <T> void exportToXlsx(File file, String sheetName, int beginRowIndex, List<T> rows) {
        ExcelWriter writer = ExcelUtil.getWriter(file, sheetName);

        if (rows != null && !rows.isEmpty()) {
            T firstRow = rows.get(0);
            List<Field> fields = getOrderedFields(firstRow.getClass());

            for (int i = 0; i < beginRowIndex; i++) {
                writer.passCurrentRow();
            }
            writeDataRows(writer, rows, fields);
        }

        writer.flush();
        writer.close();
    }

    private <T> void writeDataRows(ExcelWriter writer, List<T> rows, List<Field> fields) {
        for (T row : rows) {
            List<Object> dataRow = new ArrayList<>();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    dataRow.add(field.get(row));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Failed to access field value", e);
                }
            }
            writer.writeRow(dataRow);
        }
    }

    private <T> List<Field> getOrderedFields(Class<T> clazz) {
        List<Field> fields = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(ExcelField.class)) {
                fields.add(field);
            }
        }
        fields.sort(Comparator.comparingInt(f -> f.getAnnotation(ExcelField.class).order()));
        return fields;
    }
}
