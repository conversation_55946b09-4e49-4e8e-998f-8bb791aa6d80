import React from 'react';
import { productWebController } from "@/lib/controller";
import QuestionPurchase from './QuestionPurchase';

async function getData(productIds: string) {
  try {
    const questionBankDetails = await productWebController.batchQuestionDetails({ productIds: productIds?.split(',').map(item => Number(item)) });
    return { questionBankDetails };
  } catch (error) {
    console.error("获取题库详情失败:", error);
    throw error;
  }
}

export default async function QuestionPurchasePage({ searchParams }: { searchParams: { productIds: string } }) {
  const data = await getData(searchParams.productIds);

  if (!data) {
    return <div>加载失败，请稍后重试</div>;
  }

  const { questionBankDetails } = data;

  return <QuestionPurchase questionBankDetails={questionBankDetails} />;
}
