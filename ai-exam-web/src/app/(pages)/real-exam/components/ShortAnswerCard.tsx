import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Flag } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import CountdownTimer from './CountdownTimer';
import ExamSubmitConfirmation from './ExamSubmitConfirmation';

interface ShortAnswerCardProps {
  questionId: string;  // 新增
  questionNumber: number;
  totalQuestions: number;
  question: string;
  onNext: () => void;
  onPrevious: () => void;
  hasNext: boolean;
  hasPrevious: boolean;
  isMarked: boolean;
  onToggleMark: () => void;
  remainingTime: number;
  answeredQuestions: number;
  unansweredQuestions: number;
  onSubmitExam: () => void;
  onAnswerSubmit: (questionId: string) => void;  // 修改参数类型
  examDuration: number;
  onTimeUp: () => void;
}

const ShortAnswerCard: React.FC<ShortAnswerCardProps> = ({
  questionId,
  questionNumber,
  totalQuestions,
  question,
  onNext,
  onPrevious,
  hasNext,
  hasPrevious,
  isMarked,
  onToggleMark,
  remainingTime,
  answeredQuestions,
  unansweredQuestions,
  onSubmitExam,
  onAnswerSubmit,
  examDuration,
  onTimeUp
}) => {
  const [userAnswer, setUserAnswer] = useState('');
  const [isSubmitConfirmationOpen, setIsSubmitConfirmationOpen] = useState(false);
  const { toast } = useToast();

  const handleAnswerChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setUserAnswer(e.target.value);
    if (e.target.value.trim().length > 0) {
      onAnswerSubmit(questionId);
    }
  };

  const handleSubmit = () => {
    if (hasNext) {
      onNext();
    }
  };

  const handleMark = () => {
    onToggleMark();
    toast({
      title: isMarked ? "标记已取消" : "题目已标记",
      duration: 2000,
    });
  };

  const isLastQuestion = questionNumber === totalQuestions;

  const handleSubmitClick = () => {
    if (isLastQuestion) {
      setIsSubmitConfirmationOpen(true);
    } else {
      onNext();
    }
  };

  const handleConfirmSubmit = () => {
    setIsSubmitConfirmationOpen(false);
    onSubmitExam();
  };

  // 添加一个用于安全渲染HTML的函数
  const createMarkup = (htmlContent: string) => {
    return { __html: htmlContent };
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md relative">
      <div className="absolute top-4 right-4">
      <CountdownTimer
          initialMinutes={examDuration}
          onTimeUp={onTimeUp}
        />
      </div>
      <div className="flex items-center mb-6">
        <span className="bg-green-100/30 text-[#8DC63F] font-semibold px-3 py-1 rounded-md mr-4">
          简答题
        </span>
        <span className="text-gray-500">{questionNumber}/{totalQuestions}</span>
      </div>
      <div className="mb-6 text-lg font-medium">
      <span dangerouslySetInnerHTML={createMarkup(question)} />
      </div>
      <Textarea
        value={userAnswer}
        onChange={handleAnswerChange}
        placeholder="请输入你的答案..."
        className="mb-4"
        rows={6}
      />
      <div className="flex justify-between items-center mt-6">
        <div className="space-x-4">
          <Button variant="outline" onClick={onPrevious} disabled={!hasPrevious}>上一题</Button>
          <Button 
            onClick={handleSubmitClick}
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-[#ffdf7f] to-[#edb91b] text-black font-normal hover:from-[#edb91b] hover:to-[#ffdf7f]"
          >
            {isLastQuestion ? "交卷" : "下一题"}
          </Button>
        </div>
        <Button
          variant="outline"
          onClick={handleMark}
          className={cn(
            "transition-colors",
            isMarked && "bg-red-100 text-red-600 hover:bg-red-200"
          )}
        >
          <Flag className="w-5 h-5 mr-1" />
          {isMarked ? "取消标记" : "标记"}
        </Button>
      </div>
      <ExamSubmitConfirmation
        isOpen={isSubmitConfirmationOpen}
        onClose={() => setIsSubmitConfirmationOpen(false)}
        onConfirm={handleConfirmSubmit}
        remainingTime={remainingTime}
        answeredQuestions={answeredQuestions}
        unansweredQuestions={unansweredQuestions}
      />
    </div>
  );
};

export default ShortAnswerCard;