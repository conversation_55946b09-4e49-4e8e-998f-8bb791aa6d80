// src/app/(pages)/real-exam/lib/transform.ts

// API返回的选项接口
interface ApiOption {
  [key: string]: string;
}

// API返回的问题接口
interface ApiQuestion {
  questionId: number;
  questionContent: string;
  questionType: string;
  options?: ApiOption;
}

// 前端使用的问题接口
export interface TransformedQuestion {
  questionId: string;
  type: string;
  number: number;
  total: number;
  text: string;
  options?: string[];
}

/**
 * 将API返回的数据转换为前端需要的格式
 * @param apiData API返回的原始数据
 * @returns 转换后的问题数组
 */
export function transformApiResponse(apiData: ApiQuestion[]): TransformedQuestion[] {
  if (!Array.isArray(apiData)) {
    console.error('Invalid API data format');
    return [];
  }

  const totalQuestions = apiData.length;

  return apiData.map((q, index) => {
    // 转换选项从对象格式到数组格式
    const optionsArray = q.options ?
      Object.entries(q.options)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([_, value]) => value) :
      undefined;

    // 转换题目类型
    let questionType: string;
    switch (q.questionType) {
      case 'CHOICE':
        questionType = 'single';
        break;
      case 'CHOICES':
        questionType = 'multiple';
        break;
      case 'OPEN':
        questionType = 'short';
        break;
      case 'INDEFINITE_CHOICE':
        questionType = 'indefinite_choice';
        break;
      case 'TRUE_FALSE':
        questionType = 'true_false';
        break;
      default:
        questionType = 'single';
    }

    return {
      questionId: q.questionId.toString(),
      type: questionType,
      number: index + 1,
      total: totalQuestions,
      text: q.questionContent,
      options: optionsArray
    };
  });
}