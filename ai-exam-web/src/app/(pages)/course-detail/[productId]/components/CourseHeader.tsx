'use client';

import React, { useState } from 'react';
import { Eye, Users, Clock, Calendar, Share2, Video, BarChart2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import ShareDialog from './ShareDialog';
import { useRouter } from 'next/navigation';
import { ProductCourseVO } from '@/apis/product-vo';
import { CourseWebController } from '@/apis/web-course';
import { requireAuth } from '@/lib/auth';
import { toast } from 'sonner';

interface CourseHeaderProps {
  course: ProductCourseVO | null;
  freeTrial: boolean;
}

interface StatItemProps {
  icon: React.ReactNode;
  text: string;
}

const gradientButtonStyle = {
  background: 'linear-gradient(90deg, #FFDF7F 0%, #EDB91B 100%)',
  color: 'black',
  border: 'none',
};

const CourseHeader: React.FC<CourseHeaderProps> = ({ course, freeTrial }) => {
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);

  const renderCourseImage = () => {
    if (course?.coverImage) {
      return <img src={course.coverImage} alt={course.name} className="w-full h-full object-cover rounded-lg" />;
    }
    return <span className="text-blue-800 text-lg">Course Image</span>;
  };

  const renderStatItem = ({ icon, text }: StatItemProps) => (
    <div className="flex items-center">
      {icon}
      <span>{text}</span>
    </div>
  );

  const renderCourseStats = () => {
    return (
      <div className="flex flex-col space-y-6">
        <div className="flex items-center space-x-8">
          {renderStatItem({ icon: <Eye className="w-4 h-4 mr-2" />, text: `${course?.viewCount} 浏览` })}
          {renderStatItem({ icon: <Users className="w-4 h-4 mr-2" />, text: `${course?.learnCount} 学习` })}
        </div>
        <div className="flex items-center space-x-8">
          {renderStatItem({ icon: <BarChart2 className="w-4 h-4 mr-2" />, text: `难度：${course?.difficulty}` })}
          {renderStatItem({ icon: <Video className="w-4 h-4 mr-2" />, text: `形式：${course?.learningFormat}` })}
        </div>
        <div className="flex items-center space-x-8">
          {renderStatItem({ icon: <Clock className="w-4 h-4 mr-2" />, text: `时长：${course?.videoDuration}` })}
          {renderStatItem({ icon: <Calendar className="w-4 h-4 mr-2" />, text: `有效期：${course?.validityPeriod}天` })}
        </div>
      </div>
    );
  };

  return (
    <div className="mb-8 flex flex-col lg:flex-row">
      <div className="w-full lg:w-2/5 mb-4 lg:mb-0 lg:mr-6">
        <div className="relative" style={{ paddingBottom: 'calc(75% - 50px)' }}>
          <div className="absolute inset-0 bg-blue-200 rounded-lg flex items-center justify-center">
            {renderCourseImage()}
          </div>
        </div>
      </div>
      <div className="flex-grow flex flex-col justify-center items-start space-y-6">
        <h1 className="text-[20px] font-bold">{course?.name}</h1>
        {renderCourseStats()}
        <div className="flex items-center">
          <span className="text-2xl font-bold text-red-500 mr-2">
            {freeTrial ? '￥0' : course?.price}
          </span>
          <span className="text-gray-500 line-through">{course?.originalPrice}</span>
        </div>
        <CourseActions
          gradientButtonStyle={gradientButtonStyle}
          course={course || undefined}
          onShareClick={() => setIsShareDialogOpen(true)}
          freeTrial={freeTrial}
        />
      </div>
      <ShareDialog
        isOpen={isShareDialogOpen}
        onClose={() => setIsShareDialogOpen(false)}
        // courseId={course?.productId}
      />
    </div>
  );
};

interface CourseActionsProps {
  gradientButtonStyle: React.CSSProperties;
  course: ProductCourseVO | undefined;
  onShareClick: () => void;
  freeTrial: boolean;
}

const CourseActions: React.FC<CourseActionsProps> = ({ gradientButtonStyle, course, onShareClick, freeTrial }) => {
  const router = useRouter();
  const courseWebController = new CourseWebController();

  const handlePurchaseClick = () => {
    if (!course?.productId) return;

    if (freeTrial || course?.access) {
      handleStartLearning();
      return;
    }

    if (requireAuth(router)) {
      router.push(`/course-purchase/${course.productId}`);
    }
  };

  const handleStartLearning = async () => {
    if (!course?.productId) return;

    try {
      const menuList = await courseWebController.menuWithToken({ 
        productId: course.productId 
      });
      const firstAccessibleCourse = findFirstAccessibleItem(menuList);

      if (firstAccessibleCourse?.id) {
        window.open(`/course-learning/${course.productId}/${firstAccessibleCourse.id}`, '_blank');
      }
    } catch (error) {
      console.error('获取课程菜单失败:', error);
      toast.error('获取课程信息失败，请稍后重试');
    }
  };

  return (
    <div className="flex flex-wrap items-center">
      <Button
        className="mr-4 mb-2"
        style={gradientButtonStyle}
        onClick={handlePurchaseClick}
      >
        {freeTrial || course?.access ? '立即上课' : '立即报名'}
      </Button>
      <Button variant="ghost" className="mr-2 mb-2" onClick={onShareClick}>
        <Share2 className="w-4 h-4 mr-2" />
        分享
      </Button>
    </div>
  );
};

const findFirstAccessibleItem = (items: any[]): any => {
    for (const item of items) {
        if (item.access === true) {
            return item;
        }
        if (item.list && item.list.length > 0) {
            const found = findFirstAccessibleItem(item.list);
            if (found) {
                return found;
            }
        }
    }
    return null;
};

export default CourseHeader;
