'use client'

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { certificateWebController } from '@/lib/controller';

interface LearningAssessmentProps {
  onNext: () => void;
  selectSubjectCode: string;
}

interface Chapter {
  chapterCode: string;
  chapterName: string;
}

const LearningAssessment: React.FC<LearningAssessmentProps> = ({ onNext, selectSubjectCode }) => {
  const [selectedChapters, setSelectedChapters] = useState<string[]>([]);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChapters = async () => {
      if (!selectSubjectCode) return;
      
      try {
        setIsLoading(true);
        const response = await certificateWebController.getChapterBySubjectCode(selectSubjectCode);
        setChapters(response);
      } catch (err) {
        setError('获取章节列表失败');
        console.error('获取章节列表失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchChapters();
  }, [selectSubjectCode]);

  const handleChapterSelect = (chapter: string) => {
    setSelectedChapters(prev =>
      prev.includes(chapter)
        ? prev.filter(c => c !== chapter)
        : [...prev, chapter]
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 text-center py-4">
        {error}
      </div>
    );
  }

  return (
    <div className="max-w-[600px] mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6 text-center">AI个性化学情诊断</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">基础信息</h2>
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">学情自评</h3>
          <p className="text-gray-600 mb-4">请点亮您已复习到位的章节</p>
          <div className="space-y-2">
            {chapters.map((chapter) => (
              <Button
                key={chapter.chapterCode}
                onClick={() => handleChapterSelect(chapter.chapterCode)}
                className={`w-full text-left justify-start ${
                  selectedChapters.includes(chapter.chapterCode)
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700'
                }`}
              >
                {chapter.chapterName}
              </Button>
            ))}
          </div>
        </div>
        <div className="flex justify-center">
          <Button
            onClick={onNext}
            className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-2 rounded-full"
          >
            下一步
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LearningAssessment;