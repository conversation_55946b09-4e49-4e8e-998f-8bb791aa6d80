import React from "react";
import { RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

const QuestionOption = ({
  questionType,
  option,
  index,
  isSelected,
  isCorrect,
  showExplanation,
  onSelect,
  disabled,
}) => {
  // 修改处理选项的逻辑
  const text = typeof option === "object" ? option.text : option;
  const letter = String.fromCharCode(65 + index);
  const optionId = `option-${letter}`;
  const handleSelect = () => {
    if (!disabled) {
      onSelect(letter);
    }
  };
  return (
    <div className="flex items-center">
      {(questionType === "single" || questionType === "true_false") ? (
        <div className="flex items-start">
          <RadioGroupItem
            value={letter}
            id={optionId}
            disabled={disabled}
            className="peer sr-only"
          />
          <Label
            htmlFor={optionId}
            className={cn(
              "flex flex-1 items-center rounded-lg border border-gray-200 p-4 cursor-pointer",
              "hover:bg-gray-50",
              showExplanation &&
              isSelected &&
              isCorrect &&
              "bg-green-50 border-green-500 text-green-600",
              showExplanation &&
              isSelected &&
              !isCorrect &&
              "bg-red-50 border-red-500 text-red-600",
              !showExplanation &&
              isSelected &&
              "bg-blue-50 border-blue-500 text-blue-600"
            )}
          >
            <span
              className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 text-base font-semibold mr-4",
                showExplanation &&
                isSelected &&
                isCorrect &&
                "bg-green-500 border-green-500 text-white",
                showExplanation &&
                isSelected &&
                !isCorrect &&
                "bg-red-500 border-red-500 text-white",
                !showExplanation &&
                isSelected &&
                "bg-blue-500 border-blue-500 text-white"
              )}
            >
              {letter}
            </span>
            {text}
          </Label>
        </div>
      ) : (
        <div className="flex items-start">
          <Checkbox
            id={optionId}
            className="sr-only"
            checked={isSelected}
            onCheckedChange={onSelect}
            disabled={disabled}
          />
          <Label
            htmlFor={optionId}
            className={cn(
              "flex flex-1 items-center rounded-lg border border-gray-200 p-4 cursor-pointer",
              "hover:bg-gray-50",
              showExplanation &&
              isSelected &&
              isCorrect &&
              "bg-green-50 border-green-500 text-green-600",
              showExplanation &&
              isSelected &&
              !isCorrect &&
              "bg-red-50 border-red-500 text-red-600",
              !showExplanation &&
              isSelected &&
              "bg-blue-50 border-blue-500 text-blue-600"
            )}
          >
            <span
              className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full border border-gray-300 text-base font-semibold mr-4",
                showExplanation &&
                isSelected &&
                isCorrect &&
                "bg-green-500 border-green-500 text-white",
                showExplanation &&
                isSelected &&
                !isCorrect &&
                "bg-red-500 border-red-500 text-white",
                !showExplanation &&
                isSelected &&
                "bg-blue-500 border-blue-500 text-white"
              )}
            >
              {letter}
            </span>
            {text}
          </Label>
        </div>
      )}
    </div>
  );
};

export default QuestionOption;
