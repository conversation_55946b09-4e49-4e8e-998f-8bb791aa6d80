import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { errorBookAndFavoritesWebController } from "@/lib/controller";
import { ChevronLeft } from 'lucide-react';

interface QuestionVO {
  questionId: number;
  questionContent: string;
  questionType: string;
  options: Record<string, string>;
  userAnswer: string;
  rightAnswer: string;
  isRight: boolean;
  analysis: string;
}

interface QuestionViewPageProps {
  type: "wrong" | "favorite";
  questionSetId: number;
  exerciseMode: string;
  subjectCode: string;
  chapterCode: string;
  sectionCode: string;
}

const QuestionViewPage: React.FC<QuestionViewPageProps> = ({ type, questionSetId, exerciseMode, subjectCode, chapterCode, sectionCode }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [questionAnswers, setQuestionAnswers] = useState<QuestionVO[]>([]);

  useEffect(() => {
    if (type === 'wrong') {
      errorBookAndFavoritesWebController.getWrongQuestions(exerciseMode, subjectCode, sectionCode, chapterCode, questionSetId).then((res) => {
        setQuestionAnswers(res);
        setLoading(false);
      }).catch((err) => {
        setError(err.message);
        setLoading(false);
      });
    } else {
      errorBookAndFavoritesWebController.getFavoriteQuestions(exerciseMode, subjectCode, sectionCode, chapterCode, questionSetId).then((res) => {
        setQuestionAnswers(res);
        setLoading(false);
      }).catch((err) => {
        setError(err.message);
        setLoading(false);
      });
    }
  }, [type, exerciseMode, subjectCode, sectionCode, chapterCode, questionSetId]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">试题详情</h1>
        <p>加载中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">试题详情</h1>
        <p>{error || '加载失败'}</p>
      </div>
    );
  }

  // 将选项对象转换为数组
  const getOptionsArray = (options: Record<string, string>): string[] => {
    return Object.entries(options)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([_, value]) => value);
  };

  // 添加一个用于安全渲染HTML的函数
  const createMarkup = (htmlContent: string) => {
    return { __html: htmlContent };
  };

  return (
    <div className="w-[1200px] mb-4 p-6 container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center">
        <a
          href={`/wrong-favorite/${type}?subjectCode=${subjectCode}`}
          className="flex items-center hover:text-green-500"
        >
          <ChevronLeft className="mr-2 h-6 w-6" />
          <h2 className="text-xl font-semibold">试题详情</h2>
        </a>
      </div>
      {questionAnswers.map((question, index) => (
        <Card key={question.questionId} className="mb-6">
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold mb-4">
              {index + 1}. 
              <span dangerouslySetInnerHTML={createMarkup(question.questionContent)} />
            </h2>
            {question.questionType !== 'OPEN' && (
              <div className="mb-4">
                <h3 className="font-medium mb-2">选项：</h3>
                <ul className="list-disc pl-5">
                  {getOptionsArray(question.options).map((option, optionIndex) => (
                    <li key={optionIndex}>
                      {String.fromCharCode(65 + optionIndex)}. {option}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <div className={`mb-2 ${(question.isRight || question.rightAnswer === question.userAnswer) ? 'text-green-600' : 'text-red-600'}`}>
              <p className="mb-2">
                <strong>正确答案：</strong> 
                <span dangerouslySetInnerHTML={createMarkup(question.rightAnswer)} />
              </p>
              {type === 'wrong' &&
                <p className="mb-2">
                  <strong>你的答案：</strong> 
                  <span dangerouslySetInnerHTML={createMarkup(question.userAnswer || '')} />
                </p>
              }
            </div>
            <p>
              <strong>答案解析：</strong>
              <span dangerouslySetInnerHTML={createMarkup(question.analysis || '暂无解析')} />
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default QuestionViewPage;