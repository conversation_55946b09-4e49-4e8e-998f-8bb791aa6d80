import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Star, StarOff, Pencil } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

const ShortAnswerCard = ({
  questionNumber,
  totalQuestions,
  question,
  answer,
  isFavorite,
  onToggleFavorite,
  onPrevious,
  onNext,
  hasPrevious,
  hasNext,
  isSubmitted,
  onAnswerSubmit,
  previousAnswer,
  isShowFavorite,
}) => {
  const [userAnswer, setUserAnswer] = useState("");
  const [showAnswer, setShowAnswer] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!userAnswer.trim() || isSubmitted) return;

    setShowAnswer(true);
    await onAnswerSubmit(true, userAnswer.trim());
  };

  useEffect(() => {
    if (isSubmitted && previousAnswer) {
      setUserAnswer(previousAnswer);
      setShowAnswer(true);
    }else{
      setUserAnswer("");
      setShowAnswer(false);
    }
  }, [isSubmitted, previousAnswer]);

  const handleFavorite = async () => {
    try {
      await onToggleFavorite();
      toast({
        title: isFavorite ? "已取消收藏" : "收藏成功",
        duration: 2000,
      });
    } catch (error) {
      toast({
        title: "收藏操作失败",
        description: "请稍后重试",
        variant: "destructive",
        duration: 3000,
      });
    }
  };
  // 添加一个用于安全渲染HTML的函数
  const createMarkup = (htmlContent) => {
    return { __html: htmlContent };
  };
    
  return (
    <div className="bg-white p-8 rounded-lg shadow-md">
      <div className="flex items-center mb-6">
        <span className="bg-green-100/30 text-[#8DC63F] font-semibold px-3 py-1 rounded-md mr-4">
          简答题
        </span>
        <span className="text-gray-500">
          {questionNumber}/{totalQuestions}
        </span>
      </div>
      <div className="mb-6 text-lg font-medium">
      <span dangerouslySetInnerHTML={createMarkup(question)} />
      </div>
      <Textarea
        value={userAnswer}
        onChange={(e) => setUserAnswer(e.target.value)}
        placeholder="请输入你的答案..."
        className="mb-4"
        rows={6}
        disabled={isSubmitted}
      />
      {!showAnswer && !isSubmitted && (
        <div className="flex justify-start mb-6">
          <Button
            onClick={handleSubmit}
            disabled={userAnswer.trim() === ""}
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-[#ffdf7f] to-[#edb91b] text-black font-normal hover:from-[#edb91b] hover:to-[#ffdf7f]"
          >
            提交答案
          </Button>
        </div>
      )}
      {showAnswer && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 className="font-semibold mb-2">参考答案</h4>
          <p className="whitespace-pre-line">
          <span dangerouslySetInnerHTML={createMarkup(answer)} />
          </p>
        </div>
      )}
      <div className="flex justify-between items-center mt-6">
        <div className="space-x-4">
          <Button
            variant="outline"
            onClick={onPrevious}
            disabled={!hasPrevious}
          >
            上一题
          </Button>
          <Button
            onClick={onNext}
            disabled={!hasNext}
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-[#ffdf7f] to-[#edb91b] text-black font-normal hover:from-[#edb91b] hover:to-[#ffdf7f]"
          >
            下一题
          </Button>
        </div>
        <div className="space-x-2">
          {isShowFavorite && (
            <Button
              variant="outline"
              onClick={handleFavorite}
              className={cn(
                "transition-colors",
                isFavorite &&
                "bg-gradient-to-r from-[#ffdf7f] to-[#edb91b] text-black hover:from-[#edb91b] hover:to-[#ffdf7f]"
              )}
            >
              {isFavorite ? (
                <Star className="w-5 h-5 mr-1 fill-current" />
              ) : (
                <StarOff className="w-5 h-5 mr-1" />
              )}
              {isFavorite ? "已收藏" : "收藏"}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShortAnswerCard;
