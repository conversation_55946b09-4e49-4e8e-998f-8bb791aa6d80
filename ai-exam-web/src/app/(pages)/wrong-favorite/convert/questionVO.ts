// 后端返回的数据结构
export interface BackendQuestion {
  questionId: number;
  questionContent: string;
  questionType: string;
  options: { [key: string]: string };
  rightAnswer: string;
  number: number;
  analysis: string;
}

// 前端使用的数据结构
export interface Option {
  id: string;
  text: string;
}

export interface Question {
  questionId: number; // 新增
  number: number;
  type: "single" | "multiple" | "short" | "indefinite_choice" | "true_false";
  text: string;
  options?: Option[];
  correctAnswer?: string | string[];
  answer?: string;
  explanation?: string;
  total?: number;
  isSubmitted?: boolean; // 新增，用于标记题目是否已提交
}
