import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

const QuizPage = () => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [autoNext, setAutoNext] = useState(false);

  const questions = [
    {
      id: 1,
      content: "1/1139、1、任何人发现有违反本规程的情况，应立即制止，经（）后方可恢复作业。",
      options: [
        { id: 'A', text: '上级领导同意' },
        { id: 'B', text: '处罚' },
        { id: 'C', text: '批评教育' },
        { id: 'D', text: '纠正' },
      ],
    },
    // ... 添加更多题目
  ];

  const handleAnswerSelect = (value) => {
    setSelectedAnswer(value);
    if (autoNext) {
      // 自动跳转到下一题的逻辑
      handleNextQuestion();
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
      setSelectedAnswer(null);
    }
  };

  return (
    <div className="flex h-screen">
      <div className="w-3/4 p-6 overflow-y-auto">
        <Card className="mb-6">
          <CardContent className="p-6">
            <h2 className="text-xl font-bold mb-4">
              {questions[currentQuestion].content}
            </h2>
            <RadioGroup onValueChange={handleAnswerSelect} value={selectedAnswer}>
              {questions[currentQuestion].options.map((option) => (
                <div key={option.id} className="flex items-center space-x-2 mb-2">
                  <RadioGroupItem value={option.id} id={option.id} />
                  <Label htmlFor={option.id}>{option.id}. {option.text}</Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>
        <div className="flex justify-between">
          <Button onClick={handlePreviousQuestion} disabled={currentQuestion === 0}>
            上一题
          </Button>
          <Button onClick={handleNextQuestion} disabled={currentQuestion === questions.length - 1}>
            下一题
          </Button>
        </div>
      </div>
      <div className="w-1/4 bg-gray-100 p-6 overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">答题卡</h3>
        <div className="grid grid-cols-5 gap-2 mb-6">
          {Array.from({ length: 35 }, (_, i) => (
            <Button
              key={i}
              variant={i === currentQuestion ? "secondary" : "outline"}
              className="w-10 h-10"
              onClick={() => setCurrentQuestion(i)}
            >
              {i + 1}
            </Button>
          ))}
        </div>
        <div className="flex items-center justify-between mb-4">
          <span>答对：710题</span>
          <span>答错：17题</span>
        </div>
        <div className="mb-4">
          <span>正确率：97.66%</span>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            id="auto-next"
            checked={autoNext}
            onCheckedChange={setAutoNext}
          />
          <Label htmlFor="auto-next">答对自动下一题</Label>
        </div>
      </div>
    </div>
  );
};

export default QuizPage;