import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { ExamCountdownController } from '@/apis/web-certificate';
import { useSearchParams, useRouter } from 'next/navigation';

const LearningTask = ({ activeTab }) => {
  const [showQRDialog, setShowQRDialog] = useState(false);
  const [dialogTitle, setDialogTitle] = useState('');
  const [countdownDays, setCountdownDays] = useState('');
  const currentTab = activeTab;
  const selectedCertificate = useSelector((state) => state.certificate.selectedCertificate[0]);
  const certificatesMap = useSelector((state) => state.certificate.certificatesMap);
  const router = useRouter();
  useEffect(() => {
    const fetchCountdown = async () => {
      try {
        const examCountdownController = new ExamCountdownController();
        const response = await examCountdownController.getCountdownByCertificateCode(selectedCertificate);
        setCountdownDays(response.countdownDaysStr);
      } catch (error) {
        console.error('获取倒计时失败:', error);
      }
    };

    if (selectedCertificate) {
      fetchCountdown();
    }
  }, [selectedCertificate]);

  // 模拟AI诊断结果和备考建议
  const aiDiagnosis = "基础知识掌握良好，需要加强实践题目的训练。";
  const aiSuggestion = "建议每天完成50道模拟题，并复习错题。着重关注施工管理和工程经济两个科目。";

  const handleAIDiagnosis = (type) => {
    console.log("AI诊断按钮被点击");
    // 这里可以添加导航到AI诊断页面的逻辑
    // router.push(`/ai-learning-diagnosis`)
    setDialogTitle(type === 'diagnosis' ? 'AI诊断' : 'AI备考建议');
    setShowQRDialog(true);
  };

  const renderContent = () => {
    if (currentTab === 'chapter') {
      return (
        <>
          <div>
            <h3 className="font-semibold">AI诊断结果</h3>
            <p>{aiDiagnosis}</p>
          </div>
          <div>
            <h3 className="font-semibold">AI备考建议</h3>
            <p>{aiSuggestion}</p>
          </div>
        </>
      );
    }

    return (
      <div>
        <p>
          梦想才刚刚启程，别急，稳扎稳打是成功的开始！每一天多学一点，每一步都算数。考试前每天坚持学习，掌握好基础知识，你的未来正在积累力量！
        </p>
      </div>
    );
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>学习任务</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold">
              当前考证任务：{certificatesMap?.[selectedCertificate]?.certificateName || '未选择'}
            </h3>
          </div>
          <div>
            <p className="text-red-500 font-bold">{countdownDays}</p>
          </div>
          {renderContent()}
          <Button
            onClick={() => handleAIDiagnosis('diagnosis')}
            className="w-full bg-black hover:bg-gray-800 text-white" // 修改为黑色背景
          >
            去做AI诊断
          </Button>
          <Button
            onClick={() => handleAIDiagnosis('suggestion')}
            className="w-full bg-white hover:bg-gray-100 text-black border border-gray-200" // 修改为白色背景，添加边框
          >
            查看AI备考建议
          </Button>
        </CardContent>
      </Card>
      <Dialog open={showQRDialog} onOpenChange={setShowQRDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <button
            onClick={() => setShowQRDialog(false)}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>
          <DialogHeader>
            <DialogTitle className="text-left text-2xl font-semibold">
              {dialogTitle}
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center space-y-4">
            <p className="text-center text-gray-600">
              推荐使用乐象AI小程序查看
            </p>
            <div className="w-[200px] h-[200px] bg-gray-100 flex items-center justify-center">
              <img
                src="https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/mini-program.png"
                alt="小程序二维码"
                className="w-full h-full object-contain"
              />
            </div>
            <p className="text-center text-gray-600">
              扫一扫，打开小程序，体验更多功能
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default LearningTask;