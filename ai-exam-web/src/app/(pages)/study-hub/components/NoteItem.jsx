import React from 'react';
import { Button } from "@/components/ui/button";

const NoteItem = ({ note, type }) => {
  if (type === "courseLearning") {
    return (
      <div className="grid grid-cols-5 gap-4 p-4 border-b items-center text-sm">
        <div>{note.content}</div>
        <div>{note.course}</div>
        <div>{note.chapter}</div>
        <div>{note.updatedAt}</div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">复习课程</Button>
          <Button variant="outline" size="sm">删除</Button>
        </div>
      </div>
    );
  } else if (type === "questionBank") {
    return (
      <div className="grid grid-cols-5 gap-4 p-4 border-b items-center text-sm">
        <div>{note.content}</div>
        <div>{note.subject}</div>
        <div>{note.questionType}</div>
        <div>{note.updatedAt}</div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">查看题目</Button>
          <Button variant="outline" size="sm">删除</Button>
        </div>
      </div>
    );
  }
};

export default NoteItem;