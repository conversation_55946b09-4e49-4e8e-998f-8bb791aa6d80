import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { Brain } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Card } from "@/components/ui/card"

const QuestionBankHeader = ({ currentSubject, setCurrentSubject, subjects, activeTab, chapterData, realExamData, onNavigate }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [tempSelectedSubject, setTempSelectedSubject] = useState(null);
  const [stats, setStats] = useState([
    { label: "正确率", value: "0%" },
    { label: "已做题数", value: 0 },
    { label: "正确题数", value: 0 },
    { label: "错误题数", value: 0 },
    { label: "总题数", value: 0 },
  ]);

  useEffect(() => {
    console.log("activeTab:", activeTab);
    console.log("chapterData:", chapterData);
    console.log("realExamData:", realExamData);

    let totalCount = 0;
    let completedCount = 0;
    let correctCount = 0;

    if (activeTab === "chapter" && chapterData.length > 0) {
      chapterData.forEach(chapter => {
        totalCount += chapter.totalCount || 0;
        completedCount += chapter.completedCount || 0;
        correctCount += chapter.correctCount || 0;
      });
    } else if (activeTab === "realExam" && realExamData.length > 0) {
      realExamData.forEach(exam => {
        totalCount += exam.totalCount || 0;
        completedCount += exam.completedCount || 0;
        correctCount += exam.correctCount || 0;
      });
    }

    const incorrectCount = completedCount - correctCount;
    const accuracy = completedCount > 0 ? Math.round((correctCount / completedCount) * 100) : 0;

    console.log("Calculated stats:", {
      totalCount,
      completedCount,
      correctCount,
      incorrectCount,
      accuracy
    });

    setStats([
      { label: "正确率", value: `${accuracy}%` },
      { label: "已做题数", value: completedCount },
      { label: "正确题数", value: correctCount },
      { label: "错误题数", value: incorrectCount },
      { label: "总题数", value: totalCount },
    ]);
  }, [activeTab, chapterData, realExamData]);

  const accuracy = parseInt(stats[0].value);
  const data = [
    { name: 'Correct', value: accuracy },
    { name: 'Incorrect', value: 100 - accuracy },
  ];
  const COLORS = ['#4CAF50', '#F3F4F6'];

  const handleSubjectSelect = (subject) => {
    setTempSelectedSubject(subject);
  };


  const handleConfirm = () => {
    if (tempSelectedSubject) {
      setCurrentSubject(tempSelectedSubject);
      setIsDialogOpen(false);
    }
  };

  const CARD_HEIGHT = "96px"; // 24 * 4 = 96px
  const CARD_WIDTH = "96px";  // 保持正方形
  const CARD_PADDING = "5px";
  const CHART_SIZE = 86; // 96 - (5 * 2)

  return (
    <div className="bg-white rounded-lg shadow mb-4">
      <Card className="w-full mb-8 mt-4">
        {/* 增加了border-t border-b 添加上下边框，py-8 增加上下内边距20px(32px) */}
        <div className="flex items-center justify-between mx-4 py-6">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold mr-2">{currentSubject?.subjectName || '加载中...'}</h1>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsDialogOpen(true)}
              className="text-lg"
            >
              切换科目
            </Button>
          </div>
        </div>
     
        <div className="grid grid-cols-5 w-full gap-4  items-center mb-8">  {/* 改为5列grid布局 */}
        {/* 第一个卡片 - 饼图 */}
        {/* <Card
          className="shrink-0 flex items-center justify-center"
          style={{
            width: CARD_WIDTH,
            height: CARD_HEIGHT,
            padding: CARD_PADDING
          }}
        > */}
           <div className="relative flex items-center justify-center">
            <PieChart width={CHART_SIZE} height={CHART_SIZE}>
              <Pie
                data={[
                  { name: 'Correct', value: parseInt(stats[0].value) || 0 },
                  { name: 'Incorrect', value: 100 - (parseInt(stats[0].value) || 0) },
                ]}
                cx={38}
                cy={38}
                innerRadius={CHART_SIZE / 3}
                outerRadius={CHART_SIZE / 2}
                paddingAngle={0}
                dataKey="value"
                startAngle={90}
                endAngle={-270}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
            </PieChart>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
              <div className="text-lg font-bold">{stats[0].value}</div>
              <div className="text-lg">正确率</div>
            </div>
          </div>

        {/* 第二个卡片 - 统计数据 */}
      
            {stats.slice(1).map((stat, index) => (
              <div key={index} className="text-center flex flex-col justify-center">
                {/* 修改字体大小为30px */}
                <div className="text-4xl  font-bold">{stat.value}</div>
                {/* 修改标签文字大小,增加间距 */}
                <div className="text-lg text-gray-600 mt-1">{stat.label}</div>
              </div>
            ))}

      </div>  </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>选择科目</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {subjects.map((subject) => (
              <Button
                key={subject.subjectCode}
                variant="outline"
                className={`
                  ${tempSelectedSubject?.subjectCode === subject.subjectCode
                    ? ""
                    : "bg-[#f6f6f6] border-[#c9cdcf]"}
                  text-[#000] focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none
                `}
                style={tempSelectedSubject?.subjectCode === subject.subjectCode ? {
                  background: 'linear-gradient(90deg, #FFDF7F 0%, #EDB91B 100%)',
                } : {}}
                onClick={() => handleSubjectSelect(subject)}
              >
                {subject.subjectName}
              </Button>
            ))}
          </div>
          <DialogFooter>
            <Button
              onClick={handleConfirm}
              disabled={!tempSelectedSubject}
              style={{
                background: !tempSelectedSubject ? '#f6f6f6' : 'linear-gradient(90deg, #FFDF7F 0%, #EDB91B 100%)',
              }}
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default QuestionBankHeader;
