import React, { useState } from 'react';
import { Bookmark } from 'lucide-react';
import ExamHistoryModal from './ExamHistoryModal';
import { TableCell, TableRow } from "@/components/ui/table";

const RealExamItem = ({ exam, onPracticeModeClick, onExamModeClick }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <TableRow className="hover:bg-slate-100 text-lg">
      <TableCell className="w-[60%]">
        <div className="flex items-center">
          <Bookmark className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
          <span className="truncate">{exam.title}</span>
        </div>
      </TableCell>
      <TableCell className="w-[20%] text-center">
        {exam.progress.completed} / {exam.progress.total}
      </TableCell>
      <TableCell className="w-[20%]">
        <div className="flex flex-col items-center justify-center">
          {exam.purchased ? (
            <>
              <div className="flex items-center mb-1">
                <a
                  href="#"
                  className="text-blue-500 hover:underline"
                  onClick={(e) => {
                    e.preventDefault();
                    onPracticeModeClick(exam.id);
                  }}
                >
                  练习模式
                </a>
                {exam.isExam && <span className="mx-1 text-gray-300">|</span>}
                {exam.isExam && <a
                  href="#"
                  className="text-blue-500 hover:underline"
                  onClick={(e) => {
                    e.preventDefault();
                    onExamModeClick(exam.id);
                  }}
                >
                  考试模式
                </a>
                }
              </div>
              <button
                onClick={() => setIsModalOpen(true)}
                className="text-blue-500 hover:text-blue-600 transition-colors"
              >
                查看历史成绩
              </button>
            </>
          ) : (
            <a href="#" className="text-blue-500 hover:underline">
              购买
              <span className="ml-1 text-xs bg-gray-200 text-gray-700 px-1 rounded">🔒</span>
            </a>
          )}
        </div>
      </TableCell>
      {isModalOpen && (
        <ExamHistoryModal
          onClose={() => setIsModalOpen(false)}
          examId={exam.id}
          examName={exam.title}
        />
      )}
    </TableRow>
  );
};

export default RealExamItem;