"use client"
import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import PaymentButton from '@/components/PaymentButton';
import { ProductCourseVO } from '@/apis/product-vo';
import { TeacherVO } from '@/apis/course-vo';
import { OrderWebController } from '@/apis/web-transaction';
import { CreateOrderRequest } from '@/apis/transaction-vo';
import WechatPayModal from '@/components/WechatPayModal';

interface CoursePurchaseProps {
  courseDetail: ProductCourseVO | null;
  teachers: TeacherVO[];
}

const CoursePurchase = ({ courseDetail, teachers }: CoursePurchaseProps) => {
  const router = useRouter();
  const [timeLeft, setTimeLeft] = useState(10 * 60);
  const [selectedPayment, setSelectedPayment] = useState('');
  const [isTimeoutDialogOpen, setIsTimeoutDialogOpen] = useState(false);
  const [isAgreed, setIsAgreed] = useState(false);
  const [paymentHtml, setPaymentHtml] = useState<string>('');
  const [orderNo, setOrderNo] = useState<string>('');
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const [wechatQrCodeUrl, setWechatQrCodeUrl] = useState<string>('');
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<'success' | 'error'>('error');

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          setIsTimeoutDialogOpen(true);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  if (!courseDetail) {
    return <div>加载中...</div>;
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderCourseInfo = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-4 bg-white p-4 rounded-lg shadow-sm">
        <div className="flex-shrink-0 w-[200px]" style={{ width: '200px' }}>
          <img 
            src={courseDetail.coverImage} 
            alt={courseDetail.name} 
            className="w-full h-full object-cover rounded"
          />
        </div>
        <div className="flex-grow min-w-0 flex flex-col justify-between">
          <div>
            <h2 className="text-lg font-semibold mb-2 line-clamp-2">{courseDetail.name}</h2>
            <p className="text-gray-600 text-sm mb-2 truncate">讲师：{teachers?.map(teacher => teacher.name).join(', ')}</p>
          </div>
          <p className="text-gray-600 text-sm">有效期：{courseDetail.validityPeriod}天</p>
        </div>
      </div>
    </div>
  );

  const handleCloseTimeoutDialog = () => {
    setIsTimeoutDialogOpen(false);
    router.push(`/course-detail/${courseDetail.productId}`);
  };

  const pollOrderStatus = async (orderNo: string) => {
    try {
      const orderController = new OrderWebController();
      const status = await orderController.queryOrderStatus(orderNo);

      const shouldStopPolling = ['已支付', '已完成', '已取消'].includes(status);
      
      if (shouldStopPolling) {
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
          pollingRef.current = null;
          setPollingInterval(null);
        }
        // 关闭支付界面
        setPaymentHtml('');
        setWechatQrCodeUrl('');
      }

      switch (status) {
        case '已支付':
        case '已完成':
          setToastType('success');
          setToastMessage("支付成功，正在跳转到学习中心...");
          await new Promise(resolve => setTimeout(resolve, 1500));
          setToastMessage(null);
          router.push('/study-hub');
          break;
        case '已取消':
          setToastType('error');
          setToastMessage("订单已取消，订单支付已取消");
          setTimeout(() => setToastMessage(null), 3000);
          break;
        case '待支付':
          console.log('订单待支付，继续轮询');
          break;
        default:
          console.log('未知状态:', status);
          break;
      }
    } catch (error) {
      console.error('查询订单状态失败:', error);
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
        pollingRef.current = null;
        setPollingInterval(null);
      }
      setToastType('error');
      setToastMessage("查询订单状态失败，请稍后重试");
      setTimeout(() => setToastMessage(null), 3000);
    }
  };

  const handlePayment = async () => {
    try {
      // 清理现有状态
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
        pollingRef.current = null;
        setPollingInterval(null);
      }
      
      // 重置状态
      setPaymentHtml('');
      setWechatQrCodeUrl('');
      
      const orderController = new OrderWebController();
      const orderRequest: CreateOrderRequest = {
        productIds: [courseDetail.productId],
        paymentMethod: selectedPayment,
      };
      
      const createOrderVO = await orderController.createOrder(orderRequest);
      setOrderNo(createOrderVO.orderNo);
      
      // 根据支付方式处理返回结果
      if (selectedPayment === 'wechatPay') {
        // 使用 Promise 和 async/await 来确保状态更新
        await new Promise(resolve => {
          setWechatQrCodeUrl(createOrderVO.paymentHtml);
          setTimeout(resolve, 100);
        });
      } else if (selectedPayment === 'alipay') {
        setPaymentHtml(createOrderVO.paymentHtml);
      }

      const interval = setInterval(() => {
        pollOrderStatus(createOrderVO.orderNo);
      }, 1000);
      pollingRef.current = interval;
      setPollingInterval(interval);
    } catch (error) {
      console.error('支付请求失败:', error);
      // 发生错误时清理状态
      setPaymentHtml('');
      setWechatQrCodeUrl('');
    }
  };

  const handleClosePayment = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
    setPaymentHtml('');
    setWechatQrCodeUrl('');
  };

  const extractNumber = (price: string): number => {
    return parseFloat(price.replace(/[^\d.]/g, ''));
  };

  useEffect(() => {
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
        pollingRef.current = null;
      }
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      {toastMessage && (
        <div className={`fixed top-[200px] left-1/2 transform -translate-x-1/2 -translate-y-1/2 px-6 py-3 rounded-lg shadow-lg z-50 text-lg font-bold ${
          toastType === 'success' 
            ? 'bg-green-100 border-2 border-green-500 text-green-600'
            : 'bg-pink-100 border-2 border-red-500 text-red-500'
        }`}>
          {toastMessage}
        </div>
      )}
      {(paymentHtml || wechatQrCodeUrl) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg w-[800px] h-[600px] relative">
            <button 
              onClick={handleClosePayment}
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
            >
              关闭
            </button>
            {selectedPayment === 'alipay' ? (
              <iframe 
                srcDoc={paymentHtml}
                className="w-full h-full border-none"
                sandbox="allow-scripts allow-forms allow-same-origin"
              />
            ) : (
              <WechatPayModal 
                qrCodeUrl={wechatQrCodeUrl}
                amount={extractNumber(courseDetail.price)}
                onClose={handleClosePayment}
              />
            )}
          </div>
        </div>
      )}
      <main className="flex-grow bg-gray-100">
        <div className="w-[1200px] mx-auto px-4 py-6 mb-4 p-6">
          <Card className="mb-4">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <h1 className="text-xl font-bold mb-2">确认订单信息</h1>
                  <p className="text-gray-500 text-sm">请在10分钟内完成支付，否则订单会自动取消</p>
                </div>
                <p className="text-red-500 font-semibold text-base">剩余支付时间{formatTime(timeLeft)}</p>
              </div>
              {renderCourseInfo()}
              <div className="mt-6 text-right">
                <div className="flex justify-end items-baseline space-x-3 gap-2">
                  <p className="text-2xl font-bold">应付总额：<span className="text-red-500 text-3xl">{courseDetail.price}</span></p>
                  <p className="text-base text-gray-500 line-through my-auto">{courseDetail.originalPrice ?? 0}</p>
                </div>
                <p className="flex justify-end text-base text-gray-500">
                  优惠金额：<span>¥{(extractNumber(courseDetail.originalPrice) - extractNumber(courseDetail.price))}</span>
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold mb-4">选择支付方式</h2>
              <div className="flex flex-wrap gap-4 mb-6">
                <PaymentButton
                  icon={
                    <svg className="w-12 h-12" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13583">
                      <path d="M1010.8 628c0-141.2-141.3-256.2-299.9-256.2-168 0-300.3 115.1-300.3 256.2 0 141.4 132.3 256.2 300.3 256.2 35.2 0 70.7-8.9 106-17.7l96.8 53-26.6-88.2c70.9-53.2 123.7-123.7 123.7-203.3zM618 588.8c-22.1 0-40-17.9-40-40s17.9-40 40-40 40 17.9 40 40c0 22-17.9 40-40 40z m194.3-0.3c-22.1 0-40-17.9-40-40s17.9-40 40-40 40 17.9 40 40-17.9 40-40 40z" fill="#00C800" p-id="13584"></path><path d="M366.3 106.9c-194.1 0-353.1 132.3-353.1 300.3 0 97 52.9 176.6 141.3 238.4l-35.3 106.2 123.4-61.9c44.2 8.7 79.6 17.7 123.7 17.7 11.1 0 22.1-0.5 33-1.4-6.9-23.6-10.9-48.3-10.9-74 0-154.3 132.5-279.5 300.2-279.5 11.5 0 22.8 0.8 34 2.1C692 212.6 539.9 106.9 366.3 106.9zM247.7 349.2c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z m246.6 0c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z" fill="#00C800" p-id="13585"></path>
                    </svg>
                  }
                  text="微信支付"
                  method="wechatPay"
                  selectedPayment={selectedPayment}
                  onSelect={setSelectedPayment}
                />
                <PaymentButton
                  icon={
                    <svg className="w-12 h-12" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4405">
                      <path d="M512 64l338.773333 76.330667A85.333333 85.333333 0 0 1 917.333333 223.573333v407.253334a170.666667 170.666667 0 0 1-86.506666 148.48l-276.757334 156.842666a85.333333 85.333333 0 0 1-84.138666 0L193.173333 779.285333A170.666667 170.666667 0 0 1 106.666667 630.826667V223.573333a85.333333 85.333333 0 0 1 66.56-83.221333L512 64z m0 65.6L187.306667 202.773333a21.333333 21.333333 0 0 0-16.490667 18.346667L170.666667 223.573333v407.253334a106.666667 106.666667 0 0 0 49.792 90.24l4.266666 2.56 276.757334 156.842666a21.333333 21.333333 0 0 0 18.517333 1.216l2.517333-1.216 276.757334-156.864a106.581333 106.581333 0 0 0 36.821333-34.666666c-88.213333-43.242667-201.813333-98.922667-251.050667-123.008-57.749333 72.234667-132.224 116.053333-209.557333 116.053333-130.773333 0-175.189333-116.330667-113.28-192.938667 13.525333-16.704 36.48-32.64 72.149333-41.578666 55.744-13.930667 144.490667 8.682667 227.648 36.586666 13.098667-24.512 24.362667-51.2 33.28-79.488l3.648-12.224H342.613333v-26.410666h132.16v-47.317334h-160.064v-26.410666h160.064v-67.690667c0.064-1.365333 0.746667-9.621333 8.917334-10.986667l2.410666-0.192h64.597334v78.869334h158.293333v26.410666h-158.293333v47.317334h129.194666c-12.352 51.498667-31.146667 98.88-54.698666 140.48 39.210667 14.421333 74.410667 28.096 100.629333 37.013333 36.842667 12.544 83.882667 29.546667 127.530667 45.589333L853.333333 223.573333a21.333333 21.333333 0 0 0-14.272-20.138666l-2.368-0.682667L512 129.6z m-154.026667 342.016c-15.616 1.706667-44.885333 9.429333-60.885333 25.216-48 46.72-19.264 132.096 77.802667 132.096 56.426667 0 112.853333-40.277333 157.12-104.789333-62.997333-34.325333-116.373333-58.88-174.058667-52.522667z" fill="#1296db" p-id="4406"></path>
                    </svg>
                  }
                  text="支付宝"
                  method="alipay"
                  selectedPayment={selectedPayment}
                  onSelect={setSelectedPayment}
                />
              </div>
              <div className="flex justify-between">
                <div className="w-2/3">
                  <h2 className="text-lg font-semibold mb-4">购买说明：</h2>
                  <p className="text-gray-600 text-sm mb-4">
                        感谢您对乐象AI的青睐与选择。本网站所提供的视频、题库均仅支持在 PC端及小程序观看、练习，不提供下载功能。<br/>
                        关于有效期限为自购买之日起365天，在有效期内，您可依据自身的学习情况随时进入学习。本网站学习内容主要依赖于用户自主规划学习安排，因此，除平台故障或其他技术问题而引起您不能学习外，您所购买的内容将不予提供退换服务。<br/>
                        再次郑重提醒，您在提交订单之际，即表明您已充分知晓、理解并同意本购买说明的全部内容。希望您在乐象AI的学习过程中收获满满，如有任何疑问，请随时联系我们的客服团队。<br/>
                        感谢您的支持与配合！<br/>
                  </p>
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="agreement"
                      checked={isAgreed}
                      onChange={(e) => setIsAgreed(e.target.checked)}
                      className="w-4 h-4"
                    />
                    <label htmlFor="agreement" className="text-sm">
                      同意
                      <a 
                        href="/agreement" 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="text-blue-600 hover:underline"
                      > 用户协议</a>
                      {" "}和{" "}
                      <a 
                        href="/privacy" 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="text-blue-600 hover:underline"
                      >隐私条款</a>
                    </label>
                  </div>
                </div>
                <div className="flex items-end">
                  <Button 
                    className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 text-base"
                    onClick={() => {
                      if (!selectedPayment) {
                        setToastType('error');
                        setToastMessage("请选择支付方式");
                        setTimeout(() => setToastMessage(null), 2000);
                      } else if (!isAgreed) {
                        setToastType('error');
                        setToastMessage("请先同意用户协议和隐私条款");
                        setTimeout(() => setToastMessage(null), 2000);
                      } else {
                        handlePayment();
                      }
                    }}
                  >
                    去支付
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default CoursePurchase;
