import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Off, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import { cn } from "../lib/utils";
import ErrorReportDialog from './ErrorReportDialog';

const QuestionControls = ({
  onPrevious,
  onNext,
  hasPrevious,
  hasNext,
  isFavorite,
  onToggleFavorite,
  onNote,
  questionId,
  subjectCode,
}) => (
  <div className="flex justify-between items-center mt-6">
    <div className="space-x-4">
      <Button variant="outline" onClick={onPrevious} disabled={!hasPrevious}>上一题</Button>
      <Button
        onClick={onNext}
        disabled={!hasNext}
        className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-[#ffdf7f] to-[#edb91b] text-black font-normal hover:from-[#edb91b] hover:to-[#ffdf7f]"
      >
        下一题
      </Button>
    </div>
    <div className="space-x-2">
      <Button
        variant="outline"
        onClick={onToggleFavorite}
        className={cn(
          "transition-colors",
          isFavorite && "bg-gradient-to-r from-[#ffdf7f] to-[#edb91b] text-black hover:from-[#edb91b] hover:to-[#ffdf7f]"
        )}
      >
        {isFavorite ? (
          <Star className="w-5 h-5 mr-1 fill-current" />
        ) : (
          <StarOff className="w-5 h-5 mr-1" />
        )}
        {isFavorite ? "已收藏" : "收藏"}
      </Button>
      <Button variant="outline" onClick={onNote}>
        <Pencil className="w-5 h-5 mr-1" />
        笔记
      </Button>
      <ErrorReportDialog
        questionId={questionId}
        subjectCode={subjectCode} >
        <Button variant="outline">
          <AlertTriangle className="w-5 h-5 mr-1" />
          纠错
        </Button>
      </ErrorReportDialog>
    </div>
  </div>
);

export default QuestionControls;