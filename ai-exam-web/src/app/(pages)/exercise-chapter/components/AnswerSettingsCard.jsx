import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { testPracticeWebController } from "@/lib/controller"; // 新增
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
const AnswerSettingsCard = ({
  type,
  questions,
  currentQuestionNumber, // 改为 currentQuestionId
  onQuestionSelect,
  correctCount,
  incorrectCount,
  onClearRecord,
  answeredQuestions,
  autoNext,
  setAutoNext,
  subjectCode, // 新增
  sectionCode, // 新增
  chapterCode, // 新增
}) => {
  const [showClearDialog, setShowClearDialog] = useState(false);
  const { toast } = useToast(); // 新增

  // 获取当前题目
  const currentQuestion = questions.find(
    (q) => q.questionId === currentQuestionNumber,
  );
  // 获取当前题目类型作为默认 tab
  const defaultTabValue = currentQuestion?.type || "single";

  // 获取所有存在的题型
  const availableQuestionTypes = React.useMemo(() => {
    const types = new Set(questions.map((q) => q.type));
    return Array.from(types);
  }, [questions]);

  // 题型显示名称映射
  const typeLabels = {
    single: "单选题",
    multiple: "多选题",
    short: "简答题",
    indefinite_choice: "不定项选择题",
    true_false: "判断题"
  };

  const [activeTab, setActiveTab] = useState(defaultTabValue);
  // 当 currentQuestion 改变时更新 activeTab
  useEffect(() => {
    if (currentQuestion?.type) {
      setActiveTab(currentQuestion.type);
    }
  }, [currentQuestion]);

  const handleClearRecord = async () => {
    try {
      // 调用后台接口清除记录
      await testPracticeWebController.clearExerciseRecords(
        subjectCode,
        "ZJLX",
        sectionCode,
        chapterCode,
      );

      // 调用父组件的清除方法
      onClearRecord();

      // 关闭对话框
      setShowClearDialog(false);

      // 提示成功
      toast({
        title: "清除成功",
        duration: 2000,
      });
    } catch (error) {
      console.error("清除记录失败:", error);
      toast({
        title: "清除失败",
        description: "请稍后重试",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const getButtonColor = (questionId) => {
    if (questionId === currentQuestionNumber) {
      return "bg-white text-black border-2 border-green-500 font-bold";
    }
    if (answeredQuestions[questionId] === true) {
      return "bg-green-500 text-white";
    }
    if (answeredQuestions[questionId] === false) {
      return "bg-red-500 text-white";
    }
    return "bg-white text-black";
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base">答题卡与设置</CardTitle>
        <AlertDialog open={showClearDialog} onOpenChange={setShowClearDialog}>
          <AlertDialogTrigger asChild>
            <Button
              variant="link"
              className="text-sm text-blue-500 hover:text-blue-700"
            >
              清空答题记录
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认清空答题记录</AlertDialogTitle>
              <AlertDialogDescription>
                是否清空当前答题记录？此操作无法撤销。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={handleClearRecord}>
                清空
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList
            className={`grid w-full grid-cols-${availableQuestionTypes.length}`}
          >
            {availableQuestionTypes.map((type) => (
              <TabsTrigger key={type} value={type}>
                {typeLabels[type]}
              </TabsTrigger>
            ))}
          </TabsList>
          <TabsContent value="single" className="mt-4">
            <div className="grid grid-cols-5 gap-2">
              {questions
                .filter((q) => q.type === "single")
                .map((question) => (
                  <Button
                    key={question.questionId}
                    variant="outline"
                    className={`h-10 w-full text-xs ${getButtonColor(
                      question.questionId,
                    )}`}
                    onClick={() => onQuestionSelect(question.questionId)}
                  >
                    {question.number}
                  </Button>
                ))}
            </div>
          </TabsContent>
          <TabsContent value="multiple" className="mt-4">
            <div className="grid grid-cols-5 gap-2">
              {questions
                .filter((q) => q.type === "multiple")
                .map((question) => (
                  <Button
                    key={question.questionId}
                    variant="outline"
                    className={`h-10 w-full text-xs ${getButtonColor(
                      question.questionId,
                    )}`}
                    onClick={() => onQuestionSelect(question.questionId)}
                  >
                    {question.number}
                  </Button>
                ))}
            </div>
          </TabsContent>
          <TabsContent value="true_false" className="mt-4">
            <div className="grid grid-cols-5 gap-2">
              {questions
                .filter((q) => q.type === "true_false")
                .map((question) => (
                  <Button
                    key={question.questionId}
                    variant="outline"
                    className={`h-10 w-full text-xs ${getButtonColor(
                      question.questionId,
                    )}`}
                    onClick={() => onQuestionSelect(question.questionId)}
                  >
                    {question.number}
                  </Button>
                ))}
            </div>
          </TabsContent>
          <TabsContent value="indefinite_choice" className="mt-4">
            <div className="grid grid-cols-5 gap-2">
              {questions
                .filter((q) => q.type === "indefinite_choice")
                .map((question) => (
                  <Button
                    key={question.questionId}
                    variant="outline"
                    className={`h-10 w-full text-xs ${getButtonColor(
                      question.questionId,
                    )}`}
                    onClick={() => onQuestionSelect(question.questionId)}
                  >
                    {question.number}
                  </Button>
                ))}
            </div>
          </TabsContent>
          <TabsContent value="short" className="mt-4">
            <div className="grid grid-cols-5 gap-2">
              {questions
                .filter((q) => q.type === "short")
                .map((question) => (
                  <Button
                    key={question.questionId}
                    variant="outline"
                    className={`h-10 w-full text-xs ${getButtonColor(
                      question.questionId,
                    )}`}
                    onClick={() => onQuestionSelect(question.questionId)}
                  >
                    {question.number}
                  </Button>
                ))}
            </div>
          </TabsContent>
        </Tabs>
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-green-500">答对：{correctCount}题</span>
            <span className="text-red-500">答错：{incorrectCount}题</span>
          </div>
          <div className="mt-2 text-sm">
            <p className="mb-2 font-semibold">图例：</p>
            <div className="flex justify-between">
              <div className="flex items-center">
                <span className="mr-2 h-6 w-6 rounded bg-green-500"></span>
                <span className="text-center">
                  <span className="block">答对</span>
                  <span className="block">题目</span>
                </span>
              </div>
              <div className="flex items-center">
                <span className="mr-2 h-6 w-6 rounded bg-red-500"></span>
                <span className="text-center">
                  <span className="block">答错</span>
                  <span className="block">题目</span>
                </span>
              </div>
              <div className="flex items-center">
                <span className="mr-2 h-6 w-6 rounded border-2 border-green-500"></span>
                <span className="text-center">
                  <span className="block">当前</span>
                  <span className="block">题目</span>
                </span>
              </div>
              <div className="flex items-center">
                <span className="mr-2 h-6 w-6 rounded border border-gray-300 bg-white"></span>
                <span className="text-center">
                  <span className="block">未答</span>
                  <span className="block">题目</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <span>答对自动下一题</span>
          <Switch
            checked={autoNext}
            onCheckedChange={setAutoNext}
            id="auto-next-mode"
          />
        </div>
      </CardContent>
    </Card>
  );
};
export default AnswerSettingsCard;
