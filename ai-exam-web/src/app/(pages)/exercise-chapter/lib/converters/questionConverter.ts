import { BackendResponse, Question, Option } from "../types/question";

/**
 * 将后端返回的试题数据转换为前端使用的格式
 * @param backendData 后端返回的原始数据
 * @returns 转换后的试题数组
 */
export const convertBackendToFrontendQuestions = (
  backendData: BackendResponse
): Question[] => {
  if (!backendData?.allQuestions) {
    return [];
  }

  return backendData.allQuestions.map((q) => {
    // 转换选项格式
    const options: Option[] = Object.entries(q.options || {}).map(
      ([key, value]) => ({
        id: key,
        text: value
      })
    );

    // 确定题目类型
    let questionType: "single" | "multiple" | "short" | 'indefinite_choice' | 'true_false';
    switch (q.questionType) {
      case "CHOICES":
        questionType = "multiple";
        break;
      case "CHOICE":
        questionType = "single";
        break;
      case "OPEN":
        questionType = "short";
        break;
      case "INDEFINITE_CHOICE":
        questionType = "indefinite_choice";
        break;
      case "TRUE_FALSE":
        questionType = "true_false";
        break;
      default:
        questionType = "single"; // 默认为单选题
    }

    // 处理答案格式
    let correctAnswer: string | string[];
    if (questionType === "multiple" || questionType === 'indefinite_choice') {
      correctAnswer = q.rightAnswer.split("");
    } else {
      correctAnswer = q.rightAnswer;
    }

    return {
      questionId: q.questionId, // 确保包含 questionId
      number: q.number,
      type: questionType,
      text: q.questionContent,
      options,
      correctAnswer,
      explanation: q.explanation,
      total: backendData.allQuestions.length,
      location: q.location,
    };
  });
};
