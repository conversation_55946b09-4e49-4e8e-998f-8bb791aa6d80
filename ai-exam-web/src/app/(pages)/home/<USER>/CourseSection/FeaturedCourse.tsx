/**
 * This code was generated by Builder.io.
 */
import React from "react";
import Image from 'next/image';
import Link from 'next/link';
import { ProductCourseVO } from "@/apis/product-vo";

const FeaturedCourse: React.FC<{course: ProductCourseVO}> = ({ course }) => {
  if (!course || !course.coverImage) return null;
  const { name, description, viewCount, videoDuration, coverImage, productId } = course;
  return (
    <Link href={`/course-detail/${productId}`} className="block" target="_blank" rel="noopener noreferrer">
      <article className="flex flex-col">
        <div className="flex relative flex-col max-md:mt-8 max-md:max-w-full">
          <div className="flex relative flex-col items-start w-full text-base text-white rounded-none max-md:px-5 max-md:max-w-full">
            <img
              src={coverImage}
              alt=""
              width={500}
              height={300}
              className="w-full rounded-[20px]"
            />
            <div className="absolute p-5 top-[200px]">
              <div className="inline-block px-3 py-2 mt-[20px] font-medium text-black rounded-full shadow-[0px_2px_10px_rgba(42,112,255,0.345)] max-md:mt-10"
                style={{
                  background: 'linear-gradient(90deg, #FFDF7F 0%, #EDB91B 100%)',
                }}
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center w-7 h-7 bg-black bg-opacity-10 rounded-full">
                    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 7L0 13.9282L0 0.0717969L12 7Z" fill="black"/>
                    </svg>
                  </div>
                  <div className="text-base font-mono">{videoDuration}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col max-md:px-5 max-md:max-w-full">
          <h3 className="font-bold text-lg text-grey-700 leading-6 m-0 !text-sm lg:!text-lg mt-4 mb-1 lg:!mb-0">
            {name}
          </h3>
          <p className="text-sm leading-5 m-0 text-grey-500 mt-2">
            {description}
          </p>
        </div>
      </article>
    </Link>
  );
};

export default FeaturedCourse;
