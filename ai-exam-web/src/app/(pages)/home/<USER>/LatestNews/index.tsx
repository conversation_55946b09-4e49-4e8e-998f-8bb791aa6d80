/**
 * This code was generated by Builder.io.
 */
import React from "react";
import NewsCard from "./NewsCard";
import SectionTitle from "@/components/SectionTitle";
import { LATEST_NEWS_DATA } from "./constants";

const LatestNews: React.FC = () => {
  return (
    <section className="flex flex-col">
      <SectionTitle title="最新资讯" subtitle="新风向指引 —— 科技、环保与人文融合发展" />
      <main className="w-full">
        <div className="w-full flex flex-wrap -mx-2">
          {LATEST_NEWS_DATA.map((item, index) => (
            <NewsCard key={index} {...item} />
          ))}
        </div>
      </main>
    </section>
  );
};

export default LatestNews;
