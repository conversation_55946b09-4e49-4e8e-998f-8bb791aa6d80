/**
 * This code was generated by Builder.io.
 */
import React from "react";
import FeatureCard from "./FeatureCard";
import AICard from "./AICard";
import Image from "next/image";
import SectionTitle from "@/components/SectionTitle";

interface AILearningCenterProps {}

const AILearningCenter: React.FC<AILearningCenterProps> = () => {
  const featureCards = [
    {
      title: "AI智学",
      description: ["AI提炼重点考点", '把“厚书”变“薄”'],
      imageSrc:
        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/AI%E6%99%BA%E5%AD%A6.png",
    },
    {
      title: "AI智练",
      description: ["AI智能筛选，精准练题", "告别冗余，专注核心"],
      imageSrc:
        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/AI%E6%99%BA%E7%BB%83.png",
    },
  ];

  const aiCards = [
    {
      title: "AI诊断",
      description: ["精准学情诊断", "开启个性化备考学习"],
      imageSrc:
        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/AI%E8%AF%8A%E6%96%AD.png",
    },
    {
      title: "AI备考建议",
      description: ["针对性查漏补缺", "让学习成果稳步增长"],
      imageSrc:
        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/AI%E5%A4%87%E8%80%83.png",
    },
    {
      title: "AI助教",
      description: ["功能升级中", "敬请期待"],
      imageSrc:
        "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/homePage/AI%E5%8A%A9%E6%95%99.png",
    },
  ];

  return (
    <main className="flex flex-col w-[1200px] mx-auto">
      <SectionTitle title="AI智能学习中心" subtitle="量身打造学习路径，推动学习高效前行" />
      <section className="relative w-full max-md:max-w-full">
        <div className="flex gap-5 max-md:flex-col">
          {featureCards.map((card, index) => (
            <FeatureCard key={index} {...card} />
          ))}
        </div>
      </section>
      <section className="relative mt-5 w-full max-md:max-w-full">
        <div className="flex gap-5 max-md:flex-col">
          {aiCards.map((card, index) => (
            <AICard key={index} {...card} />
          ))}
        </div>
      </section>
    </main>
  );
};

export default AILearningCenter;
