import React from 'react';
import { CommentVO } from '@/apis/comment-vo';

const Comment: React.FC<CommentVO> = ({ avatarUrl, nickname, comment }) => (
  <div className="bg-gray-100 p-4 rounded-lg break-inside-avoid mb-4">
  <div className="flex items-center mb-2">
    {avatarUrl ? (
      <img src={avatarUrl} alt={nickname} className="w-10 h-10 rounded-full mr-3" />
    ) : (
      <div className="w-10 h-10 rounded-full bg-gray-300 mr-3 flex items-center justify-center text-gray-600">
        {nickname[nickname.length - 1]}
      </div>
    )}
    <div>
      <h5 className="font-bold text-[#96ce73]">{nickname}</h5>
    </div>
  </div>
  <p className="text-gray-700 text-sm">{comment}</p>
</div>
);

const CommunityFeedback: React.FC<{comments: CommentVO[]}> = ({comments}) => {
  return (
    <div className="container mx-auto px-4">
      <div className="columns-1 sm:columns-2 lg:columns-3 gap-4">
        {comments.map((comment, index) => (
          <Comment key={index} {...comment} />
        ))}
      </div>
    </div>
  );
};

export default CommunityFeedback;