import React from 'react';

const FAQPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        <article className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-center mb-12">常见问题</h1>

          <div className="prose prose-lg mx-auto space-y-8">
            <section>
              <ol className="list-decimal pl-6 space-y-4">
                <li>
                  <h3 className="font-semibold">注册时收不到验证码怎么办？</h3>
                  <p>首先检查手机号码是否填写正确。如果号码无误，可能是网络延迟问题，可以尝试重新获取验证码。若多次尝试无果，可通过网站下方联系电话联系网站客服，告知他们您遇到的问题，客服会协助您解决。</p>
                </li>
                <li>
                  <h3 className="font-semibold">忘记登录密码怎么办？</h3>
                  <p>在登录页面有 “忘记密码” 选项，点击后可通过手机号重置密码，系统会发送重置验证码到您的手机上，按照提示完成新密码设置即可。</p>
                </li>
                <li>
                  <h3 className="font-semibold">如何查找自己感兴趣的课程？</h3>
                  <p>您可以通过选择您报考的证书、所需要的专业快速定位到相应课程，同时网站提供单科、全科以满足您的不同学习需求。</p>
                </li>
                <li>
                  <h3 className="font-semibold">课程视频无法播放怎么办？</h3>
                  <p>检查您的网络连接是否正常，可尝试刷新页面或重新连接网络。若网络正常，可能是浏览器问题，尝试更换不同的浏览器。如果问题仍然存在，可能是视频本身的问题，可通过网站下方联系电话联系网站客服，并告知他们课程名称和出现问题的具体情况，我们会尽快解决。</p>
                </li>
                <li>
                  <h3 className="font-semibold">课程是否有有效期？</h3>
                  <p>课程有效期为自购买之日起，365天。</p>
                </li>
                <li>
                  <h3 className="font-semibold">有哪些支付方式？</h3>
                  <p>本网站支持微信支付、支付宝支付，您只需在支付页面选择相应平台，按照提示操作即可轻松完成支付。</p>
                </li>
                <li>
                  <h3 className="font-semibold">支付成功但课程未解锁怎么办？</h3>
                  <p>这种情况可能是系统延迟问题。请先稍等片刻，刷新页面查看课程是否解锁。如果长时间未解锁，可以保存支付成功的凭证，如支付订单截图，然后联系网站客服，他们会核实您的支付情况并为您解决问题。</p>
                </li>
                <li>
                  <h3 className="font-semibold">收费课程可以退款吗？</h3>
                  <p>鉴于本网络培训课程的特性，其主要依赖于用户自主规划学习安排。因此，除课程出现因平台故障或其他技术问题而无法获得观看的情形外，您所购买的课程将不予提供退换服务。</p>
                </li>
                <li>
                  <h3 className="font-semibold">可以下载课程资料或视频离线学习吗？</h3>
                  <p>网站为保障课程资源的合法权益以及平台的稳定运营，所提供的课程视频均仅支持在 PC端学习，不提供下载功能。</p>
                </li>
                <li>
                  <h3 className="font-semibold">题库内练习题可以下载吗？</h3>
                  <p>为了方便您的学习，我们支持通过PC端、微信小程序进行题库练习，不提供下载功能。</p>
                </li>
              </ol>
            </section>
          </div>
        </article>
      </div>
    </div>
  );
};

export default FAQPage; 