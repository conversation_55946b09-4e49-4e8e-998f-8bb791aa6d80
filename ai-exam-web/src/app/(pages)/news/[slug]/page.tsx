import React from 'react';
import { NEWS_CONTENT } from '../constants';
import { notFound } from 'next/navigation';

interface NewsPageProps {
  params: {
    slug: string;
  };
}

const CHAPTER_TITLES = [
  '设计创新：AI的加入',
  '施工自动化：AI与机器人的结合',
  '安全监控：AI的预警系统',
  '环境监测：AI的绿色建筑',
  '资源管理：AI的优化能力',
  '未来展望：AI的深入应用',
  '能源管理：AI的精准控制',
  '围护结构：AI的智能设计',
  '可再生能源：AI的优化利用',
  '未来展望：AI的发展挑战',
  '需求预测：AI的精准分析',
  '物流运输：AI的路径优化',
  '供应链管理：AI的智能决策',
  '未来挑战：AI的发展方向',
  '结构安全：AI的智能监测',
  '火灾防护：AI的实时预警',
  '环境监控：AI的全方位防护',
  '未来展望：AI的持续创新'
];

const NewsPage: React.FC<NewsPageProps> = ({ params }) => {
  const newsContent = NEWS_CONTENT[params.slug];
  
  if (!newsContent) {
    notFound();
  }

  const formatContent = (content: string) => {
    const paragraphs = content.split('\n\n');
    return paragraphs.map((paragraph, index) => {
      if (CHAPTER_TITLES.includes(paragraph)) {
        return (
          <h2 key={index} className="text-2xl font-bold text-gray-900 mt-10 mb-6 border-l-4 border-blue-500 pl-4">
            {paragraph}
          </h2>
        );
      }
      return (
        <p key={index} className="mb-6 text-lg text-gray-800 leading-relaxed">
          {paragraph}
        </p>
      );
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
<div className="container mx-auto px-4 py-12 max-w-5xl">
        <article className="bg-white rounded-lg shadow-sm p-8">
          <header className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {newsContent.title}
            </h1>
            <h2 className="text-xl text-gray-600 mb-6">
              {newsContent.subTitle}
            </h2>
            <div className="text-sm text-gray-500 flex items-center justify-center gap-4">
              <span>{newsContent.publishDate}</span>
              <span>•</span>
              <span>发布于：{newsContent.location}</span>
            </div>
      </header>
          
          <div className="prose prose-lg mx-auto">
            {formatContent(newsContent.content)}
          </div>
        </article>
      </div>
    </div>
  );
};

export default NewsPage; 