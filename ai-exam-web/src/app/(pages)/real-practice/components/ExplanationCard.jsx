import React from 'react';
import { cn } from "../lib/utils";
import { CheckCircle2, XCircle } from "lucide-react";

const ExplanationCard = ({ correctAnswer, selectedOptions, explanation }) => {
  const correctAnswerDisplay = Array.isArray(correctAnswer) ? correctAnswer.join(', ') : correctAnswer;
  const selectedOptionsDisplay = selectedOptions.join(', ');
  const isCorrect = Array.isArray(correctAnswer) 
    ? selectedOptions.length === correctAnswer.length && selectedOptions.every(opt => correctAnswer.includes(opt))
    : selectedOptions[0] === correctAnswer;
  
    // 添加一个用于安全渲染HTML的函数
  const createMarkup = (htmlContent) => {
    return { __html: htmlContent };
  };
    
  return (
    <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
      <div className="flex items-center space-x-8 mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-gray-700 font-semibold">正确答案</span>
          <span className="text-green-500 font-bold text-xl">
          <span dangerouslySetInnerHTML={createMarkup(correctAnswerDisplay)} />
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-gray-700 font-semibold">我的答案</span>
          <span className={cn(
            "font-bold text-xl",
            isCorrect ? "text-green-500" : "text-red-500"
          )}>
            <span dangerouslySetInnerHTML={createMarkup(selectedOptionsDisplay)} />
          </span>
          {isCorrect ? (
            <CheckCircle2 className="w-6 h-6 text-green-500" />
          ) : (
            <XCircle className="w-6 h-6 text-red-500" />
          )}
        </div>
      </div>
      <h4 className="font-semibold mb-2">答案解析</h4>
      <p>
      <span dangerouslySetInnerHTML={createMarkup(explanation)} />
      </p>
    </div>
  );
};

export default ExplanationCard;