import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { useToast } from "@/app/(pages)/user-center/hooks/use-toast";

const PasswordSection = () => {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const { toast } = useToast();

  return (
    <div className="space-y-4 py-4">
      <div className="flex items-center space-x-4">
        <Label className="w-24">当前密码:</Label>
        <Input 
          type="password" 
          placeholder="当前密码" 
          className="max-w-xs"
          value={currentPassword}
          onChange={(e) => setCurrentPassword(e.target.value)}
        />
      </div>
      <div className="flex items-center space-x-4">
        <Label className="w-24">新密码:</Label>
        <Input 
          type="password" 
          placeholder="新密码" 
          className="max-w-xs"
          value={newPassword}
          onChange={(e) => setNewPassword(e.target.value)}
        />
      </div>
      <div className="flex items-center space-x-4">
        <Label className="w-24">确认新密码:</Label>
        <Input 
          type="password" 
          placeholder="确认新密码" 
          className="max-w-xs"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
        />
      </div>
    </div>
  );
};

export default PasswordSection;