import { useEffect, useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useUser } from "@/app/(pages)/user-center/contexts/UserContext";
import { useToast } from "@/app/(pages)/user-center/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import AvatarSection from './AvatarSection';
import PasswordSection from './PasswordSection';
import { AccountWebController } from '@/apis/web-account';

const ProfileSection = () => {
  const { username, setUsername } = useUser();
  const { toast } = useToast();
  const [userInfo, setUserInfo] = useState({
    nickname: '',
    gender: '',
    phone: '',
    avatarUrl: '',
    id: 0,
    token: ''
  });
  const [tempNickname, setTempNickname] = useState('');

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const accountController = new AccountWebController();
        const data = await accountController.getAccount();
        setUserInfo(data);
        setTempNickname(data.nickname);
      } catch (error) {
        toast({
          title: "获取用户信息失败",
          description: "请稍后重试",
          variant: "destructive"
        });
      }
    };
    fetchUserInfo();
  }, []);

  const handleSave = async () => {
    try {
      const accountController = new AccountWebController();
      const updatedInfo = await accountController.updateAccount({
        ...userInfo,
        nickname: tempNickname,
        id: userInfo.id,
        token: userInfo.token
      });
      
      setUserInfo(updatedInfo);
      setUsername(tempNickname);
      
      toast({
        title: "保存成功",
        description: "个人信息已更新",
      });

      window.location.reload();
      
    } catch (error) {
      toast({
        title: "保存失败",
        description: "请稍后重试",
        variant: "destructive"
      });
    }
  };

  const handleAvatarChange = (newAvatarUrl: string) => {
    setUserInfo(prev => ({
      ...prev,
      avatarUrl: newAvatarUrl
    }));
  };

  return (
    <div className="space-y-8">
      {/* Personal Info Section */}
      <div className="border rounded-lg px-6 py-4">
        <h2 className="text-lg font-semibold mb-4">个人资料</h2>
        <div className="space-y-8">
          <div className="flex items-center space-x-4">
            <Label className="w-20">昵称:</Label>
            <Input 
              className="max-w-xs" 
              value={tempNickname}
              onChange={(e) => setTempNickname(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-4">
            <Label className="w-20">性别:</Label>
            <Select value={userInfo.gender} onValueChange={(value) => setUserInfo(prev => ({...prev, gender: value}))}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="男">男</SelectItem>
                <SelectItem value="女">女</SelectItem>
                <SelectItem value="保密">保密</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-4">
            <Label className="w-20">手机:</Label>
            <span className="text-gray-500">{userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}</span>
          </div>
        </div>
      </div>

      {/* Avatar Section */}
      <div className="border rounded-lg px-6 py-4">
        <h2 className="text-lg font-semibold mb-4">头像</h2>
        <AvatarSection 
          avatarUrl={userInfo.avatarUrl} 
          onAvatarChange={handleAvatarChange}
        />
      </div>

      {/* Save Button */}
      <div className="flex justify-center">
        <Button 
          className="bg-[#8DC63F] hover:bg-[#7DB32F] w-32"
          onClick={handleSave}
        >
          保存
        </Button>
      </div>
    </div>
  );
};

export default ProfileSection;