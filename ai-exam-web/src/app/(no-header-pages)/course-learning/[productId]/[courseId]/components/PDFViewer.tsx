"use client"
import React, { useState, useEffect } from 'react';
import * as pdfjsLib from 'pdfjs-dist';
import 'pdfjs-dist/build/pdf.worker.min.mjs';
import { NoteVO } from '@/apis/note-vo';
import Notes from './Notes';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PDFViewerProps {
  fileUrl: string;
  productId: number;
  courseId: number;
}

const PDFViewer: React.FC<PDFViewerProps> = ({ fileUrl, productId, courseId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfDoc, setPdfDoc] = useState<any>(null);
  const [pageNum, setPageNum] = useState(1);
  const [numPages, setNumPages] = useState(0);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  useEffect(() => {
    const loadPdf = async () => {
      try {
        const loadingTask = pdfjsLib.getDocument(fileUrl);
        const pdf = await loadingTask.promise;
        setPdfDoc(pdf);
        setNumPages(pdf.numPages);
        setLoading(false);
      } catch (e) {
        console.error('PDF加载失败:', e);
        setError(`PDF加载失败: ${e instanceof Error ? e.message : String(e)}`);
        setLoading(false);
      }
    };

    loadPdf();
  }, [fileUrl]);

  useEffect(() => {
    if (pdfDoc) {
      renderPage(pageNum);
    }
  }, [pdfDoc, pageNum]);

  const renderPage = async (num: number) => {
    if (pdfDoc) {
      const page = await pdfDoc.getPage(num);
      const scale = 1.5;
      const viewport = page.getViewport({ scale });
      const canvas = document.getElementById('pdf-canvas') as HTMLCanvasElement;
      const ctx = canvas.getContext('2d');

      canvas.height = viewport.height;
      canvas.width = viewport.width;

      ctx?.clearRect(0, 0, canvas.width, canvas.height);

      const renderContext = {
        canvasContext: ctx,
        viewport: viewport,
      };

      try {
        await page.render(renderContext).promise;
      } catch (error) {
        console.error('渲染页面时出错:', error);
      }
    }
  };

  // 添加阻止右键菜单的处理函数
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
  };

  // 添加阻止拖拽的处理函数
  const handleDragStart = (e: React.DragEvent) => {
    e.preventDefault();
  };

  if (loading) {
    return <div className="loading-message">正在加载 PDF...</div>;
  }

  if (error) {
    return <div className="error-message text-red-500">{error}</div>;
  }

  return (
    <div 
      className="pdf-viewer-container flex flex-col"
      onContextMenu={handleContextMenu}
    >
      <div className="flex flex-1 relative">
        <div className={`pdf-sidebar w-[100px] border-r border-gray-200 transition-all duration-300 ${isSidebarOpen ? '' : '-ml-[100px]'}`}>
          <div className="p-2.5">
            {[...Array(numPages)].map((_, index) => (
              <div
                key={index}
                className={`cursor-pointer text-center block rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground ${pageNum === index + 1 ? 'bg-accent text-accent-foreground' : ''}`}
                onClick={() => setPageNum(index + 1)}
              >
                第{index + 1}页
              </div>
            ))}
          </div>
        </div>
        <button
          className={`absolute ${isSidebarOpen ? 'left-[100px]' : 'left-0'} top-1/2 -translate-y-1/2 bg-white p-2 text-sm font-medium border border-gray-200 rounded-full text-black z-10`}
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          {isSidebarOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
        </button>
        <div className="pdf-viewer flex-1 p-2.5">
          <canvas 
            id="pdf-canvas" 
            className="w-full overflow-y-auto"
            onContextMenu={handleContextMenu}
            onDragStart={handleDragStart}
          ></canvas>
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;