import { ErrorBoundary } from 'react-error-boundary';
import { Providers } from '@/redux/provider'
import ErrorFallback from '@/components/ErrorFallback'
import { ThemeProvider } from '@/components/ThemeProvider'

export default function SignLayout({ children }: { children: React.ReactNode }) {
    return (
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <Providers>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
            <div className="min-h-screen w-full bg-white font-sans antialiased">
              {children}
            </div>
          </ThemeProvider>
        </Providers>
      </ErrorBoundary>
    )
  }