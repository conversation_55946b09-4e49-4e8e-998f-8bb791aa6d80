import { AppDispatch } from '../store';
import { ProductQuestionVO } from '@/apis/product-vo';
import { productWebController } from "@/lib/controller";
import {
  FETCH_QUESTION_BANK_REQUEST,
  FETCH_QUESTION_BANK_SUCCESS,
  FETCH_QUESTION_BANK_FAILURE,
  SET_MAJOR_CODE,
  SET_SORT_FIELD
} from '../actionTypes/questionBankActionTypes';
import { ALL } from '@/lib/config';

export const fetchQuestionBankRequest = () => ({
  type: FETCH_QUESTION_BANK_REQUEST as typeof FETCH_QUESTION_BANK_REQUEST
});

export const fetchQuestionBankSuccess = (questionBank: ProductQuestionVO[]) => ({
  type: FETCH_QUESTION_BANK_SUCCESS as typeof FETCH_QUESTION_BANK_SUCCESS,
  payload: questionBank
});

export const fetchQuestionBankFailure = (error: string) => ({
  type: FETCH_QUESTION_BANK_FAILURE as typeof FETCH_QUESTION_BANK_FAILURE,
  payload: error
});

export const setMajorCode = (majorCode: string) => ({
  type: SET_MAJOR_CODE as typeof SET_MAJOR_CODE,
  payload: majorCode
});

export const setSortField = (sortField: string) => ({
  type: SET_SORT_FIELD as typeof SET_SORT_FIELD,
  payload: sortField
});

export const fetchQuestionBank = () => {
  return async (dispatch: AppDispatch, getState: () => any) => {
    const { majorCode, sortField } = getState().questionBank;
    dispatch(fetchQuestionBankRequest());
    try {
      const questionBank = await productWebController.questionList({
        certificateCode: getState().certificate.selectedCertificate[0],
        majorCode: majorCode === ALL ? '' : majorCode, 
        sortField
      }) || [];
      dispatch(fetchQuestionBankSuccess(questionBank));
    } catch (error) {
      dispatch(fetchQuestionBankFailure(error instanceof Error ? error.message : '获取题库列表失败'));
    }
  };
};