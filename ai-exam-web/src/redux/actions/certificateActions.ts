import { AppDispatch } from '../store';
import { CertificateWithMajorsVO } from '@/apis/certificate-vo';

import {
  SET_CERTIFICATES,
  SET_SELECTED_CERTIFICATE
} from '../actionTypes/certificateActionTypes';

export const setCertificates = (certificates: CertificateWithMajorsVO[]) => {
  return {
    type: SET_CERTIFICATES as typeof SET_CERTIFICATES,
    payload: certificates
  }
};

export const setSelectedCertificate = (selectedCertificate: string[]) => ({
  type: SET_SELECTED_CERTIFICATE as typeof SET_SELECTED_CERTIFICATE,
  payload: selectedCertificate
});