import { configureStore, AnyAction } from '@reduxjs/toolkit'
import { thunk } from 'redux-thunk'
import certificateReducer from './reducers/certificateReducer'
import courseReducer from './reducers/courseReducer'
import questionBankReducer from './reducers/questionBankReducer'

export const store = configureStore({
  reducer: {
    certificate: certificateReducer,
    course: courseReducer,
    questionBank: questionBankReducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(thunk),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch