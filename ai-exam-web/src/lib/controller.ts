import { CertificateWebController } from "@/apis/web-certificate";

import { CourseWebController } from "@/apis/web-course";
import { HomePageWebController } from "@/apis/web-banner";
import { RecommendWebController } from "@/apis/web-recommend";
import { InformationWebController } from "@/apis/web-information";
import { CommentWebController } from "@/apis/web-comment";

import { AccountWebController } from "@/apis/web-account";
import { QuestionWebController } from "@/apis/web-question";

import { ProductWebController } from "@/apis/web-product";
import { NoteWebController } from "@/apis/web-note";
import { UserPermissionsController } from "@/apis/web-user";
import { StudyHubController, ChapterQuestionWebController, FeedbackFormController, TestPracticeWebController, AiPracticeWebController,ErrorBookAndFavoritesWebController } from "@/apis/web-studyhub";
// 获取所有考试&专业
export const certificateWebController = new CertificateWebController();

// 首页
export const courseWebController = new CourseWebController()
export const homePageWebController = new HomePageWebController();
export const recommendWebController = new RecommendWebController();
export const informationWebController = new InformationWebController();
export const commentWebController = new CommentWebController();

// 登录/注册
export const accountWebController = new AccountWebController();

// 题库
export const questionWebController = new QuestionWebController();

// 课程
export const productWebController = new ProductWebController();

// 笔记
export const noteWebController = new NoteWebController();

export const userPermissionsController = new UserPermissionsController();

// 学习中心
export const studyHubController = new StudyHubController();
export const chapterQuestionWebController = new ChapterQuestionWebController();
export const feedbackFormController = new FeedbackFormController();
export const testPracticeWebController = new TestPracticeWebController();
export const aiPracticeWebController = new AiPracticeWebController();
export const errorBookAndFavoritesWebController = new ErrorBookAndFavoritesWebController();