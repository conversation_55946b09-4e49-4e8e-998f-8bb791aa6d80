// import axiosInstance from '../lib/axiosInstance';

import exp from "constants";

export interface CertificateWithSubjectsVO {
  certificateCode: string;
  certificateDesc: string;
  certificateName: string;
  subjects: SubjectVO[];
}

export interface SubjectVO {
  subjectCode: string;
  subjectName: string;
}

export interface GenerateBatchNumResVO {
  batchNum: string;
}

export interface BooleanResultVO {
  successFlag: boolean;
}

export interface LocationInformationVO {
  // 练习模式 EXAM: 章节练习, ZTYLLXMS: 真题演练练习模式, AIZL: AI智练
  exerciseMode: string;
  questionId: number;
  questionSetId?: number | null;
  aiExerciseRecordId?: number;
  // 章节编号(章节练习时, 该值必传)
  sectionCode?: string;
  // 章节编号(章节真题时, 该值必传)
  chapterCode?: string;
  // 科目编号
  subjectCode?: string;
}
