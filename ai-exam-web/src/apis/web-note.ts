import axiosInstance from '../lib/axiosInstance';

import {DeleteNoteRequest, NoteDetailRequest, NoteListRequest, NoteVO} from './note-vo';

export class NoteWebController {

 public constructor() {

  }

 public  async deleteNoteById(deleteNoteRequest: DeleteNoteRequest): Promise<boolean>  {
    return (await axiosInstance.post('/api/web/note/delete_note_by_id', deleteNoteRequest)).data;
  }

 public  async getNoteDetail(noteRequest: NoteDetailRequest): Promise<NoteVO>  {
    return (await axiosInstance.post('/api/web/note/get_note_detail', noteRequest, {})).data;
  }

 public  async getNoteList(noteListRequest: NoteListRequest): Promise<NoteVO[]>  {
    return (await axiosInstance.post('/api/web/note/get_note_list', noteListRequest, {})).data;
  }

 public  async takeNotes(noteVO: NoteVO): Promise<boolean>  {
    return (await axiosInstance.post('/api/web/note/take_notes', noteVO)).data;
  }

}

