import axiosInstance from '../lib/axiosInstance';


export interface BatchProductQuestionDetailsRequest {
  productIds: number[];
}

export interface ProductCourseVO {
  access: boolean;
  certificateCode: string;

/**
*	封面角标，用于标识标签，如“精品”、“热门”等
*/
  coverCornerBadge: string;

/**
*	封面图片
*/
  coverImage: string;

/**
*	描述
*/
  description: string;

/**
*	详情图片
*/
  detailImages: string[];

/**
*	难度
*/
  difficulty: string;

/**
*	是否允许免费试用
*/
  freeTrial: boolean;

/**
*	学习人数
*/
  learnCount: number | null;

/**
*	学习形式
*/
  learningFormat: string;
  majorCode: string;

/**
*	课程名称
*/
  name: string;

/**
*	原价
*/
  originalPrice: string;

/**
*	价格
*/
  price: string;

/**
*	商品 ID
*/
  productId: number | null;

/**
*	商品分类，区分课程商品、题库商品
*/
  productType: string;

/**
*	购买说明
*/
  purchaseInstructions: string;

/**
*	二级分类，在课程商品上，表示专业全科、公共全科、专业单科
*/
  secondCategory: string;
  subjectCode: string;

/**
*	人工标签
*/
  tags: string;

/**
*	有效期
*/
  validityPeriod: number | null;

/**
*	课程时长
*/
  videoDuration: string;

/**
*	浏览人数
*/
  viewCount: number | null;
}

export interface ProductDetailRequest {
  productId: number | null;
}

export interface ProductHomeSelectedCourseListRequest {
  certificateCode: string;

/**
*	热门
*/
  popular: boolean;

/**
*	精品
 （与热门字段二选一赋值为 true，有且仅有一个字段允许为 true）
*/
  premium: boolean;
}

export interface ProductListRequest {
  certificateCode: string;
  majorCode: string;

/**
*	{@link ProductSortEnum ProductSortEnum}
*/
  sortField: string;
}

export interface ProductQuestionRelatedQuestionsVO {
  danKe: ProductQuestionVO[];
  quanKe: ProductQuestionVO[];
}

export interface ProductQuestionVO {
  certificateCode: string;

/**
*	封面角标，用于标识标签，如“精品”、“热门”等
*/
  coverCornerBadge: string;

/**
*	封面图片
*/
  coverImage: string;

/**
*	描述
*/
  description: string;

/**
*	详情图片
*/
  detailImages: string[];

/**
*	是否允许免费试用
*/
  freeTrial: boolean;

/**
*	学习人数
*/
  learnCount: number | null;

/**
*	列表页背景图片
*/
  listPageBackgroundImage: string;
  majorCode: string;
  majorName: string;

/**
*	课程名称
*/
  name: string;

/**
*	原价
*/
  originalPrice: string;

/**
*	价格
*/
  price: string;

/**
*	商品 ID
*/
  productId: number | null;

/**
*	商品分类，区分课程商品、题库商品
*/
  productType: string;

/**
*	购买说明
*/
  purchaseInstructions: string;

/**
*	题库类型列表
 eg：AI智练、真题演练、模拟考试、章节题库
*/
  questionSetTypeList: string[];

/**
*	二级分类，在课程商品上，表示专业全科、公共全科、专业单科
*/
  secondCategory: string;
  subjectCode: string;
  subjectName: string;

/**
*	共 n 科
*/
  subjectsCount: number | null;

/**
*	人工标签
*/
  tags: string[];

/**
*	有效期
*/
  validityPeriod: number | null;

/**
*	浏览人数
*/
  viewCount: number | null;
}

