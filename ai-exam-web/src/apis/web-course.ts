import axiosInstance from '../lib/axiosInstance';

import {CourseDetailRequest, CourseMenuRequest, CourseMenuVO, CourseVO, TeacherVO} from './course-vo';

export class CourseWebController {

 public constructor() {

  }

 public  async getCourseDetail(request: CourseDetailRequest): Promise<CourseVO>  {
    return (await axiosInstance.post('/api/web/course/get_course_detail', request, {})).data;
  }

 public  async getTeachersByProductId(request: CourseMenuRequest): Promise<TeacherVO[]>  {
    return (await axiosInstance.post('/api/web/course/get_teachers_by_product_id', request, {})).data;
  }

 public  async menu(request: CourseMenuRequest): Promise<CourseMenuVO[]>  {
    return (await axiosInstance.post('/api/web/course/menu', request, {})).data;
  }

 public  async menuWithToken(request: CourseMenuRequest): Promise<CourseMenuVO[]>  {
    return (await axiosInstance.post('/api/web/course/menu_with_token', request, {})).data;
  }

}

