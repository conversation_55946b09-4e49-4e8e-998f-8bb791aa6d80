/**
 * This code was generated by Builder.io.
 */
import React from "react";
import { getCertificateCode, getCookie } from "@/lib/cookieParser";
import { defaultSelectedCertificate } from "@/lib/config";
import { accountWebController, certificateWebController } from "@/lib/controller";
import { CertificateWithMajorsVO } from '@/apis/certificate-vo';
import { cookies } from 'next/headers';
import Logo from "./Logo";
import NavBar from "./NavBar";
import UserProfile from "./UserProfile";
import ChooseCertificate from './ChooseCertificate';

async function getData() {
  // 添加 cache: 'no-store' 选项来禁用缓存
  const cookieStore = cookies()
  const token = cookieStore.get('token')
  
  // 添加 no-cache 头
  const fetchOptions = {
    cache: 'no-store',
    headers: {
      'Cache-Control': 'no-cache'
    }
  };

  let accountInfo = null;

  // 如果有token，设置环境变量
  if (token) {
    process.env.AI_EXAM_AUTHORIZATION = token.value;
    try {
      accountInfo = await accountWebController.getAccount();
    } catch (error) {
      console.error("获取用户信息失败:", error);
    }
  }

  let certificates: CertificateWithMajorsVO[] = [];

  try {
    certificates = await certificateWebController.list();
  } catch (error) {
    console.error("获取证书列表失败:", error);
  }

  const initialSelectedCertificate = [getCertificateCode()];

  return {
    accountInfo,
    certificates,
    initialSelectedCertificate
  };
}

const Header: React.FC = async () => {

  const { accountInfo, certificates, initialSelectedCertificate } = await getData();

  return (
    <header className="w-full bg-white shadow-sm  h-24">
      <div className="w-[1200px] flex flex-wrap h-full gap-10 max-md:px-5 mx-auto justify-between">
        <nav className="flex gap-14 text-lg font-medium text-center text-black whitespace-nowrap max-md:max-w-full">
          <Logo />
          <ChooseCertificate
              initialCertificates={certificates}
              initialSelectedCertificate={initialSelectedCertificate as string[]}
          />
          <NavBar />
        </nav>
        <UserProfile accountInfo={accountInfo} />
      </div>
    </header>
  );
};

export default Header;
