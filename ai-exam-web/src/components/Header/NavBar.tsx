'use client'

import { usePathname } from "next/navigation"
import NavItem from "./NavItem"

export default function Nav() {
  const pathname = usePathname()

  const isActive = (paths: string[]) => {
    return paths.some(path => {
      if (path === '/') {
        return pathname === path
      }
      return pathname.startsWith(path.replace('.', ''))
    })
  }

  const navItems = [
    {
      label: "首页",
      paths: ["/home"],
    },
    {
      label: "课程",
      paths: ["/course-list", "/course-detail", "/course-purchase", "/course-learning"]
    },
    {
      label: "题库",
      paths: ["/question-bank", "/question-detail", "/question-purchase"],
    },
    // {
    //   label: "资料库（二期）",
    //   paths: []
    // },
    {
      label: "AI智能学习中心",
      paths: ["/study-hub", "/exercise-chapter", "/real-exam", "/real-practice", "/ai-learning-diagnosis", "ai-practice", "/chapter-real"],
    },
    {
      label: "用户中心",
      paths: ["/user-center"],
    },
  ];

  return (
    <div className="flex items-center gap-10">
      {navItems.map((item, index) => (
        <NavItem
          key={index}
          label={item.label}
          path={item.paths[0]} // 使用第一个路径作为链接
          isActive={isActive(item.paths)}
        />
      ))}
    </div>
  )
}