/**
 * This code was generated by Builder.io.
 */
import Link from 'next/link';
import Image from "next/image";
import React from "react";

interface LinkItem {
  text: string;
  isHighlighted: boolean;
}

interface FooterColumnProps {
  title: string;
  links: LinkItem[];
}

const FooterColumn: React.FC<FooterColumnProps> = ({ title, links }) => {
  return (
    <div className="flex flex-col w-6/12 max-md:ml-0 max-md:w-full">
      <div className="flex flex-col grow items-start text-base text-center text-white whitespace-nowrap max-md:mt-10">
        <h6 className="font-medium">
          {title === "关于我们" ? (
            <Link href="/about-us" className="hover:text-lime-500 hover:underline">
              {title}
            </Link>
          ) : (
            title
          )}
        </h6>
        <img
          src="https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/footer-cover.png?placeholderIfAbsent=true&apiKey=1768a45516594f91af08d1bb32140cef"
          alt=""
          className="object-contain self-stretch mt-4 w-full aspect-[83.33]"
        />
        <nav className="mt-3">
          <ul className="flex flex-col gap-3">
            {links.map((link, index) => (
              <li
                key={index}
                className="text-xs hover:text-lime-500 text-left"
              >
                <Link 
                  href={
                    link.text === "用户协议" 
                      ? "/agreement" 
                      : link.text === "用户隐私政策" 
                        ? "/privacy" 
                        : link.text === "常见问题"
                          ? "/faq"
                          : "#"
                  } 
                  className="hover:underline"
                  target={
                    link.text === "用户协议" || link.text === "用户隐私政策" || link.text === "常见问题" 
                      ? "_blank" 
                      : "_self"
                  }
                >
                  {link.text}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default FooterColumn;
