import React from 'react';

interface IconComponentProps {
  isHovered: boolean;
  defaultIcon?: string;
  hoverIcon?: string;
  className?: string;
}

const IconComponent: React.FC<IconComponentProps> = ({ 
  isHovered,
  defaultIcon = "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/default-icon.png",
  hoverIcon = "https://ai-exam-sh.oss-cn-shanghai.aliyuncs.com/ai-exam-web/images/hover-icon.png",
  className = '' 
}) => {
  return (
    <img
      src={isHovered ? hoverIcon : defaultIcon}
      alt=""
      className={`transition-all duration-300 ${className}`}
    />
  );
};

export default IconComponent;