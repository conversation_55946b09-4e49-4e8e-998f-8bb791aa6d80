'use client';

import { useEffect } from 'react';
import Script from 'next/script';

interface WechatPayModalProps {
  qrCodeUrl: string;
  amount: number;
  onClose: () => void;
}

const WechatPayModal = ({ qrCodeUrl, amount, onClose }: WechatPayModalProps) => {
  useEffect(() => {
    // 当 qrCodeUrl 变化时重新生成二维码
    const generateQRCode = () => {
      const qrcodeElement = document.getElementById("wechat-qrcode");
      if (qrcodeElement && window.QRCode && qrCodeUrl) {
        qrcodeElement.innerHTML = ''; // 清除之前的二维码
        new window.QRCode(qrcodeElement, {
          text: qrCodeUrl,
          width: 256,
          height: 256,
          colorDark: "#000000",
          colorLight: "#ffffff",
          correctLevel: window.QRCode.CorrectLevel.H
        });
      }
    };

    // 如果 QRCode 库已加载，直接生成
    if (window.QRCode) {
      generateQRCode();
    }
  }, [qrCodeUrl]); // 依赖于 qrCodeUrl

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-4 rounded-lg w-[800px] h-[600px] relative">
        <button 
          onClick={(e) => {
            e.preventDefault();
            onClose();
          }}
          className="absolute top-2 right-2 p-2 text-gray-500 hover:text-gray-700 cursor-pointer z-10"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <div className="w-full h-full flex flex-col items-center justify-center">
          <Script 
            src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"
            onLoad={() => {
              const qrcodeElement = document.getElementById("wechat-qrcode");
              if (qrcodeElement && qrCodeUrl) {
                qrcodeElement.innerHTML = ''; // 清除之前的二维码
                new (window as any).QRCode(qrcodeElement, {
                  text: qrCodeUrl,
                  width: 256,
                  height: 256,
                  colorDark: "#000000",
                  colorLight: "#ffffff",
                  correctLevel: (window as any).QRCode.CorrectLevel.H
                });
              }
            }}
          />
          <h2 className="text-xl font-bold mb-6">微信扫码支付</h2>
          <div id="wechat-qrcode" className="w-64 h-64 mb-4"></div>
          <p className="text-gray-600">请使用微信扫描二维码完成支付</p>
          <p className="text-gray-400 text-sm mt-2">
            <span className="text-[#ff6634] text-3xl font-bold">¥{amount}</span>
          </p>
        </div>
      </div>
    </div>
  );
};

// 为了 TypeScript 支持
declare global {
  interface Window {
    QRCode: any;
  }
}

export default WechatPayModal; 