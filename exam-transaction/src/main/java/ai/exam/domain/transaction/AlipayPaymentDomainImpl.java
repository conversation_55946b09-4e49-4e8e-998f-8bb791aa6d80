package ai.exam.domain.transaction;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Component
public class AlipayPaymentDomainImpl implements PaymentDomain {
    private static final String APP_ID = "2021005109681019";
    private static final String APP_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC/49RFwM83QAPYWKlA1Sl6htdfrfLiRZX64dBJV16koi7gv49GfPJggg+hWr/Q5pfykqlwMRukOirQKX8ttXmOf+/cLY0M5TpUBUpBfG0VVaD8e54XbotNbGa5oGPSXgegMS8MB8td2DTgt2AGbnd6Dz//AfLnkkT3FJ29SoUEyWn1hTl/JtkVry/C9oE17gzoLC5KfxLaONhKg5wsRYUigj9tUU0sLTv+Z+KQlfLS3n+Q2DTUA3BRHvaYPhi6Qa69CGjUMuJHLYr3GxuYCSaGfKGevlWYAHRpdhgSzirE2nTzyijg/s1BzXLnWrs8rUsBKUD9MM3daVwIk4cpDRUvAgMBAAECggEAe9aV7PpVAMwgbyQw/vVSko+pOUdyo5LTCxlmqZ5oJwnABS27UntalOv7GGAPwMrArzz1esFGnIMplDPjruUacNuiOc9v4y2gqg7L8F1TrNwZTDifO3INFsdpuEyJf39NnpV/Tl1NYuAgqBHQN/ZcxvpGi9V/JPHh7i9w6XPJSt9sqkAbeYE6mLaUWxUdlh4T1JJh+i3FBQeR2Ut88RcCwbsPf3CAeh7SME+W+3ZE4MIgg03tln/Pr3sKQmUs8puwjeJdYjXoJydL7IoY1rRY4docqSX7+VYRGGF8bR4u4ZEMZOgr2X+NudtbCXMmlStThfWIxkJjiyOpjwxMx5FLoQKBgQD/1kEcKLvB7GVA6QMvUHOWfjRs2yd+OwyHJbUHa62cJ2DVk+K/tVGUUFIsSTgDyANETkZHB1aYILxubqZWS2sADXyW3KvWoJsbKRUUOY0cpSVSL4q4Wkpc0GNaL+2PbS3OX8Z3xkGniLFOzx4+j3FIDb0KKOwQohosHdYeTo7QfwKBgQDAAyPzu8XjBHT3rBeYjGcHDtrWolywFxqyvqpJmSLPFsfNLwLmOtNGskfbaXRQjsT5VlLOQZTFjQeRKpIaq7/WoVdGXQYeyf1mZ+hsMMXx4tLyOhXGB0dlt76x3KOuGfSIeOH3rmSEFSQgC+yEqhNQmwjP1+MV8XoYxejvjsRjUQKBgDlLbVd2E95xJ5fq1yjAdrJsXydFyUluxfZ6Tll0FvRSqVALh4txeuyhNFXwq9fE8l5YvUpo7U6yfiCpLR1EVJk1bAKJNv6hBzuE7tCRtvWEUqBeP03cAq1YifeHpkcpp0mBj3wBbXCsnzDMF7ThAPinAwAEfCGZfA0HPY2aAEfXAoGBAJ3A9sTFPSvq+X6S8T/v+qWd2sYE10oFcXXxmE6ndiXWfYliWmt1YSfwSYFG/r1Azyxbc+TplKVJ3TGzXB51hUN9gwrYWrLpGTwNmrNozviQdud0OXdde5dig1X2BLSBtpp5Vo37xbGO/QMnSMcmRIAxH+hDYtsgbkP0NhC7pzZBAoGBAKnz8aXOiRNBLcY6vG3yeU5qpcpZVnzJSyT1PLQwDaIov9iecQ7vL4hoqcsD+mcBKqFMeKHZXP+rI6Ko1bMoiPd8epAqVaVDJEZoYzuP8k8NfRKk5gJPvUDLcoM/7KHtCpD3M8XcvaaPZm5WmoFZdFJN++KVT2IHz++vpj5J72/J";
    private static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3s0d7LW9tQIW/K8zSdqarGB533zP+fKfwPcIVjD9uSleaKZXqm3hsNd+LvWxSIIztbMXvo7F0tf6sXelcmqZ0jvy/VDZo8dAxnXPYB+IiEafXQ9daz4owZ7GOVkvKjI7n4sBmEtoFzta2xlUL0Mc8j5FncCj9pSuV5wur5FrCj3Xur2SScGCIHwl7BUK7GLRgKVlmy2tqTx9PPHVzi5CpAW8gZMj0d6HID/CqqF1Si4ryTvCCnPjDhFnbnVFwD1aZHX81tcFWp1HRQGkXVOWjLe4Xv8ry/mG6q2TYkWo/onQvl9LeOhl3poICPShwFmc4iU9J+8k6NsPPNiWfnZy9wIDAQAB";
    private static AlipayClient ALIPAY_CLIENT;

    @SneakyThrows
    @PostConstruct
    private void init() {
        ALIPAY_CLIENT = new DefaultAlipayClient(getAlipayConfig());
    }

    @Override
    @SneakyThrows
    public String createPayment(OrderDO orderDO) {
        AlipayTradePagePayRequest request = assemblePagePayRequest(orderDO);

        AlipayTradePagePayResponse response = ALIPAY_CLIENT.pageExecute(request, "POST");

        return response.getBody();
    }

    @Override
    @SneakyThrows
    public OrderStatusEnum queryPaymentStatus(String orderNo) {
        AlipayTradeQueryRequest request = assembleQueryRequest(orderNo);

        AlipayTradeQueryResponse response = ALIPAY_CLIENT.execute(request);

        String body = response.getBody();

        TradeQueryResponseBody tradeQueryResponseBody = JSON.parseObject(body, TradeQueryResponseBody.class);
        String tradeStatus = tradeQueryResponseBody.getAlipay_trade_query_response().getTrade_status();
        return AlipayTradeStatusEnum.getOrderStatusByTradeStatus(tradeStatus);
    }

    @Override
    public void handleNotify(String notifyData) {
        // Implement Alipay notify handling logic
    }

    @NotNull
    private static AlipayTradeQueryRequest assembleQueryRequest(String orderNo) {
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();

        AlipayTradeQueryModel model = assembleQueryModel(orderNo);

        request.setBizModel(model);

        return request;
    }

    @NotNull
    private static AlipayTradePagePayRequest assemblePagePayRequest(OrderDO orderDO) {
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
//        request.setReturnUrl("http://your-domain.com/return_url");
//        request.setNotifyUrl("http://your-domain.com/notify_url");

        AlipayTradePagePayModel model = assemblePagePayModel(orderDO);

        request.setBizModel(model);

        return request;
    }

    @NotNull
    private static AlipayTradeQueryModel assembleQueryModel(String orderNo) {
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();

        // 设置订单支付时传入的商户订单号
        model.setOutTradeNo(orderNo);

        // 设置支付宝交易号
//        model.setTradeNo("2014112611001004680 073956707");

        // 设置查询选项
        List<String> queryOptions = new ArrayList<>();
        queryOptions.add("trade_settle_info");
        model.setQueryOptions(queryOptions);
        return model;
    }

    @NotNull
    private static AlipayTradePagePayModel assemblePagePayModel(OrderDO orderDO) {
        AlipayTradePagePayModel model = new AlipayTradePagePayModel();

        // 设置商户订单号
        model.setOutTradeNo(orderDO.getOrderNo());

        // 设置订单总金额
        model.setTotalAmount(orderDO.getPaymentAmount().toString());

        // 设置订单标题
        model.setSubject("乐象订单：" + orderDO.getOrderNo());

        // 设置产品码
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        // 设置PC扫码支付的方式
        model.setQrPayMode("1");

        // 设置商户自定义二维码宽度
        model.setQrcodeWidth(100L);

        // 设置订单绝对超时时间
        model.setTimeExpire(orderDO.getExpirationTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 设置订单包含的商品列表信息
//        List<GoodsDetail> goodsDetail = new ArrayList<>();
//        GoodsDetail goodsDetail0 = new GoodsDetail();
//        goodsDetail0.setGoodsName("ipad");
//        goodsDetail0.setAlipayGoodsId("20010001");
//        goodsDetail0.setQuantity(1L);
//        goodsDetail0.setPrice("2000");
//        goodsDetail0.setGoodsId("apple-01");
//        goodsDetail0.setGoodsCategory("34543238");
//        goodsDetail0.setCategoriesTree("124868003|126232002|126252004");
//        goodsDetail0.setShowUrl("http://www.alipay.com/xxx.jpg");
//        goodsDetail.add(goodsDetail0);
//        model.setGoodsDetail(goodsDetail);

        // 设置二级商户信息
//        SubMerchant subMerchant = new SubMerchant();
//        subMerchant.setMerchantId("2088000603999128");
//        subMerchant.setMerchantType("alipay");
//        model.setSubMerchant(subMerchant);

        // 设置业务扩展参数
//        ExtendParams extendParams = new ExtendParams();
//        extendParams.setSysServiceProviderId("2088511833207846");
//        extendParams.setHbFqSellerPercent("100");
//        extendParams.setHbFqNum("3");
//        extendParams.setIndustryRefluxInfo("{\"scene_code\":\"metro_tradeorder\",\"channel\":\"xxxx\",\"scene_data\":{\"asset_name\":\"ALIPAY\"}}");
//        extendParams.setSpecifiedSellerName("XXX的跨境小铺");
////        extendParams.setRoyaltyFreeze("true");
//        extendParams.setCardType("S0JP0000");
//        model.setExtendParams(extendParams);

        // 设置商户传入业务信息
//        model.setBusinessParams("{\"mc_create_trade_ip\":\"127.0.0.1\"}");

        // 设置优惠参数
//        model.setPromoParams("{\"storeIdType\":\"1\"}");

        // 设置请求后页面的集成方式
//        model.setIntegrationType("PCWEB");

        // 设置请求来源地址
//        model.setRequestFromUrl("https://");

        // 设置商户门店编号
//        model.setStoreId("NJ_001");

        // 设置商户的原始订单号
//        model.setMerchantOrderNo("***********");

        // 设置外部指定买家
//        ExtUserInfo extUserInfo = new ExtUserInfo();
//        extUserInfo.setCertType("IDENTITY_CARD");
//        extUserInfo.setCertNo("362334768769238881");
//        extUserInfo.setName("李明");
//        extUserInfo.setMobile("***********");
//        extUserInfo.setMinAge("18");
//        extUserInfo.setNeedCheckInfo("F");
////        extUserInfo.setIdentityHash("27bfcd1dee4f22c8fe8a2374af9b660419d1361b1c207e9b41a754a113f38fcc");
//        model.setExtUserInfo(extUserInfo);

        // 设置开票信息
//        InvoiceInfo invoiceInfo = new InvoiceInfo();
//        InvoiceKeyInfo keyInfo = new InvoiceKeyInfo();
//        keyInfo.setTaxNum("1464888883494");
//        keyInfo.setIsSupportInvoice(true);
//        keyInfo.setInvoiceMerchantName("ABC|003");
//        invoiceInfo.setKeyInfo(keyInfo);
//        invoiceInfo.setDetails("[{\"code\":\"*********\",\"name\":\"服饰\",\"num\":\"2\",\"sumPrice\":\"200.00\",\"taxRate\":\"6%\"}]");
//        model.setInvoiceInfo(invoiceInfo);

        return model;
    }

    private static AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId(APP_ID);
        alipayConfig.setPrivateKey(APP_PRIVATE_KEY);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }

    @Data
    private static class TradeQueryResponseBody {
        private SubAlipayTradeQueryResponse alipay_trade_query_response;
    }

    @Data
    protected static class SubAlipayTradeQueryResponse {
        private String trade_status;
    }

    @Getter
    @AllArgsConstructor
    private enum AlipayTradeStatusEnum {
        WAIT_BUYER_PAY("（交易创建，等待买家付款）", OrderStatusEnum.PENDING_PAYMENT),
        TRADE_CLOSED("（未付款交易超时关闭，或支付完成后全额退款）", OrderStatusEnum.CANCELED),
        TRADE_SUCCESS("（交易支付成功）", OrderStatusEnum.PAYMENT_SUCCESS),
        TRADE_FINISHED("（交易结束，不可退款）", OrderStatusEnum.PAYMENT_SUCCESS),
        ;
        private final String desc;
        private final OrderStatusEnum orderStatus;

        private static OrderStatusEnum getOrderStatusByTradeStatus(String tradeStatus) {
            if (StringUtils.isBlank(tradeStatus)) {
                return OrderStatusEnum.PENDING_PAYMENT;
            }
            return AlipayTradeStatusEnum.valueOf(tradeStatus).getOrderStatus();
        }
    }
}
