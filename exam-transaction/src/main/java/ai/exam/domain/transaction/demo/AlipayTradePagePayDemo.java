package ai.exam.domain.transaction.demo;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;

public class AlipayTradePagePayDemo {
    private static final String APP_ID = "2021005109681019";
    private static final String APP_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC/49RFwM83QAPYWKlA1Sl6htdfrfLiRZX64dBJV16koi7gv49GfPJggg+hWr/Q5pfykqlwMRukOirQKX8ttXmOf+/cLY0M5TpUBUpBfG0VVaD8e54XbotNbGa5oGPSXgegMS8MB8td2DTgt2AGbnd6Dz//AfLnkkT3FJ29SoUEyWn1hTl/JtkVry/C9oE17gzoLC5KfxLaONhKg5wsRYUigj9tUU0sLTv+Z+KQlfLS3n+Q2DTUA3BRHvaYPhi6Qa69CGjUMuJHLYr3GxuYCSaGfKGevlWYAHRpdhgSzirE2nTzyijg/s1BzXLnWrs8rUsBKUD9MM3daVwIk4cpDRUvAgMBAAECggEAe9aV7PpVAMwgbyQw/vVSko+pOUdyo5LTCxlmqZ5oJwnABS27UntalOv7GGAPwMrArzz1esFGnIMplDPjruUacNuiOc9v4y2gqg7L8F1TrNwZTDifO3INFsdpuEyJf39NnpV/Tl1NYuAgqBHQN/ZcxvpGi9V/JPHh7i9w6XPJSt9sqkAbeYE6mLaUWxUdlh4T1JJh+i3FBQeR2Ut88RcCwbsPf3CAeh7SME+W+3ZE4MIgg03tln/Pr3sKQmUs8puwjeJdYjXoJydL7IoY1rRY4docqSX7+VYRGGF8bR4u4ZEMZOgr2X+NudtbCXMmlStThfWIxkJjiyOpjwxMx5FLoQKBgQD/1kEcKLvB7GVA6QMvUHOWfjRs2yd+OwyHJbUHa62cJ2DVk+K/tVGUUFIsSTgDyANETkZHB1aYILxubqZWS2sADXyW3KvWoJsbKRUUOY0cpSVSL4q4Wkpc0GNaL+2PbS3OX8Z3xkGniLFOzx4+j3FIDb0KKOwQohosHdYeTo7QfwKBgQDAAyPzu8XjBHT3rBeYjGcHDtrWolywFxqyvqpJmSLPFsfNLwLmOtNGskfbaXRQjsT5VlLOQZTFjQeRKpIaq7/WoVdGXQYeyf1mZ+hsMMXx4tLyOhXGB0dlt76x3KOuGfSIeOH3rmSEFSQgC+yEqhNQmwjP1+MV8XoYxejvjsRjUQKBgDlLbVd2E95xJ5fq1yjAdrJsXydFyUluxfZ6Tll0FvRSqVALh4txeuyhNFXwq9fE8l5YvUpo7U6yfiCpLR1EVJk1bAKJNv6hBzuE7tCRtvWEUqBeP03cAq1YifeHpkcpp0mBj3wBbXCsnzDMF7ThAPinAwAEfCGZfA0HPY2aAEfXAoGBAJ3A9sTFPSvq+X6S8T/v+qWd2sYE10oFcXXxmE6ndiXWfYliWmt1YSfwSYFG/r1Azyxbc+TplKVJ3TGzXB51hUN9gwrYWrLpGTwNmrNozviQdud0OXdde5dig1X2BLSBtpp5Vo37xbGO/QMnSMcmRIAxH+hDYtsgbkP0NhC7pzZBAoGBAKnz8aXOiRNBLcY6vG3yeU5qpcpZVnzJSyT1PLQwDaIov9iecQ7vL4hoqcsD+mcBKqFMeKHZXP+rI6Ko1bMoiPd8epAqVaVDJEZoYzuP8k8NfRKk5gJPvUDLcoM/7KHtCpD3M8XcvaaPZm5WmoFZdFJN++KVT2IHz++vpj5J72/J";
    private static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3s0d7LW9tQIW/K8zSdqarGB533zP+fKfwPcIVjD9uSleaKZXqm3hsNd+LvWxSIIztbMXvo7F0tf6sXelcmqZ0jvy/VDZo8dAxnXPYB+IiEafXQ9daz4owZ7GOVkvKjI7n4sBmEtoFzta2xlUL0Mc8j5FncCj9pSuV5wur5FrCj3Xur2SScGCIHwl7BUK7GLRgKVlmy2tqTx9PPHVzi5CpAW8gZMj0d6HID/CqqF1Si4ryTvCCnPjDhFnbnVFwD1aZHX81tcFWp1HRQGkXVOWjLe4Xv8ry/mG6q2TYkWo/onQvl9LeOhl3poICPShwFmc4iU9J+8k6NsPPNiWfnZy9wIDAQAB";

    public static void main(String[] args) throws AlipayApiException {
        pay();
    }

    public static String pay() throws AlipayApiException {
        // 初始化SDK
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        AlipayTradePagePayModel model = new AlipayTradePagePayModel();

        // 设置商户订单号
        model.setOutTradeNo("20150320010101003");

        // 设置订单总金额
        model.setTotalAmount("0.01");

        // 设置订单标题
        model.setSubject("测试商品");

        // 设置产品码
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        // 设置PC扫码支付的方式
        model.setQrPayMode("4");

        // 设置商户自定义二维码宽度
        model.setQrcodeWidth(100L);

        // 设置订单包含的商品列表信息
//        List<GoodsDetail> goodsDetail = new ArrayList<>();
//        GoodsDetail goodsDetail0 = new GoodsDetail();
//        goodsDetail0.setGoodsName("ipad");
//        goodsDetail0.setAlipayGoodsId("20010001");
//        goodsDetail0.setQuantity(1L);
//        goodsDetail0.setPrice("2000");
//        goodsDetail0.setGoodsId("apple-01");
//        goodsDetail0.setGoodsCategory("34543238");
//        goodsDetail0.setCategoriesTree("124868003|126232002|126252004");
//        goodsDetail0.setShowUrl("http://www.alipay.com/xxx.jpg");
//        goodsDetail.add(goodsDetail0);
//        model.setGoodsDetail(goodsDetail);

        // 设置订单绝对超时时间
        model.setTimeExpire("2024-12-31 00:00:00");

        // 设置二级商户信息
//        SubMerchant subMerchant = new SubMerchant();
//        subMerchant.setMerchantId("2088000603999128");
//        subMerchant.setMerchantType("alipay");
//        model.setSubMerchant(subMerchant);

        // 设置业务扩展参数
//        ExtendParams extendParams = new ExtendParams();
//        extendParams.setSysServiceProviderId("2088511833207846");
//        extendParams.setHbFqSellerPercent("100");
//        extendParams.setHbFqNum("3");
//        extendParams.setIndustryRefluxInfo("{\"scene_code\":\"metro_tradeorder\",\"channel\":\"xxxx\",\"scene_data\":{\"asset_name\":\"ALIPAY\"}}");
//        extendParams.setSpecifiedSellerName("XXX的跨境小铺");
////        extendParams.setRoyaltyFreeze("true");
//        extendParams.setCardType("S0JP0000");
//        model.setExtendParams(extendParams);

        // 设置商户传入业务信息
//        model.setBusinessParams("{\"mc_create_trade_ip\":\"127.0.0.1\"}");

        // 设置优惠参数
//        model.setPromoParams("{\"storeIdType\":\"1\"}");

        // 设置请求后页面的集成方式
//        model.setIntegrationType("PCWEB");

        // 设置请求来源地址
//        model.setRequestFromUrl("https://");

        // 设置商户门店编号
//        model.setStoreId("NJ_001");

        // 设置商户的原始订单号
//        model.setMerchantOrderNo("***********");

        // 设置外部指定买家
//        ExtUserInfo extUserInfo = new ExtUserInfo();
//        extUserInfo.setCertType("IDENTITY_CARD");
//        extUserInfo.setCertNo("362334768769238881");
//        extUserInfo.setName("李明");
//        extUserInfo.setMobile("***********");
//        extUserInfo.setMinAge("18");
//        extUserInfo.setNeedCheckInfo("F");
////        extUserInfo.setIdentityHash("27bfcd1dee4f22c8fe8a2374af9b660419d1361b1c207e9b41a754a113f38fcc");
//        model.setExtUserInfo(extUserInfo);

        // 设置开票信息
//        InvoiceInfo invoiceInfo = new InvoiceInfo();
//        InvoiceKeyInfo keyInfo = new InvoiceKeyInfo();
//        keyInfo.setTaxNum("1464888883494");
//        keyInfo.setIsSupportInvoice(true);
//        keyInfo.setInvoiceMerchantName("ABC|003");
//        invoiceInfo.setKeyInfo(keyInfo);
//        invoiceInfo.setDetails("[{\"code\":\"*********\",\"name\":\"服饰\",\"num\":\"2\",\"sumPrice\":\"200.00\",\"taxRate\":\"6%\"}]");
//        model.setInvoiceInfo(invoiceInfo);

        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token
        // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

//        AlipayTradePagePayResponse response = alipayClient.pageExecute(request, "POST");
        // 如果需要返回GET请求，请使用
         AlipayTradePagePayResponse response = alipayClient.pageExecute(request, "GET");
        String pageRedirectionData = response.getBody();
        System.out.println(pageRedirectionData);

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }

        return pageRedirectionData;
    }

    private static AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
        alipayConfig.setAppId(APP_ID);
        alipayConfig.setPrivateKey(APP_PRIVATE_KEY);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
}
