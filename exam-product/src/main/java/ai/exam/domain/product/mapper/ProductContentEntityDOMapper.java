package ai.exam.domain.product.mapper;

import ai.exam.domain.product.ProductContentDO;
import ai.exam.domain.product.po.ProductContentEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface ProductContentEntityDOMapper {

    /**
     * 将 ProductContentDO 转换为 ProductContentEntity
     */
    ProductContentEntity toProductContentEntity(ProductContentDO productContentDO);

    /**
     * 将 ProductContentEntity 转换为 ProductContentDO
     */
    ProductContentDO toProductContentDO(ProductContentEntity entity);

    /**
     * 将 ProductContentEntity 列表转换为 ProductContentDO 列表
     */
    List<ProductContentDO> toProductContentDOList(List<ProductContentEntity> entities);

    /**
     * 将 ProductContentDO 列表转换为 ProductContentEntity 列表
     */
    List<ProductContentEntity> toProductContentEntityList(List<ProductContentDO> productContentDOs);
}