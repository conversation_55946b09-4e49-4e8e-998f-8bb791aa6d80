package ai.exam.domain.studyrecord.mapper;

import ai.exam.domain.studyrecord.StudyTrackerDO;
import ai.exam.domain.studyrecord.po.StudyTrackerEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface StudyTrackerEntityDOMapper {
    StudyTrackerDO toStudyTrackerDO(StudyTrackerEntity entity);

    StudyTrackerEntity toStudyTrackerEntity(StudyTrackerDO studyTrackerDO);

    List<StudyTrackerDO> toStudyTrackerDO(List<StudyTrackerEntity> entities);
}