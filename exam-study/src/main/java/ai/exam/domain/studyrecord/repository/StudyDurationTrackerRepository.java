package ai.exam.domain.studyrecord.repository;

import ai.exam.domain.studyrecord.po.StudyDurationTrackerEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface StudyDurationTrackerRepository extends JpaRepository<StudyDurationTrackerEntity, Long> {

    /**
     * 根据用户ID查询未删除的学习时长记录
     * @param userId 用户ID
     * @param delFlag 删除标记
     * @return 学习时长记录
     */
    Optional<StudyDurationTrackerEntity> findByUserIdAndDelFlag(Long userId, Boolean delFlag);

    /**
     * 增加用户学习时长
     * @param userId 用户ID
     * @param minutes 增加的分钟数
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE StudyDurationTrackerEntity e SET e.duration = e.duration + :minutes WHERE e.userId = :userId AND e.delFlag = false")
    int incrementDuration(Long userId, Integer minutes);
}