package ai.exam.domain.studyrecord.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(
        name = "t_user_study_tracker",
        indexes = {
                @Index(
                        name = "idx_userId_studyType_createTime",
                        columnList = "userId,studyType,createTime"
                )
        }
)
@SuperBuilder
@NoArgsConstructor
public class StudyTrackerEntity extends BaseEntity {

    @Column(name = "delFlag", nullable = false)
    private Boolean delFlag = false;

    @Column(name = "userId", nullable = false)
    private Long userId;

    @Column(name = "studyType", nullable = false, length = 50)
    private String studyType;

    @Column(name = "subStudyType", nullable = false, length = 50)
    private String subStudyType = "";

    @Column(name = "studyContentId", nullable = false, length = 50)
    private String studyContentId;
}