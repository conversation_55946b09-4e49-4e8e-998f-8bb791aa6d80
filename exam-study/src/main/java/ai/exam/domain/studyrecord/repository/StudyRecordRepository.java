package ai.exam.domain.studyrecord.repository;

import ai.exam.domain.studyrecord.po.StudyRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StudyRecordRepository extends JpaRepository<StudyRecordEntity, Long> {
    Optional<StudyRecordEntity> findByAccountIdAndKnowledgeSliceIdAndDelFlag(Long accountId, Long knowledgeSliceId, Integer delFlag);

    List<StudyRecordEntity> findByAccountIdAndKnowledgeSliceIdInAndDelFlag(Long accountId, List<Long> knowledgeSliceIds, Integer delFlag);
}