package ai.exam.domain.exercise.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_wrong_question_record")
@SuperBuilder
@NoArgsConstructor
public class WrongQuestionRecordEntity extends BaseEntity {

    @Column(columnDefinition = "varchar(50) COMMENT '节编号'")
    private String sectionCode;

    @Column(columnDefinition = "varchar(50) COMMENT '章编号'")
    private String chapterCode;

    @Column(columnDefinition = "varchar(50) COMMENT '科目编号'")
    private String subjectCode;

    @Column(columnDefinition = "varchar(50) COMMENT '练习模式'")
    private String exerciseMode;

    @Column(columnDefinition = "bigint(20) COMMENT '题目ID'")
    private Long questionId;

    @Column(columnDefinition = "bigint(20) COMMENT '题库ID'")
    private Long questionSetId;

    @Column(columnDefinition = "varchar(255) COMMENT '用户提交的答案'")
    private String userAnswer;

    @Column(columnDefinition = "bigint(20) COMMENT '账号 ID'")
    private Long accountId;

    @Column(columnDefinition = "datetime COMMENT '收集时间'")
    private LocalDateTime collectedAt;

    @Column(columnDefinition = "int(11)")
    private Integer createBy;

    @Column(columnDefinition = "int(11)")
    private Integer updateBy;

    @Column(columnDefinition = "longtext COMMENT '备注'")
    private String remark;

    @Column(columnDefinition = "int(1) DEFAULT '0' COMMENT '删除标识 0存在 其他删除'")
    private Integer delFlag;
}