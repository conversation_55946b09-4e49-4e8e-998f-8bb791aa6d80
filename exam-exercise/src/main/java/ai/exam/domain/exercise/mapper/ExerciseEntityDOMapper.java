package ai.exam.domain.exercise.mapper;

import ai.exam.domain.exercise.ExerciseDO;
import ai.exam.domain.exercise.po.ExerciseEntity;
import ai.exam.domain.exercise.po.WrongQuestionRecordEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface ExerciseEntityDOMapper {
    ExerciseDO toExerciseDO(ExerciseEntity entity);

    ExerciseEntity toExerciseEntity(ExerciseDO exerciseDO);

    List<ExerciseDO> toExerciseDOList(List<ExerciseEntity> entities);

    List<ExerciseEntity> toExerciseEntityList(List<ExerciseDO> exerciseDOS);

    List<WrongQuestionRecordEntity>  toWrongQuestionRecordEntityList(List<ExerciseDO> exerciseDOS);
}