package ai.exam.domain.exercise.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_user_subject_preference")
@SuperBuilder
@NoArgsConstructor
public class UserSubjectPreferenceEntity extends BaseEntity {

    @Column(columnDefinition = "bigint(20) COMMENT '账户ID'")
    private Long accountId;

    @Column(columnDefinition = "varchar(50) COMMENT '科目编号'")
    private String subjectCode;

    @Column(columnDefinition = "bit(1) COMMENT '答对后是否自动移除错题'")
    private Boolean autoRemoveWrongQuestion;
}