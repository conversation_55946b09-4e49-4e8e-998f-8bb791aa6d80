package ai.exam.domain.exercise.mapper;

import ai.exam.domain.exercise.WrongQuestionRecordDO;
import ai.exam.domain.exercise.po.WrongQuestionRecordEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface WrongQuestionRecordEntityDOMapper {

    @Mapping(source = "updateTime", target = "updatedAt")
    @Mapping(source = "createTime", target = "createdAt")
    WrongQuestionRecordDO toWrongQuestionRecordDO(WrongQuestionRecordEntity entity);

    @Mapping(source = "updatedAt", target = "updateTime")
    @Mapping(source = "createdAt", target = "createTime")
    WrongQuestionRecordEntity toWrongQuestionRecordEntity(WrongQuestionRecordDO recordDO);
}