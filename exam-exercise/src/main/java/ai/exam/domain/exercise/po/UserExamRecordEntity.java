package ai.exam.domain.exercise.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.function.BiConsumer;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_user_exam_record")
@SuperBuilder
@NoArgsConstructor
public class UserExamRecordEntity extends BaseEntity {

    @Column(columnDefinition = "bigint(20) COMMENT '账号id'")
    private Long accountId;

    @Column(columnDefinition = "bigint(20) COMMENT '题集id'")
    private Long questionSetId;

    @Column(columnDefinition = "decimal(10,2) COMMENT '总分数'")
    private BigDecimal totalScore;

    @Column(columnDefinition = "decimal(10,2) COMMENT '得分'")
    private BigDecimal score;

    @Column(columnDefinition = "int(11) COMMENT '错题数'")
    private Integer wrongCount;

    @Column(columnDefinition = "int(11) COMMENT '总题数'")
    private Integer totalCount;

    @Column(columnDefinition = "datetime COMMENT '考试开始时间'")
    private LocalDateTime examStartTime;

    @Column(columnDefinition = "datetime COMMENT '考试结束时间'")
    private LocalDateTime examEndTime;
}