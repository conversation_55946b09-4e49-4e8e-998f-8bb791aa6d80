package ai.exam.domain.exercise.mapper;

import ai.exam.domain.exercise.LocationInformationDO;
import ai.exam.domain.exercise.po.LocationInformationEntity;
import org.mapstruct.Mapper;

@Mapper
public interface LocationInformationEntityDOMapper {
    LocationInformationDO toLocationInformationDO(LocationInformationEntity entity);
    LocationInformationEntity toLocationInformationEntity(LocationInformationDO locationInformationDO);
}