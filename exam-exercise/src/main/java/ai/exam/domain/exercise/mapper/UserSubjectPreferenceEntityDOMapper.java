package ai.exam.domain.exercise.mapper;

import ai.exam.domain.exercise.UserSubjectPreferenceDO;
import ai.exam.domain.exercise.po.UserSubjectPreferenceEntity;
import org.mapstruct.Mapper;

@Mapper
public interface UserSubjectPreferenceEntityDOMapper {
    UserSubjectPreferenceDO toUserSubjectPreferenceDO(UserSubjectPreferenceEntity entity);
    UserSubjectPreferenceEntity toUserSubjectPreferenceEntity(UserSubjectPreferenceDO userSubjectPreferenceDO);
}