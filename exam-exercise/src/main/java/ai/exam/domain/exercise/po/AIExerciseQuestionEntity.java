package ai.exam.domain.exercise.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_ai_exercise_question")
@SuperBuilder
@NoArgsConstructor
public class AIExerciseQuestionEntity extends BaseEntity {

    @Column(columnDefinition = "bigint(20) COMMENT 'AI智练记录ID'")
    private Long aiExerciseRecordId;

    @Column(columnDefinition = "bigint(20) COMMENT '题目ID'")
    private Long questionId;

    @Column(columnDefinition = "bigint(20) COMMENT '题库ID'")
    private Long questionSetId;

    @Column(columnDefinition = "tinyint(1) COMMENT '是否正确'")
    private Boolean isRight;

    @Column(columnDefinition = "varchar(255) COMMENT '用户答案'")
    private String userAnswer;

    @Column(columnDefinition = "varchar(50) COMMENT '批次号'")
    private String batchNum;
}