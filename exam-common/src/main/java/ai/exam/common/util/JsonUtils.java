package ai.exam.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * JSON 工具类
 */
@Component
public class JsonUtils {

	private static ObjectMapper objectMapper;

	@Autowired
	public void setObjectMapper(ObjectMapper objectMapper) {
		JsonUtils.objectMapper = objectMapper;
	}

	/**
	 * JSON 字符串转对象
	 */
	public static <T> T fromJson(String json, Class<T> clazz) {
		if (json == null || json.isEmpty()) {
			return null;
		}
		try {
			return objectMapper.readValue(json, clazz);
		} catch (JsonProcessingException e) {
			throw new RuntimeException("解析 JSON 字符串失败", e);
		}
	}

	/**
	 * JSON 字符串转对象（使用 TypeReference）
	 */
	public static <T> T fromJson(String json, TypeReference<T> typeReference) {
		if (json == null || json.isEmpty()) {
			return null;
		}
		try {
			return objectMapper.readValue(json, typeReference);
		} catch (JsonProcessingException e) {
			throw new RuntimeException("解析 JSON 字符串失败", e);
		}
	}

	/**
	 * 对象转 JSON 字符串
	 */
	public static String toJson(Object object) {
		if (object == null) {
			return null;
		}
		try {
			return objectMapper.writeValueAsString(object);
		} catch (JsonProcessingException e) {
			throw new RuntimeException("转换 JSON 字符串失败", e);
		}
	}
}
