package ai.exam.common.util;

import ai.exam.domain.question.QuestionDO;
import ai.exam.domain.question.enums.QuestionType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/12/11 23:03
 * @description
 */
public class QuestionContentUtils {

    /**
     * 计算内容 Hash 值
     */
    public static String calculateContentHash(QuestionDO questionDO) {
        if (questionDO == null) {
            return null;
        }

        // 处理题目内容：去除年份前缀和空格
        String cleanedContent = normalizeQuestionContent(questionDO.getContent());

        // 处理选项：将选项转为 JSON 后去除空格
        String optionsJson = normalizeOptions(questionDO.getOptions());

        // 计算 content + options 的 MD5
        String contentForHash = cleanedContent + StringUtils.defaultIfBlank(optionsJson, StringUtils.EMPTY);
        String contentHash = DigestUtils.md5DigestAsHex(contentForHash.getBytes(StandardCharsets.UTF_8));

        // 拼接完整的 Hash 值
        return String.format("%s_%s_%s",
                questionDO.getSubjectCode(),
                questionDO.getType().getName(),
                contentHash);
    }

    /**
     * 标准化处理题目内容
     *
     * @param questionContent 题目内容
     * @return 标准化后的题目内容
     */
    public static String normalizeQuestionContent(String questionContent) {
        return normalizeText(removeYearRealExamsPrefix(questionContent));
    }


    /**
     * 去除题目内容中的年份前缀，并移除首尾 p 标签
     */
    public static String removeYearRealExamsPrefix(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }

        // 1. 先移除首尾 p 标签
        String cleanContent = content
                .replaceAll("^\\s*<p>\\s*", "")
                .replaceAll("\\s*</p>\\s*$", "");

        // 2. 再移除年份前缀
        // 匹配格式：(2009年真题) 或 (2009 年真题) 或 （2009年真题）或 （2009 年真题）
        String pattern = "^[(（]\\d{4}\\s*年真题[)）]";
        return cleanContent.replaceFirst(pattern, "");
    }

    /**
     * 标准化处理所有选项
     */
    public static String normalizeOptions(Map<String, String> options) {
        if (options == null) {
            return StringUtils.EMPTY;
        }

        Map<Object, Object> normalizeOptions = options.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> normalizeText(e.getValue())
                ));
        return JsonUtils.toJson(normalizeOptions).replaceAll("\\s+", "");
    }

    /**
     * 标准化处理文本
     * 1. 移除首尾 p 标签
     * 2. 全角空格转半角
     * 3. 全角括号转半角
     * 4. 移除所有空格
     */
    public static String normalizeText(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        return text
                // 移除首尾 p 标签
                .replaceAll("^\\s*<p>\\s*", "")
                .replaceAll("\\s*</p>\\s*$", "")
                // 全角空格转半角
                .replace((char) 12288, ' ')
                // 半角括号转全角（因为是中文，括号使用全角）
                .replace('(', '（')
                .replace(')', '）')
                // 移除所有空格
                .replaceAll("\\s+", "");
    }

    public static void main(String[] args) {
        QuestionDO questionDO = new QuestionDO();
        questionDO.setSubjectCode("EJ-ZYGCGLYSW-GLGC");
        questionDO.setType(QuestionType.CHOICES);
        questionDO.setContent("<p>（2021年真题）根据《物权法》，下列各项财产抵押的，抵押权自登记时设立的有（   ）</p>");

        String jsonStr = "{\"A\": \"<p>交通运输工具</p>\", \"B\": \"<p>建筑物</p>\", \"C\": \"<p>生产设备、原材料</p>\", \"D\": \"<p>正在建造的船舶</p>\", \"E\": \"<p>建设用地使用权</p>\"}";

        Map<String, String> options = JSON.parseObject(jsonStr, new TypeReference<>() {
        });

        questionDO.setOptions(options);
        calculateContentHash(questionDO);
    }
}
