#!/usr/bin/env bash

ENV=test
PROJECT_NAME=exam-bootstrap-$ENV
TARGET_PACKAGE_NAME=/root/ai-exam/exam-bootstrap/target/$PROJECT_NAME.jar

function git_pull() {
    echo "开始拉取代码..."
    git stash
    git pull
    echo "拉取代码完成!"
}

function install() {
  echo "开始编译打包..."
  mvn -DskipTests -Denv=$ENV clean install
  echo "编译打包完成!"
}

function kill_previous_process() {
  echo "开始杀死之前的进程..."
  pid=$(jps -l | grep "$PROJECT_NAME.jar" | awk '{print $1}')

  if [ -z "$pid" ]; then
    echo "之前无进程，无需杀死!"
  else
    echo "之前的进程 id 为 $pid"
    kill "$pid"
  fi

  echo "杀死之前的进程完成!"
}

function run() {
  echo "启动 java 服务中，命令为：java -jar -Dspring.profiles.active=$ENV $TARGET_PACKAGE_NAME >/dev/null 2>&1 &"
  nohup java -jar -Dspring.profiles.active=$ENV $TARGET_PACKAGE_NAME >/dev/null 2>&1 &
}

git_pull
install
# 杀死之前的进程
kill_previous_process
# 启动服务
run

