import { STORAGE_KEYS } from '@/constants/storage';
import { TabInfo } from '@/types/menu-types';
import { CertificateSubjectsVO } from '@/apis/pages/certificate-vo';
import { MgtCertificateSubjectController } from '@/apis/pages/pages-certificate';

// 创建控制器单例
const certificateSubjectController = new MgtCertificateSubjectController();

// 获取 string 类型的 sessionStorage 值
export function getStorageString(key: string): string | null {
  try {
    return sessionStorage.getItem(key) ?? null;
  } catch {
    return null;
  }
}

// 设置 string 类型的 sessionStorage 值
export const setStorageString = (key: string, value: string): void => {
  if (value === null || value === undefined || value === '') {
    sessionStorage.removeItem(key);
    return;
  }
  sessionStorage.setItem(key, value);
};

// 获取数组类型的 sessionStorage 值
export function getStorageArray<T>(key: string): T[] | null {
  try {
    const item = sessionStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch {
    return null;
  }
}

// 设置数组类型的 sessionStorage 值
export const setStorageArray = <T>(key: string, value: T[]): void => {
  if (!value) {
    sessionStorage.removeItem(key);
    return;
  }
  sessionStorage.setItem(key, JSON.stringify(value));
};

// 获取对象类型的 sessionStorage 值
export function getStorageObject<T>(key: string): T | null {
  try {
    const item = sessionStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch {
    return null;
  }
}

// 设置对象类型的 sessionStorage 值
export const setStorageObject = <T>(key: string, value: T): void => {
  if (value === null || value === undefined) {
    sessionStorage.removeItem(key);
    return;
  }
  sessionStorage.setItem(key, JSON.stringify(value));
};

// 证书列表相关的存储管理
export const certificatesStorage = {
  key: STORAGE_KEYS.CERTIFICATES_WITH_SUBJECTS,
  // 添加一个请求 Promise 的缓存
  pendingRequest: null as Promise<CertificateSubjectsVO[]> | null,
  
  async getCertificatesWithSubjects(): Promise<CertificateSubjectsVO[]> {
    try {
      // 先从缓存获取
      const cached = getStorageObject<CertificateSubjectsVO[]>(this.key);
      if (cached) {
        return cached;
      }

      // 如果已经有进行中的请求，直接返回该 Promise
      if (this.pendingRequest) {
        return this.pendingRequest;
      }

      // 创建新的请求
      this.pendingRequest = certificateSubjectController.getCertificatesWithSubjects()
        .then(response => {
          setStorageObject(this.key, response);
          return response;
        })
        .finally(() => {
          // 请求完成后清除 pendingRequest
          this.pendingRequest = null;
        });

      return this.pendingRequest;
    } catch (error) {
      throw error;
    }
  },

  clearCache(): void {
    sessionStorage.removeItem(this.key);
    this.pendingRequest = null; // 清除缓存时也清除进行中的请求
  }
};

// 清除指定页面的 storage
export const clearTabStorage = (tab: TabInfo) => {
  if (tab.name.includes('题目管理')) {
    sessionStorage.removeItem(STORAGE_KEYS.QUESTION.FILTERS);
  }
  if (tab.name.includes('题库管理')) {
    sessionStorage.removeItem(STORAGE_KEYS.QUESTION_SET.CERTIFICATE_CODE);
    sessionStorage.removeItem(STORAGE_KEYS.QUESTION_SET.SUBJECT_CODE);
    sessionStorage.removeItem(STORAGE_KEYS.QUESTION_SET.SEARCH_TEXT);
    sessionStorage.removeItem(STORAGE_KEYS.QUESTION_SET.EXPANDED_KEYS);
  }
  if (tab.name.includes('纠错管理')) {
    sessionStorage.removeItem(STORAGE_KEYS.ERROR_CORRECTION.FILTERS);
  }

  if (tab.name.includes('考试管理')) {
    sessionStorage.removeItem(STORAGE_KEYS.EXAMS.CERTIFICATE_CODE);
    sessionStorage.removeItem(STORAGE_KEYS.EXAMS.SUBJECT_CODE);
  }

  if (tab.name.includes('用户管理')) {
    sessionStorage.removeItem(STORAGE_KEYS.USER.FILTERS);
  }
  if (tab.name.includes('角色管理')) {
    sessionStorage.removeItem(STORAGE_KEYS.ROLE.FILTERS);
  }
};

// 清除所有 storage
export const clearAllStorage = () => {
  sessionStorage.clear();
}; 