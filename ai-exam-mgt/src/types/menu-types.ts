export interface MenuItemData {
    isHidden?: any;
    id: number | null;
    name: string;
    icon: string;
    path?: string;
    component?: string;
    subItems?: MenuItemData[];
}

export interface TabInfo {
    name: string;
    path: string;
}

export interface MenuState {
    isMenuOpen: boolean;
    activeTab: TabInfo | null;
    openMenus: Record<string, boolean>;
    openTabs: TabInfo[];
    currentMenuItem: MenuItemData | undefined;
    toggleMenu: () => void;
    toggleSubmenu: (menuName: string) => void;
    setActiveTab: (menuItem: MenuItemData) => void;
    closeTab: (tabToClose: TabInfo) => void;
}

export interface MenuContextType extends MenuState {
    menus: MenuItemData[];
    loading: boolean;
    initialized: boolean;
    error: Error | null;
    loadUserMenus: () => Promise<void>;
    clearMenus: () => void;
}