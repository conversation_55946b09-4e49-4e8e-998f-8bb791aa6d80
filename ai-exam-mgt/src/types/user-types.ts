export interface DecodedToken {
    iss: string;            // 签发者
    iat: number;            // 签发时间
    exp: number;            // 过期时间
    userId: number;         // 用户 Id
    username: string;       // 用户名
    nickname?: string;      // 昵称（可选）
    avatar?: string;        // 头像（可选）
}

export interface ParsedUserInfo {
    userId: number;
    username: string;
    nickname?: string;
    avatar?: string;
}