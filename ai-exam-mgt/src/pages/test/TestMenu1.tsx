const TestMenuPage1 = () => {
  // 模拟数据
  const stats = [
    { title: "总考试数量", value: "1,234" },
    { title: "平均通过率", value: "76.5%" },
    { title: "活跃用户数", value: "5,678" },
  ];

  const monthlyData = [
    { month: "一月", examCount: 120, passRate: "80%" },
    { month: "二月", examCount: 150, passRate: "75%" },
    { month: "三月", examCount: 180, passRate: "78%" },
    { month: "四月", examCount: 200, passRate: "82%" },
  ];

  return (
    <div className="p-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white p-4 rounded shadow">
            <h2 className="text-lg font-semibold">{stat.title}</h2>
            <p className="text-3xl font-bold mt-2">{stat.value}</p>
          </div>
        ))}
      </div>

      <div className="bg-white p-4 rounded shadow">
        <h2 className="text-lg font-semibold mb-4">月度考试数据统计</h2>
        <table className="w-full">
          <thead>
            <tr>
              <th className="text-left">月份</th>
              <th className="text-left">考试数量</th>
              <th className="text-left">通过率</th>
            </tr>
          </thead>
          <tbody>
            {monthlyData.map((data, index) => (
              <tr key={index}>
                <td>{data.month}</td>
                <td>{data.examCount}</td>
                <td>{data.passRate}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TestMenuPage1;