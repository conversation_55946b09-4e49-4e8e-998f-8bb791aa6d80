import { useState, useEffect } from 'react';
import UserFilters from '@/components/pages/system/UserFilters';
import UserTable from '@/components/pages/system/UserTable';
import Pagination from '@/components/pages/Pagination';
import UserFormModal from '@/components/pages/system/UserFormModal';
import { MgtUserVO, MgtUserQueryVO } from '@/apis/pages/user-vo';
import { MgtUserController } from '@/apis/pages/pages-user';
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { message } from "antd";
import { getStorageObject, setStorageObject } from '@/utils/storageUtils';
import { STORAGE_KEYS } from '@/constants/storage';

const UserManagement = () => {
  const [filters, setFilters] = useState<MgtUserQueryVO>(() => {
    const cached = getStorageObject<MgtUserQueryVO>(STORAGE_KEYS.USER.FILTERS);
    return cached ?? {
      pageNum: 1,
      pageSize: 10,
      username: '',
      phoneNumber: '',
      nickname: '',
      status: null,
      startTime: '',
      endTime: '',
      roleIds: [],
      excludeRoleIds: [],
      targetRoleId: null,
    };
  });

  // 当筛选条件改变时，保存到 sessionStorage
  useEffect(() => {
    setStorageObject(STORAGE_KEYS.USER.FILTERS, filters);
  }, [filters]);

  const [currentPage, setCurrentPage] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<MgtUserVO | null>(null);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [users, setUsers] = useState<MgtUserVO[]>([]);
  const [total, setTotal] = useState(0);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const mgtUserController = new MgtUserController();

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleAction = async (action: string, id: number) => {
    const user = users.find(u => u.id === id);
    if (user) {
      setSelectedUser(user);
      switch (action) {
        case 'edit':
          setModalMode('edit');
          setIsModalOpen(true);
          break;
        case 'view':
          setModalMode('view');
          setIsModalOpen(true);
          break;
        case 'delete':
          handleDeleteUser(id);
          break;
        case 'enable':
          await handleUpdateStatus([id], 1);
          break;
        case 'disable':
          await handleUpdateStatus([id], 0);
          break;
      }
    }
  };

  const handleNewUser = () => {
    setSelectedUser(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  const handleDeleteUser = async (id: number) => {
    try {
      await mgtUserController.batchDelete({
        userIds: [id]
      });
      message.success('删除用户成功');
      fetchUsers();
    } catch (error: any) {
      message.error(`删除用户失败，${error?.data?.message}`);
    }
  };

  const handleUpdateStatus = async (userIds: number[], status: number) => {
    try {
      await mgtUserController.batchUpdateStatus({
        userIds,
        status
      });
      message.success(status === 1 ? '启用用户成功' : '禁用用户成功');
      fetchUsers();
    } catch (error: any) {
      message.error(`${status === 1 ? '启用' : '禁用'}用户失败，${error?.data?.message}`);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await mgtUserController.pageQuery({
        pageNum: currentPage,
        pageSize: itemsPerPage,
        username: filters.username,
        phoneNumber: filters.phoneNumber,
        nickname: filters.nickname,
        status: filters.status ? Number(filters.status) : null,
        startTime: filters.startTime,
        endTime: filters.endTime,
        roleIds: filters.roleIds,
        excludeRoleIds: filters.excludeRoleIds,
        targetRoleId: filters.targetRoleId,
      });
      setUsers(response.list || []);
      setTotal(response.total || 0);
    } catch (error: any) {
      message.error(`获取用户列表失败，${error?.data?.message}`);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [currentPage, itemsPerPage, filters]);

  const totalPages = Math.ceil(total / itemsPerPage);

  return (
    <div className="w-full p-8">
      <UserFilters 
        filters={filters as MgtUserQueryVO} 
        onFilterChange={handleFilterChange} 
      />
      
      <div className="mb-4">
        <Button onClick={handleNewUser}>
          <Plus className="h-4 w-4" /> 新建用户
        </Button>
      </div>    
      
      <UserTable 
        users={users}
        onAction={handleAction}
      />

      <Pagination 
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
        totalItems={total}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={(newSize: number) => {
          setCurrentPage(1);
          setItemsPerPage(newSize);
        }}
      />
      
      <UserFormModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedUser(null);
        }}
        onSuccess={fetchUsers}
        userId={selectedUser?.id ?? undefined}
        mode={modalMode}
      />
    </div>
  );
};

export default UserManagement;
