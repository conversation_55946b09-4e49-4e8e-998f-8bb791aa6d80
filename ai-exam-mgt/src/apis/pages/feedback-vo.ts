import { PageReqVO } from "./common-vo";
import { SubQuestionInfoVO } from "./question-vo";

export interface FeedbackQuestionInfoVO {
  answer: string;
  caseAnalysisQuestion: { [key: string]: SubQuestionInfoVO };
  certificateCode: string;
  certificateName: string;
  content: string;
  explanation: string;
  id: number | null;
  options: { [key: string]: string };
  subjectCode: string;
  subjectName: string;
  type: string;
}

export interface ProcessFeedbackQuestionInfoVO {
  answer: string;
  caseAnalysisQuestion: { [key: string]: SubQuestionInfoVO };
  certificateCode: string;
  content: string;
  explanation: string;
  options: { [key: string]: string };
  subjectCode: string;
  type: string;
}

export interface ProcessFeedbackVO {
  afterQuestionInfo: ProcessFeedbackQuestionInfoVO;
  feedbackResultContent: string;
  feedbackResultType: string;
  id: number | null;
  questionId: number | null;
}

export interface QuestionFeedbackQueryVO extends PageReqVO {
  certificateCodes: string[];
  feedbackTypes: string[];
  ids: number[];
  questionIds: number[];
  statuses: number[];
  subjectCodes: string[];
}

export interface QuestionFeedbackVO {
  afterQuestionId: number | null;
  afterQuestionInfo: FeedbackQuestionInfoVO;
  beforeQuestionInfo: FeedbackQuestionInfoVO;
  certificateCode: string;
  certificateName: string;
  content: string;
  createBy: string;
  createTime: number;
  exerciseMode: string;
  feedbackResultContent: string;
  feedbackResultType: any;
  feedbackType: any;
  id: number | null;
  questionId: number | null;
  status: number | null;
  subjectCode: string;
  subjectName: string;
  updateBy: string;
  updateTime: number;
  userId: number | null;
  userNickname: string;
  userPhone: string;
}
