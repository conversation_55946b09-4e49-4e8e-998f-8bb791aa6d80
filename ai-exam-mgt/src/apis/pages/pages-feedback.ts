import axiosInstance from "@/lib/axiosInstance";

import { PageRespVO } from "./common-vo";
import {
  ProcessFeedbackVO,
  QuestionFeedbackQueryVO,
  QuestionFeedbackVO,
} from "./feedback-vo";

export class MgtQuestionFeedbackController {
  public constructor() {}

  public async getFeedback(id: number | null): Promise<QuestionFeedbackVO> {
    return (await axiosInstance.get("/mgt/question-feedback/" + id + "", {}))
      .data;
  }

  public async pageQuery(
    queryVO: QuestionFeedbackQueryVO,
  ): Promise<PageRespVO<QuestionFeedbackVO>> {
    return (
      await axiosInstance.post("/mgt/question-feedback/pageQuery", queryVO, {})
    ).data;
  }

  public async processFeedback(processVO: ProcessFeedbackVO): Promise<void> {
    return (
      await axiosInstance.post("/mgt/question-feedback/process", processVO)
    ).data;
  }
}
