import {useState, useEffect} from 'react';
import type {FormProps} from 'antd';
import {Button, Cascader, Form, Input, Modal, Select, message} from 'antd';
import {MgtQuestionSetController} from '@/apis/pages/pages-question';
import {QUESTION_SET_TYPES} from '@/constants/question';
import { KnowledgeQuestionSetNodeVO } from '@/apis/pages/question-vo';

interface Option {
    value: string;
    label: string;
    children?: Option[];
}

interface ModalQuestionStoreProps {
    isOpen: boolean;
    onOk: () => void;
    onCancel: () => void;
    knowledgeStructure?: KnowledgeQuestionSetNodeVO[];
    certificateCode?: string;
    subjectCode?: string;
    mode?: 'create' | 'edit';
    questionSetId?: number;
}

type FieldType = {
    type: string;
    lastLevel?: string[];
    name?: string;
    year?: number;
};

const convertToOptions = (nodes?: KnowledgeQuestionSetNodeVO[]): Option[] => {
    if (!nodes) return [];
    return nodes.map(node => ({
        value: node.code,
        label: node.name,
        type: node.type,
        children: convertToOptions(node.children),
    }));
};

const QuestionSetAddModal = (props: ModalQuestionStoreProps) => {
    const {isOpen, onOk, onCancel, knowledgeStructure, certificateCode, subjectCode, mode = 'create', questionSetId} = props;
    const [form] = Form.useForm();
    const [questionType, setQuestionType] = useState<string>();
    const [originalData, setOriginalData] = useState<any>(null);
    const questionSetController = new MgtQuestionSetController();

    useEffect(() => {
        if (mode === 'edit' && questionSetId && isOpen) {
            // 获取题库详情
            questionSetController.getDetail(questionSetId)
                .then(response => {
                    setOriginalData(response);
                    setQuestionType(response.type);
                    form.setFieldsValue({
                        type: response.type,
                        name: response.name,
                        year: response.year,
                    });
                })
                .catch((error: any) => {
                    message.error(`获取题库详情失败，${error?.data?.message || '未知错误'}`);
                });
        }
    }, [questionSetId, mode, isOpen]);

    const handleOk = () => {
        form.resetFields();
        setQuestionType(undefined);
        setOriginalData(null);
        onOk();
    };

    const handleCancel = () => {
        form.resetFields();
        setQuestionType(undefined);
        setOriginalData(null);
        onCancel();
    };

    const onFinish: FormProps<FieldType>['onFinish'] = async (values) => {
        try {
            if (mode === 'edit' && questionSetId && originalData) {
                // 编辑模式下更新 name 和 year 字段
                await questionSetController.update(questionSetId, {
                    name: values.name || '',
                    year: values.year || null,
                    addQuestionIds: [],
                    removeQuestionIds: [],
                    remark: ''
                });
            } else {
                await questionSetController.create({
                    certificateCode: certificateCode || '',
                    subjectCode: subjectCode || '',
                    type: values.type || '',
                    name: values.name || '',
                    year: values.year || null,
                    articleCode: values.lastLevel?.[0] || '',
                    chapterCode: values.lastLevel?.[1] || '',
                    sectionCode: values.lastLevel?.[2] || '',
                    questionIds: [],
                    remark: null,
                    externalId: null,
                    sourceExt: null
                });
            }
            message.success(mode === 'edit' ? '编辑成功' : '添加成功');
            handleOk();
        } catch (error: any) {
            message.error(`${mode === 'edit' ? '编辑' : '添加'}失败，${error?.data?.message || '未知错误'}`);
        }
    };

    const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = (errorInfo) => {
        console.log('Failed:', errorInfo);
    };

    return (
        <>
            <Modal
                title={mode === 'edit' ? "编辑题库" : "新增题库"}
                open={isOpen}
                onOk={handleOk}
                onCancel={handleCancel}
                okText={mode === 'edit' ? "保存" : "添加"}
                footer={null}
                width={700}
                style={{ top: 0 }}
                styles={{
                    header: {
                        marginTop: 170,
                        paddingTop: 10
                    },
                    content: {
                        paddingTop: 0
                    }
                }}
            >
                <Form
                    form={form}
                    name="basic"
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 19 }}
                    style={{ maxWidth: 650, marginTop: '20px' }}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete="off"
                >
                    <Form.Item<FieldType>
                        label="题库类型"
                        name="type"
                        rules={[{required: true, message: '请选择题库类型'}]}
                    >
                        <Select
                            // 只支持新增历年真题和模拟考试类型题库
                            options={QUESTION_SET_TYPES
                                .filter(type => type.value === 'REAL_EXAMS' || type.value === 'MOCK_EXAMS')
                                .map(type => ({value: type.value, label: type.label}))}
                            onChange={(value) => setQuestionType(value)}
                            placeholder="请选择题库类型"
                            disabled={mode === 'edit'}
                        />
                    </Form.Item>

                    {(questionType === 'CHAP_PRACTICE' || questionType === 'CHAP_REAL_EXAMS') && mode !== 'edit' && (
                        <Form.Item<FieldType>
                            label="章节目录"
                            name="lastLevel"
                            rules={[{required: true, message: '请选择章节目录'}]}
                            labelCol={{ span: 4 }}
                            wrapperCol={{ span: 19 }}
                        >
                            <Cascader
                                options={convertToOptions(knowledgeStructure)}
                                placeholder="请选择章节目录"
                                changeOnSelect
                                style={{ width: '100%' }}
                                displayRender={(labels) => labels.join(' / ')}
                                showSearch={{
                                    filter: (inputValue, path) => {
                                        return path.some(option => 
                                            option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1
                                        );
                                    }
                                }}
                                onChange={(_, selectedOptions) => {
                                    if (selectedOptions && selectedOptions.length > 0) {
                                        const articleOption = selectedOptions.find((option: any) => option.type === 'article');
                                        const chapterOption = selectedOptions.find((option: any) => option.type === 'chapter');
                                        const sectionOption = selectedOptions.find((option: any) => option.type === 'section');

                                        const chapterAndSection = [
                                            articleOption?.value || '',
                                            chapterOption?.value || '',
                                            sectionOption?.value || ''
                                        ];
                                        form.setFieldsValue({
                                            lastLevel: chapterAndSection
                                        });
                                    }
                                }}
                            />
                        </Form.Item>
                    )}

                    {questionType === 'REAL_EXAMS' && (
                        <Form.Item<FieldType>
                            label="年份"
                            name="year"
                            rules={[
                                { required: true, message: '请输入年份' },
                                {
                                    pattern: /^[0-9]{4}$/,
                                    message: '请输入4位数字年份'
                                },
                                {
                                    validator: (_, value) => {
                                        const year = parseInt(value);
                                        if (year >= 2000 && year <= 2099) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject('年份必须在2000-2099之间');
                                    }
                                }
                            ]}
                        >
                            <Input
                                placeholder="请输入年份（2000-2099）"
                                maxLength={4}
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    )}

                    <Form.Item<FieldType>
                        label="题库名称"
                        name="name"
                        rules={[{required: true, message: '请输入题库名'}]}
                    >
                        <Input placeholder="请输入题库名称"/>
                    </Form.Item>

                    <Form.Item wrapperCol={{offset: 4, span: 19}} style={{ textAlign: 'right' }}>
                        <Button type="primary" htmlType="submit">
                            {mode === 'edit' ? '保存' : '添加'}
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default QuestionSetAddModal;
