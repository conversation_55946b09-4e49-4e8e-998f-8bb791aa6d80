import React, { useEffect, useState } from 'react';
import { Modal, Select, message, Checkbox, Input } from 'antd';
import { MgtQuestionSetController } from '@/apis/pages/pages-question';
import { QuestionSetVO } from '@/apis/pages/question-vo';
import { QuestionVO } from '@/apis/pages/question-vo';
import { Button } from "@/components/ui/button";
import { KnowledgeQuestionSetNodeVO } from '@/apis/pages/question-vo';

interface QuestionSetBindModalProps {
  isOpen: boolean;
  onCancel: () => void;
  questionId: number | undefined;
  question: QuestionVO | undefined;
}

const QuestionSetBindModal: React.FC<QuestionSetBindModalProps> = ({
  isOpen,
  onCancel,
  questionId,
  question,
}) => {
  const [chapterPracticeSets, setChapterPracticeSets] = useState<KnowledgeQuestionSetNodeVO[]>([]);
  const [chapterRealExamsSets, setChapterRealExamsSets] = useState<KnowledgeQuestionSetNodeVO[]>([]);
  const [normalQuestionSets, setNormalQuestionSets] = useState<Record<string, QuestionSetVO[]>>({});
  const [loading, setLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('');
  const [showBound, setShowBound] = useState<boolean | null>(true);
  const [selectedQuestionSets, setSelectedQuestionSets] = useState<number[]>([]);
  const [searchText, setSearchText] = useState('');
  const [boundSetIds, setBoundSetIds] = useState<Set<number>>(new Set());

  const questionSetController = new MgtQuestionSetController();

  useEffect(() => {
    if (isOpen && question?.subjectCode) {
      fetchAllData();
    }
  }, [isOpen, question?.subjectCode]);

  useEffect(() => {
    setSelectedQuestionSets([]);
  }, [showBound]);

  useEffect(() => {
    // 当 showBound 改变时，重置选中状态
    if (showBound === true) {
      // 如果显示已绑定题库，则默认全选所有已绑定的题库
      setSelectedQuestionSets(Array.from(boundSetIds));
    } else {
      // 如果显示未绑定题库，则清空选择
      setSelectedQuestionSets([]);
    }
  }, [showBound, boundSetIds]);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      const [
        chapterPracticeSets,
        chapterRealExamsSets,
        normalSets,
        boundSets
      ] = await Promise.all([
        // 1. 获取章节练习
        questionSetController.queryAndCreateKnowledgeQuestionSet({
          subjectCode: question?.subjectCode || '',
          questionSetType: 'CHAP_PRACTICE',
        }),
        // 2. 获取章节真题
        questionSetController.queryAndCreateKnowledgeQuestionSet({
          subjectCode: question?.subjectCode || '',
          questionSetType: 'CHAP_REAL_EXAMS',
        }),
        // 3. 获取历年真题和模拟考试题库
        questionSetController.pageQuery({
          pageNum: 1,
          pageSize: 1000,
          subjectCodes: [question?.subjectCode || ''],
          types: ['REAL_EXAMS', 'MOCK_EXAMS'],
          years: [],
          chapterCodes: [],
          sectionCodes: []
        }),
        // 4. 获取已绑定的题库
        questionSetController.getQuestionSetsByQuestionId(questionId || 0)
      ]);

      setChapterPracticeSets(chapterPracticeSets);
      setChapterRealExamsSets(chapterRealExamsSets);

      const groupedSets: Record<string, QuestionSetVO[]> = {
        'REAL_EXAMS': normalSets.list?.filter(set => set.type === 'REAL_EXAMS') || [],
        'MOCK_EXAMS': normalSets.list?.filter(set => set.type === 'MOCK_EXAMS') || [],
      };
      setNormalQuestionSets(groupedSets);
      setBoundSetIds(new Set(boundSets.map(set => set.id || 0)));
    } catch (error: any) {
      message.error(`获取数据失败：${error?.data?.message}`);
    } finally {
      setLoading(false);
    }
  };

  const resetState = () => {
    setSelectedQuestionSets([]);
    setShowBound(true);
    setSelectedType('');
    setSearchText('');
  };

  const resetStateAndRefresh = async () => {
    resetState();
    await fetchAllData();
  };

  const handleBind = async () => {
    if (selectedQuestionSets.length === 0) {
      message.warning('请选择题库');
      return;
    }

    Modal.confirm({
      title: '确认绑定',
      content: `确定要将当前题目绑定到选中的 ${selectedQuestionSets.length} 个题库吗？`,
      okText: '确认绑定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await questionSetController.bindQuestionSets({
            questionId: questionId || 0,
            questionSetIds: selectedQuestionSets,
          });
          message.success('绑定成功');
          await resetStateAndRefresh();
        } catch (error: any) {
          message.error(`绑定失败，${error?.data?.message}`);
        }
      }
    });
  };

  const getSetsToUnbind = () => {
    return Array.from(boundSetIds).filter(id => !selectedQuestionSets.includes(id));
  };

  const handleUnbind = async () => {
    const setsToUnbind = getSetsToUnbind();

    if (setsToUnbind.length === 0) {
      message.warning('请选择需要解绑的题库');
      return;
    }

    Modal.confirm({
      title: '确认解绑',
      content: `确定要将当前题目从选中的 ${setsToUnbind.length} 个题库中解绑吗？`,
      okText: '确认解绑',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: async () => {
        try {
          await questionSetController.unbindQuestionSets({
            questionId: questionId || 0,
            questionSetIds: setsToUnbind,
          });
          message.success('解绑成功');
          await resetStateAndRefresh();
        } catch (error: any) {
          message.error(`解绑失败，${error?.data?.message}`);
        }
      }
    });
  };

  // 创建一个通用的 Checkbox 渲染组件以保持一致性
  const CheckboxItem = ({
    label,
    checked,
    onChange,
    paddingLeft
  }: {
    label: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
    paddingLeft: string;
  }) => (
    <div className="p-3" style={{ paddingLeft }}>
      <div className="flex items-center">
        <Checkbox
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          className="mr-2"
        />
        <span className="font-normal">{label}</span>
      </div>
    </div>
  );

  const KnowledgeQuestionSetNode = ({ node, level, type }: {
    node: KnowledgeQuestionSetNodeVO;
    level: number;
    type: string;
  }) => {
    const hasChildren = node.children?.length > 0;
    const paddingLeft = `${(level + 2) * 16}px`;
    const nodeKey = node.questionSet?.id || 0;
    const isSelected = selectedQuestionSets.includes(nodeKey);

    return (
      <div key={nodeKey}>
        {!hasChildren ? (
          <CheckboxItem
            label={node.name}
            checked={isSelected}
            onChange={(checked) => {
              setSelectedQuestionSets(prev =>
                checked
                  ? [...prev, node.questionSet?.id || 0]
                  : prev.filter(id => id !== node.questionSet?.id || 0)
              );
            }}
            paddingLeft={paddingLeft}
          />
        ) : (
          <div className="p-3" style={{ paddingLeft }}>
            <span className="font-medium">{node.name}</span>
          </div>
        )}

        {hasChildren && node.children?.map(child => (
          <KnowledgeQuestionSetNode
            key={child.code}
            node={child}
            level={level + 1}
            type={type}
          />
        ))}
      </div>
    );
  };

  const renderQuestionSets = () => {
    // 过滤普通题库
    const filteredNormalSets = Object.entries(normalQuestionSets).reduce((acc, [type, sets]) => {
      const filteredSets = sets.filter(set => {
        const isBound = boundSetIds.has(set.id || 0);
        const matchesSearch = set.name.toLowerCase().includes(searchText.toLowerCase());
        const matchesType = !selectedType || selectedType === set.type;
        const matchesBoundStatus = showBound === null || showBound === isBound;
        return matchesSearch && matchesType && matchesBoundStatus;
      });
      if (filteredSets.length > 0) {
        acc[type] = filteredSets;
      }
      return acc;
    }, {} as Record<string, QuestionSetVO[]>);

    // 过滤章节练习和章节真题
    const filterKnowledgeQuestionSetNodes = (nodes: KnowledgeQuestionSetNodeVO[]): KnowledgeQuestionSetNodeVO[] => {
      return nodes.map(node => {
        // 处理叶子节点
        if (!node.children?.length) {
          const nodeQuestionSetId = node.questionSet?.id || 0;
          const isBound = boundSetIds.has(nodeQuestionSetId);
          const matchesSearch = node.name.toLowerCase().includes(searchText.toLowerCase());
          const matchesBoundStatus = showBound === null || showBound === isBound;

          if (!matchesSearch || !matchesBoundStatus) {
            return null;
          }
          return node;
        }

        // 先递归处理所有子节点
        const filteredChildren = filterKnowledgeQuestionSetNodes(node.children);

        // 如果有符合条件的子节点，则保留该节点
        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren
          };
        }

        return null;
      }).filter(Boolean) as KnowledgeQuestionSetNodeVO[];
    };

    const filteredPracticeSets = (!selectedType || selectedType === 'CHAP_PRACTICE')
      ? filterKnowledgeQuestionSetNodes(chapterPracticeSets)
      : [];

    const filteredRealExamsSets = (!selectedType || selectedType === 'CHAP_REAL_EXAMS')
      ? filterKnowledgeQuestionSetNodes(chapterRealExamsSets)
      : [];

    const hasNoData = (
      filteredPracticeSets.length === 0 &&
      filteredRealExamsSets.length === 0 &&
      Object.keys(filteredNormalSets).length === 0
    );

    if (hasNoData) {
      return (
        <div className="flex items-center justify-center h-[300px] text-gray-500">
          未找到符合条件的题库
        </div>
      );
    }

    return (
      <>
        {/* 章节练习 */}
        {filteredPracticeSets.length > 0 && (
          <div className="space-y-2">
            <div className="font-medium text-gray-700">章节练习</div>
            <div className="space-y-1">
              {filteredPracticeSets.map(node => (
                <KnowledgeQuestionSetNode
                  key={node.code}
                  node={node}
                  level={0}
                  type="CHAP_PRACTICE"
                />
              ))}
            </div>
          </div>
        )}

        {/* 章节真题 */}
        {filteredRealExamsSets.length > 0 && (
          <div className="space-y-2">
            <div className="font-medium text-gray-700">章节真题</div>
            <div className="space-y-1">
              {filteredRealExamsSets.map(node => (
                <KnowledgeQuestionSetNode
                  key={node.code}
                  node={node}
                  level={0}
                  type="CHAP_REAL_EXAMS"
                />
              ))}
            </div>
          </div>
        )}

        {/* 历年真题和模拟考试 */}
        {Object.entries(filteredNormalSets).map(([type, sets]) => (
          <div key={type} className="space-y-2">
            <div className="font-medium text-gray-700">
              {type === 'REAL_EXAMS' ? '历年真题' : '模拟考试'}
            </div>
            <div className="space-y-1">
              {sets.map(set => (
                <CheckboxItem
                  key={set.id}
                  label={set.name}
                  checked={selectedQuestionSets.includes(set.id || 0)}
                  onChange={(checked) => {
                    if (set.id) {
                      setSelectedQuestionSets(prev =>
                        checked && set.id
                          ? [...prev, set.id]
                          : prev.filter(id => id !== set.id)
                      );
                    }
                  }}
                  paddingLeft="32px"
                />
              ))}
            </div>
          </div>
        ))}
      </>
    );
  };

  const handleSearchTextChange = (value: string) => {
    setSearchText(value);
  };

  return (
    <Modal
      title="题目绑定题库"
      open={isOpen}
      onCancel={() => {
        resetState(); // 只重置状态，不重新获取数据
        onCancel();
      }}
      width={800}
      footer={null}
    >
      <div className="space-y-4">
        <div className="flex gap-4">
          <Select
            style={{ width: 200 }}
            value={showBound}
            onChange={setShowBound}
            placeholder="绑定状态"
          >
            <Select.Option value={true}>已绑定</Select.Option>
            <Select.Option value={false}>未绑定</Select.Option>
          </Select>

          <Select
            style={{ width: 200 }}
            value={selectedType || undefined}
            onChange={setSelectedType}
            placeholder="选择题库类型"
            allowClear
          >
            <Select.Option value="CHAP_PRACTICE">章节练习</Select.Option>
            <Select.Option value="CHAP_REAL_EXAMS">章节真题</Select.Option>
            <Select.Option value="REAL_EXAMS">历年真题</Select.Option>
            <Select.Option value="MOCK_EXAMS">模拟考试</Select.Option>
          </Select>

          <Input
            style={{ width: 200 }}
            value={searchText}
            onChange={(e) => handleSearchTextChange(e.target.value)}
            placeholder="搜索题库名称"
          />
        </div>

        <div className="border rounded-md p-4 min-h-[300px] max-h-[400px] overflow-auto">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              加载中...
            </div>
          ) : (
            <div className="space-y-4">
              {renderQuestionSets()}
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>
          {showBound ? (
            <Button
              onClick={handleUnbind}
              disabled={getSetsToUnbind().length === 0}
              className={`${getSetsToUnbind().length === 0
                  ? 'text-gray-400 bg-gray-100'
                  : 'text-white bg-red-500 hover:bg-red-600'
                }`}
            >
              批量解绑
            </Button>
          ) : (
            <Button
              onClick={handleBind}
              disabled={selectedQuestionSets.length === 0}
              className={`${selectedQuestionSets.length === 0
                  ? 'text-gray-400 bg-gray-100'
                  : 'text-white bg-green-500 hover:bg-green-600'
                }`}
            >
              批量绑定
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default QuestionSetBindModal;
