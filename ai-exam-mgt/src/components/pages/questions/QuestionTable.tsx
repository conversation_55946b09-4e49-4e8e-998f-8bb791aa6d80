import { useState } from 'react';
import { Table, Modal, Button, Tag } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined, CopyOutlined, BookOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { getQuestionType, getQuestionStatus } from '@/constants/question';
import dayjs from 'dayjs';

interface QuestionTableProps {
  questions: any[];
  selectedQuestions: number[];
  setSelectedQuestions: (selected: number[]) => void;
  onAction: (action: string, id: number, question?: any) => void;
  loading?: boolean;
}

const QuestionTable = ({ 
  questions, 
  selectedQuestions, 
  setSelectedQuestions, 
  onAction,
  loading 
}: QuestionTableProps) => {
  const [offlineModalOpen, setOfflineModalOpen] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<{ questionBanks?: any[]; id: number; status: number; } | null>(null);

  const handleDelete = (question: { id: number; status: number }) => {
    if ([1, 2].includes(question.status)) {
      setCurrentQuestion(question);
      setOfflineModalOpen(true);
    } else {
      onAction('delete', question.id, question);
    }
  };

  const handleConfirmOffline = (confirmed: boolean) => {
    if (confirmed && currentQuestion) {
      onAction('delete', currentQuestion.id, currentQuestion);
    }
    setOfflineModalOpen(false);
    setCurrentQuestion(null);
  };

  const getOfflineMessage = (status: number) => {
    switch (status) {
      case 0:
        return '确认要下线该待提交审核的题目吗？';
      case 1:
        return '该题目正在审核中，确认下线吗？';
      case 2:
        return '该题目已审核通过，下线后将影响题库使用，是否继续？';
      case 3:
        return '该题目审核不通过，确认要下线吗？';
      default:
        return '确认要下线该题目吗？';
    }
  };

  const columns: TableColumnsType<any> = [
    {
      title: '编号',
      dataIndex: 'id',
      width: 50,
      fixed: 'left',
    },
    {
      title: '题型',
      dataIndex: 'type',
      width: 60,
      fixed: 'left',
      render: (type) => getQuestionType(type)?.label || '-'
    },
    {
      title: '题干',
      dataIndex: 'content',
      width: 150,
      fixed: 'left',
      ellipsis: true
    },
    {
      title: '答案',
      dataIndex: 'answer',
      width: 150,
      ellipsis: true
    },
    {
      title: '解析',
      dataIndex: 'explanation',
      width: 150,
      ellipsis: true
    },
    {
      title: '证书',
      dataIndex: 'certificateName',
      width: 120
    },
    {
      title: '科目',
      dataIndex: 'subjectName',
      width: 150
    },
    {
      title: '更新人',
      dataIndex: 'updateBy',
      width: 100
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime', 
      width: 100,
      render: (time) => time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status) => {
        const questionStatus = getQuestionStatus(status);
        return <Tag color={questionStatus?.color}>{questionStatus?.label}</Tag>;
      }
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <Button.Group>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => onAction('view', record.id, record)}
          />
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => onAction('copy', record.id, record)}
          />
          {[0].includes(record.status) && (
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onAction('edit', record.id, record)}
            />
          )}
          {[0, 1, 2, 3].includes(record.status) && (
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          )}
          <Button
            type="text"
            icon={<BookOutlined />}
            onClick={() => onAction('bindQuestionSet', record.id, record)}
          />
        </Button.Group>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedQuestions,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedQuestions(selectedRowKeys as number[]);
    }
  };

  const totalWidth = columns.reduce((total, column) => total + (column.width as number || 0), 0) + 600;

  return (
    <>
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={questions}
        rowKey="id"
        scroll={{ x: totalWidth }}
        pagination={false}
        loading={loading}
        size="middle"
      />

      <Modal
        title="下线确认"
        open={offlineModalOpen}
        onOk={() => handleConfirmOffline(true)}
        onCancel={() => handleConfirmOffline(false)}
        okText="确认"
        cancelText="取消"
      >
        <p>{currentQuestion && getOfflineMessage(currentQuestion.status)}</p>
      </Modal>
    </>
  );
};

export default QuestionTable;
