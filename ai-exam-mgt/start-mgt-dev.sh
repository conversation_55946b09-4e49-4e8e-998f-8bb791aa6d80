#!/usr/bin/env bash

ENV=prod
PROJECT_NAME=ai-exam-mgt
PROJECT_PATH=/root/ai-exam/ai-exam-mgt

function git_pull() {
    echo "开始拉取代码..."
    git stash
    git pull
    echo "拉取代码完成!"
}

function install() {
    echo "开始安装依赖..."
    npm install
    echo "依赖安装完成!"
}

function build() {
    echo "开始构建项目..."
    npm run build
    echo "项目构建完成!"
}

function kill_previous_process() {
    echo "开始杀死之前的进程..."
    # 精确匹配完整的进程命令
    pid=$(ps aux | grep "node /root/ai-exam/ai-exam-mgt/node_modules/.bin/vite --mode $ENV" | grep -v grep | awk '{print $2}')

    if [ -z "$pid" ]; then
        echo "之前无进程，无需杀死!"
    else
        echo "发现进程 id: $pid，正在杀死..."
        kill "$pid"
        sleep 2
        # 检查进程是否还在运行，如果还在则强制终止
        if ps -p $pid > /dev/null 2>&1; then
            echo "进程未能正常停止，强制终止..."
            kill -9 $pid
            sleep 1
        fi
    fi

    echo "杀死之前的进程完成!"
}

function run() {
    echo "启动前端服务中，命令为：npm run $ENV"
    nohup npm run $ENV > ${PROJECT_PATH}/startup.log 2>&1 &

    # 等待服务启动
    sleep 3

    # 检查服务是否启动成功
    pid=$(ps aux | grep "node /root/ai-exam/ai-exam-mgt/node_modules/.bin/vite --mode $ENV" | grep -v grep | awk '{print $2}')
    if [ -n "$pid" ]; then
        echo "服务启动成功，进程 ID: $pid"
        port=$(netstat -nlp 2>/dev/null | grep "$pid/node" | grep -oP ':\K\d+' | head -1)
        if [ -n "$port" ]; then
            echo "服务运行端口: $port"
        else
            echo "无法获取端口信息"
        fi
    else
        echo "服务启动失败，请检查日志"
        exit 1
    fi
}

# 切换到项目目录
cd $PROJECT_PATH

git_pull
install
build
kill_previous_process
run
