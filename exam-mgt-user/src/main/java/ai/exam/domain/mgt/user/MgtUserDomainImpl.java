package ai.exam.domain.mgt.user;

import ai.exam.common.util.ErrorMessageUtils;
import ai.exam.domain.mgt.PageRespDO;
import ai.exam.domain.mgt.user.mapper.MgtUserEntityDOMapper;
import ai.exam.domain.mgt.user.po.MgtUserEntity;
import ai.exam.domain.mgt.user.po.MgtUserRoleEntity;
import ai.exam.domain.mgt.user.repository.MgtUserRepository;
import ai.exam.domain.mgt.user.repository.MgtUserRoleRepository;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/10/20 15:47
 * @description
 */
@Component
public class MgtUserDomainImpl implements MgtUserDomain {

    /**
     * 超级管理员用户名列表
     */
    private static final Set<String> SUPER_ADMIN_USERNAMES = Set.of(
            "superAdmin", "systemAdmin", "超级管理员", "系统管理员"
    );

    @Resource
    private MgtUserRepository mgtUserRepository;

    @Resource
    private MgtUserRoleRepository mgtUserRoleRepository;

    @Resource
    private MgtUserEntityDOMapper mgtUserEntityDOMapper;

    @Resource
    private MgtRoleDomain mgtRoleDomain;

    @Override
    @Transactional
    public MgtUserDO saveUser(MgtUserDO mgtUserDO, List<Long> roleIds) {
        // 新增时的必填字段校验
        if (mgtUserDO.getId() == null) {
            validateNewUser(mgtUserDO);
        }

        // 校验手机号唯一性（新增和修改都需要校验）
        validatePhoneNumber(mgtUserDO);

        // 转换为实体对象
        MgtUserEntity entity = mgtUserEntityDOMapper.toMgtUserEntity(mgtUserDO);

        if (mgtUserDO.getId() != null) {
            // 更新时，检查用户是否存在并保持不变的字段
            MgtUserEntity existingEntity = mgtUserRepository.findById(mgtUserDO.getId())
                    .orElseThrow(() -> new EntityNotFoundException("用户不存在"));
            preserveUnchangeableFields(entity, existingEntity, mgtUserDO);
        }

        // 保存用户实体
        MgtUserEntity savedEntity = mgtUserRepository.save(entity);

        // 处理角色关联
        if (savedEntity.getId() != null) {
            handleUserRoleAssociations(savedEntity.getId(), roleIds, mgtUserDO.getUpdateBy());
        }

        // 转换回 DO 并返回
        return mgtUserEntityDOMapper.toMgtUserDO(savedEntity);
    }

    /**
     * 验证新用户的必填字段
     *
     * @param mgtUserDO 用户信息
     */
    private void validateNewUser(MgtUserDO mgtUserDO) {
        List<String> errors = new ArrayList<>();

        // 校验用户名
        if (StringUtils.isBlank(mgtUserDO.getUsername())) {
            errors.add("用户名不能为空");
        } else {
            Optional<MgtUserEntity> existingUser = mgtUserRepository.findByUsername(mgtUserDO.getUsername());
            if (existingUser.isPresent()) {
                errors.add("用户名已存在");
            }
        }

        // 校验手机号
        if (StringUtils.isBlank(mgtUserDO.getPhoneNumber())) {
            errors.add("手机号不能为空");
        }

        // 校验密码
        if (StringUtils.isBlank(mgtUserDO.getPassword())) {
            errors.add("密码不能为空");
        }

        // 如果有错误，抛出异常
        if (!errors.isEmpty()) {
            throw new IllegalArgumentException(String.join("；", errors));
        }
    }

    /**
     * 校验手机号唯一性
     *
     * @param mgtUserDO 用户信息
     */
    private void validatePhoneNumber(MgtUserDO mgtUserDO) {
        if (StringUtils.isNotBlank(mgtUserDO.getPhoneNumber())) {
            Optional<MgtUserEntity> existingUser = mgtUserRepository.findByPhoneNumber(mgtUserDO.getPhoneNumber());
            if (existingUser.isPresent() && !existingUser.get().getId().equals(mgtUserDO.getId())) {
                throw new IllegalStateException("手机号已被其他用户使用");
            }
        }
    }

    /**
     * 保持不可变字段的值
     *
     * @param entity         新实体
     * @param existingEntity 已存在的实体
     * @param mgtUserDO      用户信息
     */
    private void preserveUnchangeableFields(MgtUserEntity entity, MgtUserEntity existingEntity, MgtUserDO mgtUserDO) {
        entity.setCreateBy(existingEntity.getCreateBy());
        entity.setCreateTime(existingEntity.getCreateTime());
        entity.setUsername(existingEntity.getUsername());  // 用户名不允许修改
        entity.setStatus(existingEntity.getStatus());

        // 如果没有传密码，保持原密码
        if (StringUtils.isBlank(mgtUserDO.getPassword())) {
            entity.setPassword(existingEntity.getPassword());
        }

        // 如果没有传手机号，保持原手机号
        if (StringUtils.isBlank(mgtUserDO.getPhoneNumber())) {
            entity.setPhoneNumber(existingEntity.getPhoneNumber());
        }
    }

    /**
     * 处理用户角色关联
     *
     * @param userId   用户 Id
     * @param roleIds  新的角色 Id 列表
     * @param updateBy 更新人
     */
    private void handleUserRoleAssociations(Long userId, List<Long> roleIds, String updateBy) {
        if (userId == null) {
            return;
        }

        // 1. 获取当前用户的所有角色关联
        List<MgtUserRoleEntity> existingRoles = mgtUserRoleRepository.findByUserId(userId);
        Map<Long, MgtUserRoleEntity> existingRoleMap = existingRoles.stream()
                .collect(Collectors.toMap(MgtUserRoleEntity::getRoleId, role -> role));

        // 2. 将新角色列表转换为 Set 便于比较
        Set<Long> newRoleIds = CollectionUtils.isEmpty(roleIds) ?
                Collections.emptySet() : new HashSet<>(roleIds);

        // 3. 分类处理
        List<MgtUserRoleEntity> toAdd = new ArrayList<>();
        List<MgtUserRoleEntity> toUpdate = new ArrayList<>();
        List<Long> toDelete = new ArrayList<>();

        // 处理需要删除的关联
        existingRoleMap.forEach((roleId, entity) -> {
            if (!newRoleIds.contains(roleId)) {
                toDelete.add(entity.getId());
            }
        });

        // 处理需要新增或更新的关联
        for (Long roleId : newRoleIds) {
            MgtUserRoleEntity existingRole = existingRoleMap.get(roleId);
            if (existingRole == null) {
                // 新增
                toAdd.add(MgtUserRoleEntity.builder()
                        .userId(userId)
                        .roleId(roleId)
                        .createBy(updateBy)
                        .updateBy(updateBy)
                        .build());
            } else {
                // 更新
                existingRole.setUpdateBy(updateBy);
                existingRole.setUpdateTime(LocalDateTime.now());
                toUpdate.add(existingRole);
            }
        }

        // 4. 批量执行数据库操作
        if (!toDelete.isEmpty()) {
            mgtUserRoleRepository.deleteAllById(toDelete);
        }
        if (!toUpdate.isEmpty()) {
            mgtUserRoleRepository.saveAll(toUpdate);
        }
        if (!toAdd.isEmpty()) {
            mgtUserRoleRepository.saveAll(toAdd);
        }
    }

    @Override
    @Transactional
    public void batchUpdateStatus(List<Long> ids, Integer targetStatus, String updateBy) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 1. 批量查询要更新的实体
        List<MgtUserEntity> entities = mgtUserRepository.findAllById(ids);
        if (entities.isEmpty()) {
            return;
        }

        // 2. 校验状态变更的合法性
        Map<Long, String> failureInfos = new LinkedHashMap<>();
        for (MgtUserEntity entity : entities) {
            if (Objects.equals(entity.getStatus(), targetStatus)) {
                continue;
            }
            String errorMsg = validateStatusTransition(entity.getStatus(), targetStatus, entity);
            if (errorMsg != null) {
                failureInfos.put(entity.getId(), errorMsg);
            }
        }

        // 3. 如果有状态变更不合法的情况，抛出异常
        Optional.of(failureInfos)
                .filter(infos -> !infos.isEmpty())
                .ifPresent(infos -> {
                    String errorMsg = ErrorMessageUtils.buildErrorMessage("以下用户状态更新失败", infos);
                    throw new IllegalStateException(errorMsg);
                });

        // 4. 执行批量更新
        mgtUserRepository.saveAll(
                entities.stream()
                        .peek(entity -> {
                            entity.setStatus(targetStatus);
                            entity.setUpdateBy(StringUtils.defaultIfBlank(updateBy, "unknownOperator"));
                        })
                        .collect(Collectors.toList())
        );
    }

    @Override
    @Transactional
    public void batchDelete(List<Long> ids, String updateBy) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 1. 批量查询要删除的实体
        List<MgtUserEntity> entities = mgtUserRepository.findAllById(ids);
        if (entities.isEmpty()) {
            return;
        }

        // 2. 检查是否有超级管理员
        Map<Long, String> failureInfos = entities.stream()
                .filter(this::isSuperAdmin)
                .collect(Collectors.toMap(
                        MgtUserEntity::getId,
                        user -> "超级管理员不能删除",
                        (v1, v2) -> v1,
                        LinkedHashMap::new
                ));

        // 3. 如果有不能删除的用户，抛出异常
        Optional.of(failureInfos)
                .filter(infos -> !infos.isEmpty())
                .ifPresent(infos -> {
                    String errorMsg = ErrorMessageUtils.buildErrorMessage("以下用户无法删除", infos);
                    throw new IllegalStateException(errorMsg);
                });

        // 4. 删除用户角色关联
        mgtUserRoleRepository.deleteByUserIdIn(ids);

        // 5. 删除用户
        mgtUserRepository.deleteAll(entities);
    }

    @Override
    public MgtUserDO getById(Long id) {
        return mgtUserRepository.findById(id)
                .map(mgtUserEntityDOMapper::toMgtUserDO)
                .orElse(null);
    }

    @Override
    public MgtUserDO getByUsername(String username) {
        return mgtUserRepository.findByUsername(username)
                .map(mgtUserEntityDOMapper::toMgtUserDO)
                .orElse(null);
    }

    @Override
    public MgtUserDO getByPhoneNumber(String phoneNumber) {
        return mgtUserRepository.findByPhoneNumber(phoneNumber)
                .map(mgtUserEntityDOMapper::toMgtUserDO)
                .orElse(null);
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    @Transactional(readOnly = true)
    public PageRespDO<MgtUserDO> pageQuery(MgtUserQueryDO queryDO) {
        // 构建查询条件
        Specification<MgtUserEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户名模糊查询
            if (StringUtils.isNotBlank(queryDO.getUsername())) {
                predicates.add(cb.like(root.get("username"),
                        "%" + queryDO.getUsername() + "%"));
            }

            // 手机号模糊查询
            if (StringUtils.isNotBlank(queryDO.getPhoneNumber())) {
                predicates.add(cb.like(root.get("phoneNumber"),
                        "%" + queryDO.getPhoneNumber() + "%"));
            }

            // 昵称模糊查询
            if (StringUtils.isNotBlank(queryDO.getNickname())) {
                predicates.add(cb.like(root.get("nickname"),
                        "%" + queryDO.getNickname() + "%"));
            }

            // 状态查询
            if (queryDO.getStatus() != null) {
                predicates.add(cb.equal(root.get("status"), queryDO.getStatus()));
            }

            // 创建时间范围查询
            if (queryDO.getStartTime() != null) {
                predicates.add(cb.greaterThanOrEqualTo(
                        root.get("createTime"),
                        queryDO.getStartTime()));
            }
            if (queryDO.getEndTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(
                        root.get("createTime"),
                        queryDO.getEndTime()));
            }

            // 角色 ID 列表过滤
            if (CollectionUtils.isNotEmpty(queryDO.getRoleIds())) {
                // 创建子查询获取指定角色的用户 ID
                Subquery<Long> subquery = query.subquery(Long.class);
                Root<MgtUserRoleEntity> subRoot = subquery.from(MgtUserRoleEntity.class);
                subquery.select(subRoot.get("userId"))
                        .where(subRoot.get("roleId").in(queryDO.getRoleIds()));

                predicates.add(root.get("id").in(subquery));
            }

            // 排除角色 ID 列表
            if (CollectionUtils.isNotEmpty(queryDO.getExcludeRoleIds())) {
                // 创建子查询获取要排除角色的用户 ID
                Subquery<Long> subquery = query.subquery(Long.class);
                Root<MgtUserRoleEntity> subRoot = subquery.from(MgtUserRoleEntity.class);
                subquery.select(subRoot.get("userId"))
                        .where(subRoot.get("roleId").in(queryDO.getExcludeRoleIds()));

                predicates.add(cb.not(root.get("id").in(subquery)));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 创建排序对象：按更新时间降序
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(
                queryDO.getPageNum() - 1,
                queryDO.getPageSize(),
                sort
        );

        // 执行分页查询
        Page<MgtUserEntity> entityPage = mgtUserRepository.findAll(spec, pageRequest);

        // 如果指定了目标角色 ID，查询该角色下的用户 ID
        Set<Long> targetRoleUserIds = Collections.emptySet();
        if (queryDO.getTargetRoleId() != null) {
            List<MgtUserRoleEntity> relations = mgtUserRoleRepository
                    .findByRoleId(queryDO.getTargetRoleId());

            if (CollectionUtils.isNotEmpty(relations)) {
                targetRoleUserIds = relations.stream()
                        .map(MgtUserRoleEntity::getUserId)
                        .collect(Collectors.toSet());
            }
        }

        // 转换结果
        Set<Long> finalTargetRoleUserIds = targetRoleUserIds;
        List<MgtUserDO> userDOList = entityPage.getContent().stream()
                .map(entity -> {
                    // 根据是否传入目标角色 ID 决定转换方式
                    if (queryDO.getTargetRoleId() != null) {
                        return mgtUserEntityDOMapper.toMgtUserDOWithRoleFlag(
                                entity,
                                finalTargetRoleUserIds.contains(entity.getId())
                        );
                    } else {
                        return mgtUserEntityDOMapper.toMgtUserDO(entity);
                    }
                })
                .collect(Collectors.toList());

        // 返回分页结果
        return PageRespDO.of(
                userDOList,
                entityPage.getTotalElements(),
                queryDO.getPageNum(),
                queryDO.getPageSize()
        );
    }

    @Override
    @Transactional(readOnly = true)
    public List<MgtRoleDO> getUserEnabledRoles(Long userId) {
        // 查询用户角色关联
        List<MgtUserRoleEntity> userRoles = mgtUserRoleRepository.findByUserId(userId);

        // 提取角色ID列表
        List<Long> roleIds = userRoles.stream()
                .map(MgtUserRoleEntity::getRoleId)
                .collect(Collectors.toList());

        // 查询启用状态的角色
        return mgtRoleDomain.getEnabledRolesByIds(roleIds);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MgtMenuDO> getUserEnabledMenus(Long userId) {
        // 查询用户的所有角色
        List<MgtUserRoleEntity> userRoles = mgtUserRoleRepository.findByUserId(userId);
        List<Long> roleIds = userRoles.stream()
                .map(MgtUserRoleEntity::getRoleId)
                .collect(Collectors.toList());

        // 过滤出启用状态的角色
        List<MgtRoleDO> enabledRoles = mgtRoleDomain.getEnabledRolesByIds(roleIds);
        List<Long> enabledRoleIds = enabledRoles.stream()
                .map(MgtRoleDO::getId)
                .collect(Collectors.toList());

        // 只查询启用状态角色的菜单
        return CollectionUtils.isEmpty(enabledRoleIds)
                ? Collections.emptyList()
                : mgtRoleDomain.getEnabledMenusByRoleIds(enabledRoleIds);
    }

    /**
     * 校验用户状态变更的合法性
     *
     * @param currentStatus 当前状态
     * @param targetStatus  目标状态：0-禁用，1-启用
     * @param user          用户实体
     * @return 错误信息，如果合法则返回 null
     */
    @SuppressWarnings("DuplicatedCode")
    private String validateStatusTransition(Integer currentStatus, Integer targetStatus, MgtUserEntity user) {
        // 状态值相同，不需要变更
        if (currentStatus.equals(targetStatus)) {
            return null;
        }

        // 状态只允许在启用 (1) 和禁用 (0) 之间切换
        if (currentStatus != 0 && currentStatus != 1) {
            return String.format("当前状态（%d）无效", currentStatus);
        }
        if (targetStatus != 0 && targetStatus != 1) {
            return String.format("目标状态（%d）无效", targetStatus);
        }

        // 检查是否是超级管理员且要禁用
        if (targetStatus == 0 && isSuperAdmin(user)) {
            return "超级管理员账号不能被禁用";
        }

        // 状态转换合法
        return null;
    }

    /**
     * 判断是否为超级管理员
     *
     * @param user 用户实体
     * @return true 表示是超级管理员
     */
    private boolean isSuperAdmin(MgtUserEntity user) {
        return user != null &&
                user.getUsername() != null &&
                SUPER_ADMIN_USERNAMES.contains(user.getUsername());
    }
}
