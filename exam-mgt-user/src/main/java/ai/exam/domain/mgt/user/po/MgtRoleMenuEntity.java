package ai.exam.domain.mgt.user.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/10/21 10:00
 * @description Role-Menu association entity
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mgt_role_menu", uniqueConstraints = {@UniqueConstraint(name = "uk_roleId_menuId", columnNames = {"roleId", "menuId"})})
@SuperBuilder
@NoArgsConstructor
public class MgtRoleMenuEntity extends BaseEntity {

    @Column(columnDefinition = "varchar(64) NOT NULL DEFAULT '' COMMENT '创建者'")
    private String createBy;

    @Column(columnDefinition = "varchar(64) NOT NULL DEFAULT '' COMMENT '更新者'")
    private String updateBy;

    @Column(columnDefinition = "bigint NOT NULL COMMENT '角色 Id'")
    private Long roleId;

    @Column(columnDefinition = "bigint NOT NULL COMMENT '菜单 Id'")
    private Long menuId;
}