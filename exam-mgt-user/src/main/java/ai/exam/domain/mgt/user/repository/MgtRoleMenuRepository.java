package ai.exam.domain.mgt.user.repository;

import ai.exam.domain.mgt.user.po.MgtRoleMenuEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @create 2024/10/20 16:52
 * @description
 */
@Repository
public interface MgtRoleMenuRepository extends JpaRepository<MgtRoleMenuEntity, Long> {

    /**
     * 根据角色 Id 查询角色菜单关联
     *
     * @param roleId 角色 Id
     * @return 角色菜单关联列表
     */
    List<MgtRoleMenuEntity> findByRoleId(Long roleId);

    /**
     * 根据角色 Id 列表查询角色菜单关联
     *
     * @param roleIds 角色 Id 列表
     * @return 角色菜单关联列表
     */
    List<MgtRoleMenuEntity> findByRoleIdIn(List<Long> roleIds);

    /**
     * 根据角色 Id 列表删除角色菜单关联
     *
     * @param roleIds 角色 Id 列表
     */
    @Modifying
    void deleteByRoleIdIn(List<Long> roleIds);
}