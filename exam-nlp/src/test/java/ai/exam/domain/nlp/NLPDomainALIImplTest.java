package ai.exam.domain.nlp;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class NLPDomainALIImplTest {

    @Test
    public void testCorrectError() {
        NLPDomain nlpDomain = new NLPDomainALIImpl();
        String shortErrorText = "我今天吃苹果，明天吃香姣，吃香姣";
        String shortCorrectText = "第 12 个字：姣=>蕉，第 16 个字：姣=>蕉";

        String longErrorText = "这是一段超过一百二十个字符的文本，我们需要创见一个非常长的字符串，我们需要创件一个非常长的字符串。为了测试correctError方法，我们需要创建一个非常长的字符串。这个字符串会被分割成多个部风，每个部分的长度都不会超过一百二十个字符。这样我们就能测试分段纠错的功能是否正常。每个部风的长度都不会超过一百二十个字符";
        String longCorrectText = "第 23 个字：见=>建，第 39 个字：件=>建，第 98 个字：风=>分第 143 个字：风=>分";

        Assertions.assertEquals(shortCorrectText, nlpDomain.correctSpellError(shortErrorText));
        Assertions.assertEquals(longCorrectText, nlpDomain.correctSpellError(longErrorText));
    }
}
