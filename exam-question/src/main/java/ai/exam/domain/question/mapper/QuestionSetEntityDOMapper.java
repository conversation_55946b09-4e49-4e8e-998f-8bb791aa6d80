package ai.exam.domain.question.mapper;

import ai.exam.common.util.JsonUtils;
import ai.exam.domain.question.ExamsScopeDO;
import ai.exam.domain.question.QuestionMockExamsScopeDO;
import ai.exam.domain.question.QuestionSetDO;
import ai.exam.domain.question.po.QuestionSetEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", imports = {JsonUtils.class})
public interface QuestionSetEntityDOMapper {

    /**
     * 实体转换为 DO
     */
    @Mapping(target = "mockExamsScope", expression = "java(JsonUtils.fromJson(entity.getMockExamsScope(), " +
            "ai.exam.domain.question.QuestionMockExamsScopeDO.class))")
    QuestionSetDO toQuestionSetDO(QuestionSetEntity entity);

    List<QuestionSetDO> toQuestionSetDOList(List<QuestionSetEntity> entityList);

    /**
     * DO 转换为实体
     */
    @Mapping(target = "mockExamsScope", expression = "java(JsonUtils.toJson(questionSetDO.getMockExamsScope()))")
    QuestionSetEntity toQuestionSetEntity(QuestionSetDO questionSetDO);

    /**
     * ExamsScopeDO 转换为 QuestionMockExamsScopeDO
     *
     * @param examsScopeDO 考试记分规则
     * @return 题目模拟考试记分规则
     */
    @Mapping(target = "duration", source = "duration")
    @Mapping(target = "totalScore", source = "totalScore")
    @Mapping(target = "passingScore", source = "passingScore")
    @Mapping(target = "scopeRules", source = "scopeRules")
    QuestionMockExamsScopeDO toQuestionMockExamsScopeDO(ExamsScopeDO examsScopeDO);
}
