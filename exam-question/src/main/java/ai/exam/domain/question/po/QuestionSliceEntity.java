package ai.exam.domain.question.po;

import ai.exam.common.repository.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_question_slice",
    uniqueConstraints = @UniqueConstraint(columnNames = {"questionId", "sliceId"}),
    indexes = {
        @Index(name = "idx_questionId", columnList = "questionId"),
        @Index(name = "idx_sliceId", columnList = "sliceId")
    }
)
@SuperBuilder
@NoArgsConstructor
public class QuestionSliceEntity extends BaseEntity {

    @Column(columnDefinition = "bigint(20) COMMENT '题目ID'", nullable = false)
    private Long questionId;

    @Column(columnDefinition = "bigint(20) COMMENT '切片ID'", nullable = false)
    private Long sliceId;

    @Column(columnDefinition = "int(11)")
    private Integer createBy;

    @Column(columnDefinition = "int(11)")
    private Integer updateBy;

    @Column(columnDefinition = "int(1) DEFAULT '0' COMMENT '删除标识 0存在 其他删除'", nullable = false)
    private Integer delFlag;

    @Column(columnDefinition = "longtext COMMENT '备注'")
    private String remark;
}
