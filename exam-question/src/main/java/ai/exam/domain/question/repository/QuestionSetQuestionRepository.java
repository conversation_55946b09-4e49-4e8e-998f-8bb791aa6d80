package ai.exam.domain.question.repository;

import ai.exam.domain.question.po.QuestionSetQuestionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface QuestionSetQuestionRepository extends JpaRepository<QuestionSetQuestionEntity, Long>, JpaSpecificationExecutor<QuestionSetQuestionEntity> {

    /**
     * 根据题库 Id 和题目 Id 列表查询关联关系
     *
     * @param questionSetId 题库 Id
     * @param questionIds   题目 Id 列表
     * @return 关联关系列表
     */
    List<QuestionSetQuestionEntity> findByQuestionSetIdAndQuestionIdIn(Long questionSetId, Set<Long> questionIds);

    /**
     * 根据题库 Id 和删除标记查询关联关系
     *
     * @param questionSetId 题库 Id
     * @param delFlag       删除标记
     * @return 关联关系列表
     */
    List<QuestionSetQuestionEntity> findByQuestionSetIdAndDelFlag(Long questionSetId, Integer delFlag);

    /**
     * 根据题库 Id 和删除标记查询关联关系，并按排序号升序排序
     *
     * @param questionSetId 题库 Id
     * @param delFlag       删除标记
     * @return 关联关系列表
     */
    List<QuestionSetQuestionEntity> findByQuestionSetIdAndDelFlagOrderBySortNumAsc(Long questionSetId, Integer delFlag);

    /**
     * 根据题目 Id 查询题库关联关系
     *
     * @param questionId 题目 Id
     * @param delFlag    删除标志
     * @return 题库关联关系列表
     */
    List<QuestionSetQuestionEntity> findByQuestionIdAndDelFlag(Long questionId, Integer delFlag);

    /**
     * 根据题目 Id 列表和删除标记查询关联关系
     *
     * @param questionIds 题目 Id 列表
     * @param delFlag     删除标记
     * @return 关联关系列表
     */
    List<QuestionSetQuestionEntity> findByQuestionIdInAndDelFlag(List<Long> questionIds, Integer delFlag);

    /**
     * 根据题目 Id 和题库 Id 列表查询关联关系
     *
     * @param questionId     题目 Id
     * @param questionSetIds 题库 Id 列表
     * @return 关联关系列表
     */
    List<QuestionSetQuestionEntity> findByQuestionIdAndQuestionSetIdIn(Long questionId, List<Long> questionSetIds);

    List<QuestionSetQuestionEntity> findQuestionIdsByQuestionSetIdInAndDelFlag(Set<Long> questionSetIds, int delFlag);

    /**
     * 根据题目 Id 列表查询关联关系
     *
     * @param questionIds 题目 Id 列表
     * @return 关联关系列表
     */
    List<QuestionSetQuestionEntity> findByQuestionIdIn(List<Long> questionIds);

    /**
     * 根据题目 ID 列表删除关联关系
     *
     * @param questionIds 题目 ID 列表
     */
    @Modifying
    void deleteByQuestionIdIn(List<Long> questionIds);

    /**
     * 根据题目 ID 和题库 ID 列表删除关联关系
     *
     * @param questionId     题目 ID
     * @param questionSetIds 题库 ID 列表
     */
    @Modifying
    void deleteByQuestionIdAndQuestionSetIdIn(Long questionId, List<Long> questionSetIds);

    /**
     * 根据题库 ID 和题目 ID 列表删除关联关系
     *
     * @param questionSetId 题库 ID
     * @param questionIds   题目 ID 列表
     */
    @Modifying
    void deleteByQuestionSetIdAndQuestionIdIn(Long questionSetId, Set<Long> questionIds);
}