package ai.exam.domain.question.po;

import ai.exam.common.repository.BaseEntity;
import ai.exam.domain.question.enums.QuestionType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_question", indexes = {
        @Index(name = "idx_external_Id", columnList = "externalId"),
        @Index(name = "idx_question_del", columnList = "delFlag,updateTime")
})
@SuperBuilder
@NoArgsConstructor
public class QuestionEntity extends BaseEntity {

    @Column(columnDefinition = "varchar(64) NOT NULL DEFAULT '' COMMENT '创建者'")
    private String createBy;

    @Column(columnDefinition = "varchar(64) NOT NULL DEFAULT '' COMMENT '更新者'")
    private String updateBy;

    @Column(name = "certificateCode", columnDefinition = "varchar(50) COMMENT '证书代码'", nullable = false)
    private String certificateCode;

    @Column(name = "subjectCode", columnDefinition = "varchar(50) COMMENT '科目代码'", nullable = false)
    private String subjectCode;

    @Column(name = "status", columnDefinition = "int COMMENT '状态：0-待提交审核、1-审核中、2-审核通过、3-审核不通过'", nullable = false)
    private Integer status;

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "varchar(20) COMMENT '题目类型'", nullable = false)
    private QuestionType type;

    @Column(columnDefinition = "double COMMENT '分值'", nullable = false)
    private Double score;

    @Column(columnDefinition = "longtext COMMENT '题目内容'", nullable = false)
    private String content;

    @Column(columnDefinition = "json COMMENT '选项'")
    private String options;

    @Column(columnDefinition = "varchar(4095) COMMENT '答案'", nullable = false)
    private String answer;

    @Column(columnDefinition = "longtext COMMENT '解析'")
    private String explanation;

    @Column(columnDefinition = "json COMMENT '案例分析题详情'")
    private String caseAnalysisQuestion;

    @Column(columnDefinition = "int(11) COMMENT '题目序号'")
    private Integer orderNumber;

    // 去除关联sql查询，效率太慢
    // @ElementCollection
    // @CollectionTable(name = "t_question_set_question", joinColumns = @JoinColumn(name = "questionId"))
    // @Column(name = "questionSetId")
    // private Set<Long> questionSetIds;

    @Column(columnDefinition = "int(1) DEFAULT '0' COMMENT '删除标识 0存在 其他删除'", nullable = false)
    private Integer delFlag;

    @Column(columnDefinition = "longtext COMMENT '备注'")
    private String remark;

    @Column(columnDefinition = "varchar(512) COMMENT '关联外部 Id'")
    private String externalId;

    @Column(columnDefinition = "datetime COMMENT '来源更新时间'")
    private LocalDateTime sourceChangeDate;

    @Column(columnDefinition = "int(1) DEFAULT '0' COMMENT '是否被管理后台用户修改过'", nullable = false)
    private Integer changedByUser;

    @Column(columnDefinition = "varchar(512) COMMENT '内容 Hash 值'")
    private String contentHash;

    @Column(columnDefinition = "bigint(20) NOT NULL DEFAULT '0' COMMENT '复制源题目 Id'")
    private Long copyFromId;
}

