package ai.exam.domain.question.repository;

import ai.exam.domain.question.po.QuestionSliceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuestionSliceRepository extends JpaRepository<QuestionSliceEntity, Long> {
    List<QuestionSliceEntity> findByQuestionId(Long questionId);
    List<QuestionSliceEntity> findBySliceId(Long sliceId);
    List<QuestionSliceEntity> findByQuestionIdIn(List<Long> questionIds);
}
