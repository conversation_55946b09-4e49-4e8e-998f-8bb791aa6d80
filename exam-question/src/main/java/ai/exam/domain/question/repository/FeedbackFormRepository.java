package ai.exam.domain.question.repository;

import ai.exam.domain.question.po.FeedbackFormEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FeedbackFormRepository extends JpaRepository<FeedbackFormEntity, Long>
        , JpaSpecificationExecutor<FeedbackFormEntity> {

    List<FeedbackFormEntity> findByUserIdOrderByCreateTimeDesc(Long userId);

}
