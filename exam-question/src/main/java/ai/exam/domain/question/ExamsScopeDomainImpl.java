package ai.exam.domain.question;

import ai.exam.common.util.ErrorMessageUtils;
import ai.exam.domain.mgt.PageRespDO;
import ai.exam.domain.question.enums.QuestionType;
import ai.exam.domain.question.mapper.ExamsScopeEntityDOMapper;
import ai.exam.domain.question.po.ExamsScopeEntity;
import ai.exam.domain.question.po.QuestionSetEntity;
import ai.exam.domain.question.repository.ExamsScopeRepository;
import ai.exam.domain.question.repository.QuestionSetRepository;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考试记分规则领域服务实现类
 */
@Slf4j
@Component
public class ExamsScopeDomainImpl implements ExamsScopeDomain {

    @Resource
    private ExamsScopeRepository examsScopeRepository;

    @Resource
    private QuestionSetRepository questionSetRepository;

    @Resource
    private ExamsScopeEntityDOMapper examsScopeEntityDOMapper;

    @Override
    @Transactional
    public ExamsScopeDO saveExamsScope(ExamsScopeDO examsScopeDO) {
        // 参数校验
        validateExamsScope(examsScopeDO);

        // 查询现有记录
        ExamsScopeEntity existingEntity = null;
        if (examsScopeDO.getId() != null) {
            existingEntity = examsScopeRepository.findById(examsScopeDO.getId())
                    .orElseThrow(() -> new EntityNotFoundException("考试记分规则不存在"));
        }

        // 转换为实体对象
        ExamsScopeEntity entity = examsScopeEntityDOMapper.toExamsScopeEntity(examsScopeDO);

        // 设置基础字段
        if (existingEntity != null) {
            // 更新时保持一些字段不变
            entity.setCreateTime(existingEntity.getCreateTime());
            entity.setCreateBy(existingEntity.getCreateBy());
            entity.setDelFlag(existingEntity.getDelFlag());
            entity.setCertificateCode(existingEntity.getCertificateCode());
            entity.setSubjectCode(existingEntity.getSubjectCode());
        } else {
            // 新增时设置删除标记
            entity.setDelFlag(0);
        }

        // 保存实体
        ExamsScopeEntity savedEntity = examsScopeRepository.save(entity);

        // 转换回 DO 并返回
        return examsScopeEntityDOMapper.toExamsScopeDO(savedEntity);
    }

    @Override
    @Transactional
    public void batchDelete(List<Long> ids, String updateBy) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 1. 批量查询要更新的实体
        List<ExamsScopeEntity> entities = examsScopeRepository.findAllById(ids);
        if (entities.isEmpty()) {
            return;
        }

        // 2. 校验是否有被引用的规则
        Map<Long, String> failureInfos = new LinkedHashMap<>();
        for (ExamsScopeEntity entity : entities) {
            // 查询使用该记分规则的题库列表
            List<QuestionSetEntity> questionSets = questionSetRepository
                    .findByMockExamsScopeIdAndDelFlag(entity.getId(), false);

            if (!questionSets.isEmpty()) {
                // 收集引用该规则的题库信息
                String failureMsg = questionSets.stream()
                        .map(qs -> String.format("题库 Id：%d，名称：%s", qs.getId(), qs.getName()))
                        .collect(Collectors.joining("；"));
                failureInfos.put(entity.getId(), "已被以下题库引用：" + failureMsg);
            }
        }

        // 3. 如果有不能删除的规则，抛出异常
        Optional.of(failureInfos)
                .filter(infos -> !infos.isEmpty())
                .ifPresent(infos -> {
                    String errorMsg = ErrorMessageUtils.buildErrorMessage("以下考试记分规则无法删除", infos);
                    throw new IllegalStateException(errorMsg);
                });

        // 4. 执行批量更新
        examsScopeRepository.saveAll(
                entities.stream()
                        .peek(entity -> {
                            entity.setDelFlag(1);
                            entity.setUpdateBy(StringUtils.defaultIfBlank(updateBy, "unknownOperator"));
                        })
                        .collect(Collectors.toList())
        );
    }

    @Override
    @Transactional(readOnly = true)
    public ExamsScopeDO getExamsScope(Long id) {
        ExamsScopeEntity entity = examsScopeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("考试记分规则不存在"));

        return examsScopeEntityDOMapper.toExamsScopeDO(entity);
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    @Transactional(readOnly = true)
    public PageRespDO<ExamsScopeDO> pageQuery(MgtExamsScopeQueryDO queryDO) {
        // 构建查询条件
        Specification<ExamsScopeEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加默认条件：未删除
            predicates.add(cb.equal(root.get("delFlag"), 0));

            // Id 列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getIds())) {
                predicates.add(root.get("id").in(queryDO.getIds()));
            }

            // 证书代码列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getCertificateCodes())) {
                predicates.add(root.get("certificateCode").in(queryDO.getCertificateCodes()));
            }

            // 科目代码列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getSubjectCodes())) {
                predicates.add(root.get("subjectCode").in(queryDO.getSubjectCodes()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 创建排序对象：按更新时间降序
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(queryDO.getPageNum() - 1, queryDO.getPageSize(), sort);

        // 执行分页查询
        Page<ExamsScopeEntity> entityPage = examsScopeRepository.findAll(spec, pageRequest);

        // 转换结果
        List<ExamsScopeDO> examsScopeDOList = entityPage.getContent().stream()
                .map(examsScopeEntityDOMapper::toExamsScopeDO)
                .toList();

        // 返回分页结果
        return PageRespDO.of(examsScopeDOList, entityPage.getTotalElements(), queryDO.getPageNum(), queryDO.getPageSize());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExamsScopeDO> getExamsScopeByCertificateAndSubject(String certificateCode, String subjectCode) {
        List<ExamsScopeEntity> entities = examsScopeRepository
                .findByCertificateCodeAndSubjectCodeAndDelFlag(certificateCode, subjectCode, 0);

        return examsScopeEntityDOMapper.toExamsScopeDOList(entities);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Long> getExamsScopeAssociatedQuestionSetIds(Long id) {
        // 查询使用该记分规则的题库列表
        List<QuestionSetEntity> questionSets = questionSetRepository
                .findByMockExamsScopeIdAndDelFlag(id, false);

        if (CollectionUtils.isEmpty(questionSets)) {
            return new ArrayList<>();
        }

        return questionSets.stream()
                .map(QuestionSetEntity::getId)
                .collect(Collectors.toList());
    }

    /**
     * 校验考试记分规则参数
     */
    private void validateExamsScope(ExamsScopeDO examsScopeDO) {
        List<String> errors = new ArrayList<>();

        // 基础字段校验
        if (examsScopeDO.getDuration() == null || examsScopeDO.getDuration() <= 0) {
            errors.add("考试时长必须大于 0");
        }
        if (examsScopeDO.getTotalScore() == null || examsScopeDO.getTotalScore() <= 0) {
            errors.add("总分必须大于 0");
        }
        if (examsScopeDO.getPassingScore() == null || examsScopeDO.getPassingScore() <= 0) {
            errors.add("及格分数必须大于 0");
        }

        // 及格分数必须小于总分
        if (examsScopeDO.getTotalScore() != null && examsScopeDO.getPassingScore() != null
                && examsScopeDO.getPassingScore() >= examsScopeDO.getTotalScore()) {
            errors.add("及格分数必须小于总分");
        }

        // 记分规则校验
        List<ExamsScopeScopeRuleDO> scopeRules = examsScopeDO.getScopeRules();
        if (CollectionUtils.isEmpty(scopeRules)) {
            errors.add("记分规则不能为空");
        } else {
            // 校验需要检查重复的题型是否重复
            List<String> duplicateCheckTypes = scopeRules.stream()
                    .map(ExamsScopeScopeRuleDO::getQuestionType)
                    .filter(this::needCheckDuplicate)
                    .toList();

            long distinctTypeCount = duplicateCheckTypes.stream()
                    .distinct()
                    .count();
            if (distinctTypeCount != duplicateCheckTypes.size()) {
                errors.add("选择题、判断题类型的记分规则不能重复");
            }

            // 校验题型是否合法
            for (ExamsScopeScopeRuleDO rule : scopeRules) {
                try {
                    QuestionType.valueOf(rule.getQuestionType());
                } catch (IllegalArgumentException e) {
                    errors.add("无效的题型：" + rule.getQuestionType());
                }

                // 校验题目总数
                if (rule.getQuestionCount() == null) {
                    errors.add(String.format("题型 %s 的题目总数不能为空", rule.getQuestionType()));
                } else if (rule.getQuestionCount() <= 0) {
                    errors.add(String.format("题型 %s 的题目总数必须大于 0", rule.getQuestionType()));
                }

                // 校验 requiredAnswerCount
                if (rule.getRequiredAnswerCount() != null) {
                    if (rule.getQuestionCount() != null && rule.getRequiredAnswerCount() > rule.getQuestionCount()) {
                        errors.add(String.format(
                                "题型 %s 的需要答题数量（%d）不能超过题目总数（%d）",
                                rule.getQuestionType(),
                                rule.getRequiredAnswerCount(),
                                rule.getQuestionCount()
                        ));
                    }
                }
            }

            // 校验题号是否连续
            validateQuestionNumbers(scopeRules, errors);

            // 校验总分是否一致
            BigDecimal totalScoreFromRules = scopeRules.stream()
                    .map(rule -> {
                        // 使用 requiredAnswerCount 如果存在，否则使用 questionCount
                        int count = rule.getRequiredAnswerCount() != null ?
                                rule.getRequiredAnswerCount() :
                                rule.getQuestionCount();

                        BigDecimal questionCount = BigDecimal.valueOf(count);
                        return questionCount.multiply(rule.getFullScore());
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalScoreFromRules.compareTo(BigDecimal.valueOf(examsScopeDO.getTotalScore())) != 0) {
                errors.add(String.format(
                        "记分规则总分（%s）必须等于设置的总分（%d）",
                        totalScoreFromRules,
                        examsScopeDO.getTotalScore()
                ));
            }
        }

        if (!errors.isEmpty()) {
            throw new IllegalArgumentException(String.join("；", errors));
        }
    }

    /**
     * 判断题型是否需要校验重复
     *
     * @return true-需要校验重复，false-不需要校验重复
     */
    private boolean needCheckDuplicate(String questionType) {
        try {
            QuestionType type = QuestionType.valueOf(questionType);
            return List.of(
                    QuestionType.CHOICE,
                    QuestionType.CHOICES,
                    QuestionType.INDEFINITE_CHOICE,
                    QuestionType.TRUE_FALSE
            ).contains(type);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 校验题号的连续性
     */
    private void validateQuestionNumbers(List<ExamsScopeScopeRuleDO> scopeRules, List<String> errors) {
        int expectedStartNumber = 1;
        for (ExamsScopeScopeRuleDO rule : scopeRules) {
            // 起始题号必须等于期望的题号
            if (rule.getStartNumber() != expectedStartNumber) {
                errors.add("题号必须从 1 开始连续编号");
                return;
            }

            // 起始题号不能大于结束题号
            if (rule.getEndNumber() < rule.getStartNumber()) {
                errors.add("起始题号不能大于结束题号");
                return;
            }

            // 题目数量必须等于题号范围
            int questionCount = rule.getEndNumber() - rule.getStartNumber() + 1;
            if (questionCount != rule.getQuestionCount()) {
                errors.add("题目数量必须等于题号范围");
                return;
            }

            // 更新下一个期望的题号
            expectedStartNumber = rule.getEndNumber() + 1;
        }
    }
}