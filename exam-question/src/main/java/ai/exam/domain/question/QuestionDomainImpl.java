package ai.exam.domain.question;

import ai.exam.common.util.ErrorMessageUtils;
import ai.exam.common.util.QuestionContentUtils;
import ai.exam.domain.knowledge.KnowledgeDomain;
import ai.exam.domain.knowledge.SectionDO;
import ai.exam.domain.knowledge.SliceDO;
import ai.exam.domain.mgt.PageRespDO;
import ai.exam.domain.question.enums.QuestionSetType;
import ai.exam.domain.question.enums.QuestionStatus;
import ai.exam.domain.question.enums.QuestionType;
import ai.exam.domain.question.mapper.QuestionEntityDOMapper;
import ai.exam.domain.question.mapper.QuestionSetEntityDOMapper;
import ai.exam.domain.question.po.*;
import ai.exam.domain.question.repository.*;
import jakarta.annotation.Resource;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Component
public class QuestionDomainImpl implements QuestionDomain {

    /**
     * 敏感词列表，实际应该配置在配置文件中
     */
    private static final List<String> SENSITIVE_WORDS = Arrays.asList("jkd", "jinkaodian", "金考典", "建匠教育");

    @Resource
    private QuestionSetRepository questionSetRepository;

    @Resource
    private QuestionSetQuestionRepository questionSetQuestionRepository;

    @Resource
    private QuestionRepository questionRepository;

    @Resource
    private QuestionSliceRepository questionSliceRepository;

    @Resource
    private QuestionSetEntityDOMapper questionSetEntityDOMapper;

    @Resource
    private QuestionEntityDOMapper questionEntityDOMapper;

    @Resource
    private KnowledgeDomain knowledgeDomain;

    // @Override
    // @Transactional
    // public QuestionSetDO createQuestionSet(QuestionSetDO questionSetDO) {
    //     QuestionSetEntity entity = questionSetEntityDOMapper.toQuestionSetEntity(questionSetDO);
    //     QuestionSetEntity savedEntity = questionSetRepository.save(entity);
    //
    //     if (questionSetDO.getQuestionIds() != null && !questionSetDO.getQuestionIds().isEmpty()) {
    //         savedEntity.setQuestionIds(questionSetDO.getQuestionIds());
    //         savedEntity = questionSetRepository.save(savedEntity);
    //
    //         for (Long questionId : questionSetDO.getQuestionIds()) {
    //             associateQuestionWithQuestionSet(questionId, savedEntity.getId());
    //         }
    //     }
    //
    //     return getQuestionSetWithQuestions(savedEntity.getId());
    // }
    // @Override
    // @Transactional
    // public List<QuestionDO> createQuestions(Long questionSetId, List<QuestionDO> questionDOList) {
    //     QuestionSetEntity questionSet = questionSetRepository.findById(questionSetId).orElseThrow(() -> new EntityNotFoundException("QuestionSet not found"));
    //
    //     List<QuestionEntity> questionEntities = questionDOList.stream().map(questionEntityDOMapper::toQuestionEntity).collect(Collectors.toList());
    //
    //     List<QuestionEntity> savedQuestionEntities = questionRepository.saveAll(questionEntities);
    //
    //     for (QuestionEntity question : savedQuestionEntities) {
    //         associateQuestionWithQuestionSet(question.getId(), questionSetId);
    //     }
    //
    //     return savedQuestionEntities.stream().map(questionEntityDOMapper::toQuestionDO).collect(Collectors.toList());
    // }
    // @Override
    // @Transactional
    // public void addQuestionsToQuestionSet(Long questionSetId, Set<Long> questionIds) {
    // for (Long questionId : questionIds) {
    //     associateQuestionWithQuestionSet(questionId, questionSetId);
    // }
    // }
    // @Override
    // @Transactional
    // public void removeQuestionsFromQuestionSet(Long questionSetId, Set<Long> questionIds) {
    //     for (Long questionId : questionIds) {
    //         disassociateQuestionFromQuestionSet(questionId, questionSetId);
    //     }
    // }
    //
    // @Override
    // @Transactional
    // public void associateQuestionWithSlice(Long questionId, Long sliceId) {
    //     QuestionSliceEntity questionSliceEntity = new QuestionSliceEntity();
    //     questionSliceEntity.setQuestionId(questionId);
    //     questionSliceEntity.setSliceId(sliceId);
    //     questionSliceRepository.save(questionSliceEntity);
    // }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDO> getQuestionsByQuestionSetId(Long questionSetId) {
        List<QuestionDO> questions = getQuestionByQuestionIds(Lists.newArrayList(questionSetId));
        questions.forEach(q -> q.setQuestionSetId(questionSetId));
        return questions;
    }

    @Override
    public List<Long> getQuestionIdsByQuestionSetId(Long questionSetId) {
        List<QuestionSetQuestionEntity> questionSetQuestionEntitys = questionSetQuestionRepository.findQuestionIdsByQuestionSetIdInAndDelFlag(new HashSet<>(Lists.newArrayList(questionSetId)), 0);
        Set<Long> questionIds = questionSetQuestionEntitys.stream().filter(q -> q.getStatus().equals(QuestionStatus.APPROVED.getValue())).map(QuestionSetQuestionEntity::getQuestionId).collect(Collectors.toSet());
        return new ArrayList<>(questionIds);
    }

    @Override
    public List<Long> getQuestionIdsByQuestionSetId(List<Long> questionSetId) {
        List<QuestionSetQuestionEntity> questionSetQuestionEntitys = questionSetQuestionRepository.findQuestionIdsByQuestionSetIdInAndDelFlag(new HashSet<>(questionSetId), 0);
        Set<Long> questionIds = questionSetQuestionEntitys.stream().filter(q -> q.getStatus().equals(QuestionStatus.APPROVED.getValue())).map(QuestionSetQuestionEntity::getQuestionId).collect(Collectors.toSet());
        return new ArrayList<>(questionIds);
    }

    private List<QuestionDO> getQuestionByQuestionIds(List<Long> questionSetIds) {
        List<QuestionSetQuestionEntity> questionSetQuestionEntitys = questionSetQuestionRepository.findQuestionIdsByQuestionSetIdInAndDelFlag(new HashSet<>(questionSetIds), 0);
        Set<Long> questionIds = questionSetQuestionEntitys.stream().filter(q -> q.getStatus().equals(QuestionStatus.APPROVED.getValue())).map(QuestionSetQuestionEntity::getQuestionId).collect(Collectors.toSet());
        return getQuestionsByQuestionIds(Lists.newArrayList(questionIds));
    }

    // @Override
    // public List<QuestionDO> getQuestionsByQuestionSetId(List<Long> questionSetIds) {
    //     List<QuestionDO> result = new ArrayList<>();
    //     questionSetIds.forEach(questionSetId -> result.addAll(getQuestionsByQuestionSetId(questionSetId)));
    //     return result;
    // }

    @Override
    public Map<Long, List<Long>> getMapQuestionIdsByQuestionSetId(List<Long> questionSetIds) {
        List<QuestionSetQuestionEntity> questionSetQuestions = questionSetQuestionRepository.findQuestionIdsByQuestionSetIdInAndDelFlag(new HashSet<>(questionSetIds), 0);
        return questionSetQuestions.stream().filter(q -> q.getStatus().equals(QuestionStatus.APPROVED.getValue())).collect(Collectors.groupingBy(QuestionSetQuestionEntity::getQuestionSetId, Collectors.mapping(QuestionSetQuestionEntity::getQuestionId, Collectors.toList())));
    }

    // @Override
    // public List<Long> getSliceIdsByQuestionId(Long questionId) {
    //     List<QuestionSliceEntity> questionSliceEntities = questionSliceRepository.findByQuestionId(questionId);
    //     return questionSliceEntities.stream().map(QuestionSliceEntity::getSliceId).collect(Collectors.toList());
    // }

    // @Override
    // public List<Long> getQuestionIdsBySectionCode(String sectionCode) {
    //     return questionRepository.findQuestionIdsBySectionCode(sectionCode);
    // }

    // @Override
    // public Integer getExamYearCountBySliceId(Long sliceId, Integer recentYears) {
    //     return questionRepository.findExamYearCountBySliceId(sliceId, recentYears);
    // }

    // @Override
    // public Map<Long, Integer> getExamYearCountBySliceIds(List<Long> sliceIds, Integer recentYears) {
    //     List<Object[]> results = questionRepository.findExamYearCountBySliceIds(sliceIds, recentYears);
    //     Map<Long, Integer> examYearCounts = new HashMap<>();
    //     for (Object[] result : results) {
    //         Long sliceId = (Long) result[0];
    //         Long count = (Long) result[1];
    //         examYearCounts.put(sliceId, count.intValue());
    //     }
    //     return examYearCounts;
    // }

    // @Override
    // public List<QuestionDO> getQuestionsBySubjectCodeAndYear(String subjectCode, Integer year) {
    //     List<QuestionSetEntity> questionSets = questionSetRepository.findBySubjectCodeAndYearAndTypeAndDelFlag(subjectCode, year, QuestionSetType.REAL_EXAMS, false);
    //     Set<Long> questionSetIds = questionSets.stream().map(QuestionSetEntity::getId).collect(Collectors.toSet());
    //     return getQuestionByQuestionIds(Lists.newArrayList(questionSetIds));
    // }

    @Override
    public Map<String, List<Long>> getQuestionIdsBySectionCodes(List<String> sectionCodes, QuestionSetType questionSetType) {
        // 查询节下的题库id,根据题库id查询
        // getQuestionSetWithQuestions
        Map<String, List<Long>> questionIdsBySectionCode = new HashMap<>();
        List<QuestionSetEntity> questionSetEntitys = questionSetRepository.findBySectionCodeInAndTypeAndDelFlag(sectionCodes, questionSetType, false);
        questionSetEntitys.forEach(q -> {
            QuestionSetDO questionSet = getQuestionSetWithQuestions(q);
            Set<Long> list = questionSet.getQuestionIds();
            List<Long> hisList = questionIdsBySectionCode.get(questionSet.getSectionCode());
            if (hisList != null && !hisList.isEmpty()) {
                list.addAll(hisList);
                questionIdsBySectionCode.put(questionSet.getSectionCode(), list.stream().toList());
            } else {
                questionIdsBySectionCode.put(questionSet.getSectionCode(), list.stream().toList());
            }
        });
        return questionIdsBySectionCode;
    }

    // @Override
    // public Map<String, List<Long>> getQuestionIdsBySectionCodes(List<String> sectionCodes, QuestionSetType questionSetType) {
    //     List<Object[]> results = questionRepository.findQuestionIdsBySectionCodes(sectionCodes, questionSetType);
    //     Map<String, List<Long>> questionIdsBySectionCode = new HashMap<>();
    //
    //     for (Object[] result : results) {
    //         String sectionCode = (String) result[0];
    //         Long questionId = (Long) result[1];
    //         questionIdsBySectionCode.computeIfAbsent(sectionCode, k -> new ArrayList<>()).add(questionId);
    //     }
    //
    //     // 确保所有传入的 sectionCodes 都有对应的条目，即使是空列表
    //     for (String sectionCode : sectionCodes) {
    //         questionIdsBySectionCode.putIfAbsent(sectionCode, new ArrayList<>());
    //     }
    //
    //     return questionIdsBySectionCode;
    // }

    @Override
    public List<QuestionDO> getQuestionsByQuestionIds(List<Long> questionIds) {
        List<QuestionEntity> questionEntities = questionRepository.findByIdInAndDelFlag(questionIds, 0);
        return questionEntities.stream().filter(q -> q.getStatus().equals(QuestionStatus.APPROVED.getValue())).map(questionEntityDOMapper::toQuestionDO).collect(Collectors.toList());
    }

    @Override
    public QuestionDO getQuestionsByQuestionId(Long questionId) {
        Optional<QuestionEntity> questionEntity = questionRepository.findById(questionId);
        return questionEntity.isPresent() ? questionEntityDOMapper.toQuestionDO(questionEntity.get()) : new QuestionDO();
    }

    @Override
    public List<QuestionDO> getQuestionsByChapterCodes(Map<String, Integer> chapterQuestionCounts) {
        List<QuestionDO> result = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : chapterQuestionCounts.entrySet()) {
            String chapterCode = entry.getKey();
            int count = entry.getValue();
            List<Long> questionDOList = Lists.newArrayList();
            List<QuestionSetEntity> chapterQuestSet = questionSetRepository.findByChapterCodeAndTypeAndDelFlag(chapterCode, QuestionSetType.CHAP_REAL_EXAMS, false);
            if (CollectionUtils.isNotEmpty(chapterQuestSet)) {
                Set<Long> questionSetIds = chapterQuestSet.stream().map(c -> c.getId()).collect(Collectors.toSet());
                questionDOList = Optional.ofNullable(getQuestionIdsByQuestionSetId(Lists.newArrayList(questionSetIds))).orElse(Lists.newArrayList());
                if (CollectionUtils.isEmpty(questionDOList)) {
                    questionDOList = getQuestionDOListByParcitice(chapterCode);
                }
            } else {
                questionDOList = getQuestionDOListByParcitice(chapterCode);
            }
            // 随机打乱
            Collections.shuffle(questionDOList);
            result.addAll(getQuestionsByQuestionIds(questionDOList.subList(0, Math.min(count, questionDOList.size()))));
        }
        return result;
    }

    private List<Long> getQuestionDOListByParcitice(String chapterCode) {
        List<QuestionSetEntity> chapterQuestSet = questionSetRepository.findByChapterCodeAndTypeAndDelFlag(chapterCode, QuestionSetType.CHAP_PRACTICE, false);
        Set<Long> questionSetIds = chapterQuestSet.stream().map(c -> c.getId()).collect(Collectors.toSet());
        return Optional.ofNullable(getQuestionIdsByQuestionSetId(Lists.newArrayList(questionSetIds))).orElse(Lists.newArrayList());
    }

    // @Override
    // public List<QuestionDO> getExamQuestionsBySubjectAndSection(String subjectCode, String sectionCode) {
    //     List<QuestionEntity> historicalExamQuestions = questionRepository.findHistoricalExamQuestionsBySubjectAndSection(subjectCode, sectionCode);
    //     return historicalExamQuestions.stream().map(questionEntityDOMapper::toQuestionDO).collect(Collectors.toList());
    // }

    @Override
    public List<QuestionDO> getExamQuestionsBySubjectAndSection(String subjectCode, String sectionCode, QuestionSetType questionSetType) {
        List<QuestionSetEntity> questionSetEntities = questionSetRepository.findBySectionCodeInAndTypeAndDelFlag(Lists.newArrayList(sectionCode), questionSetType, false);
        return getQuestionByQuestionIds(questionSetEntities.stream().map(q -> q.getId()).collect(Collectors.toList()));
    }

    @Override
    public List<QuestionDO> getExamQuestionsBySubjectAndChapter(String subjectCode, String chapterCode, QuestionSetType questionSetType) {
        List<QuestionSetEntity> questionSetEntities = questionSetRepository.findByChapterCodeInAndTypeAndDelFlag(Lists.newArrayList(chapterCode), questionSetType, false);
        return getQuestionByQuestionIds(questionSetEntities.stream().map(q -> q.getId()).collect(Collectors.toList()));
    }

    // @Override
    // @Transactional(readOnly = true)
    // public List<QuestionSetDO> getExamQuestionSetsBySubject(String subjectCode) {
    //     List<QuestionSetEntity> examQuestionSets = questionSetRepository.findBySubjectCodeAndTypeAndDelFlagOrderByYearDesc(subjectCode, QuestionSetType.REAL_EXAMS, false);
    //     List<QuestionSetDO> questionSetDOList = questionSetEntityDOMapper.toQuestionSetDOList(examQuestionSets);
    //     Set<Long> questionSetIds = questionSetDOList.stream().map(q -> q.getId()).collect(Collectors.toSet());
    //     Map<Long, Set<Long>> questionSetIdToQuestionIds = questionSetQuestionRepository.findQuestionIdsByQuestionSetIdInAndDelFlag(questionSetIds, 0).stream()
    //             .filter(q -> q.getStatus().equals(QuestionStatus.APPROVED.getValue()))
    //             .collect(Collectors.groupingBy(QuestionSetQuestionEntity::getQuestionSetId, Collectors.mapping(QuestionSetQuestionEntity::getQuestionId, Collectors.toSet())));
    //     questionSetDOList.forEach(x -> x.setQuestionIds(questionSetIdToQuestionIds.get(x.getId())));
    //     return questionSetDOList;
    // }

    // @Override
    // public AIQuestionsDO getAIPracticeQuestions(String subjectCode, Long accountId) {
    //     List<Map<String, Object>> randomQuestions = questionRepository.findRandomQuestionsBySubjectCode(subjectCode, 20);
    //     AIQuestionsDO aiQuestions = new AIQuestionsDO();
    //     aiQuestions.setQuestions(randomQuestions.stream().map(questionEntityDOMapper::toQuestionDO).collect(Collectors.toList()));
    //     aiQuestions.setDescription("您的个性化习题已生成，合计20题，包含：\n" + "【15道】您已系统复习过知识点，但尚未练习到的该知识点对应的高频真题\n" + "【3道】您最近一周错题的变式练习题\n" + "【2道】您已系统复习过的知识点再次巩固\n\n" );
    //     return aiQuestions;
    // }

    // 什么作用？
    // private void associateQuestionWithQuestionSet(Long questionId, Long questionSetId) {
    //     QuestionEntity question = questionRepository.findById(questionId).orElseThrow(() -> new EntityNotFoundException("Question not found"));
    //     QuestionSetEntity questionSet = questionSetRepository.findById(questionSetId).orElseThrow(() -> new EntityNotFoundException("QuestionSet not found"));
    //     questionRepository.save(question);
    //
    //     if (questionSet.getQuestionIds() == null) {
    //         questionSet.setQuestionIds(new HashSet<>());
    //     }
    //     questionSet.getQuestionIds().add(questionId);
    //     questionSetRepository.save(questionSet);
    // }

    // private void disassociateQuestionFromQuestionSet(Long questionId, Long questionSetId) {
    //     QuestionEntity question = questionRepository.findById(questionId).orElseThrow(() -> new EntityNotFoundException("Question not found"));
    //     QuestionSetEntity questionSet = questionSetRepository.findById(questionSetId).orElseThrow(() -> new EntityNotFoundException("QuestionSet not found"));
    //
    //     if (question.getQuestionSetIds() != null) {
    //         question.getQuestionSetIds().remove(questionSetId);
    //         questionRepository.save(question);
    //     }
    //
    //     if (questionSet.getQuestionIds() != null) {
    //         questionSet.getQuestionIds().remove(questionId);
    //         questionSetRepository.save(questionSet);
    //     }
    // }

    // private QuestionSetDO getQuestionSetWithQuestions(Long questionSetId) {
    // QuestionSetEntity questionSet = questionSetRepository.findById(questionSetId).orElseThrow(() -> new EntityNotFoundException("QuestionSet not found with id: " + questionSetId));
    // QuestionSetDO questionSetDO = questionSetEntityDOMapper.toQuestionSetDO(questionSet);
    //
    // // 如果需要获取问题的详细信息，可以这样做：
    // // List<QuestionEntity> questions = questionRepository.findAllById(questionSet.getQuestionIds());
    // // List<QuestionDO> questionDOs = questions.stream()
    // //         .map(questionEntityDOMapper::toQuestionDO)
    // //         .collect(Collectors.toList());
    // // questionSetDO.setQuestions(questionDOs);
    //
    // // 如果只需要问题ID，可以直接设置：
    // questionSetDO.setQuestionIds(questionSet.getQuestionIds());
    //
    // return questionSetDO;
    // }

    private QuestionSetDO getQuestionSetWithQuestions(QuestionSetEntity questionSet) {
        QuestionSetDO questionSetDO = questionSetEntityDOMapper.toQuestionSetDO(questionSet);
        List<Long> questions = getQuestionIdsByQuestionSetId(questionSet.getId());
        questionSetDO.setQuestionIds(new HashSet<>(questions));
        return questionSetDO;
    }

    @Override
    public List<SectionQuestionsDO> getSectionsByQuestionIds(List<Long> questionIds) {
        // 步骤1：获取给定questionIds的所有QuestionSliceEntities
        List<QuestionSliceEntity> questionSliceEntities = questionSliceRepository.findByQuestionIdIn(questionIds);

        // 步骤2：按sliceId对QuestionSliceEntities进行分组
        Map<Long, List<QuestionSliceEntity>> sliceIdToQuestionSlices = questionSliceEntities.stream().collect(Collectors.groupingBy(QuestionSliceEntity::getSliceId));

        // 步骤3：获取所有唯一的sliceIds
        Set<Long> sliceIds = sliceIdToQuestionSlices.keySet();

        // 步骤4：获取这些sliceIds对应的SliceDOs
        List<SliceDO> sliceDOs = knowledgeDomain.getSlicesByIds(new ArrayList<>(sliceIds));

        // 步骤5：按sectionCode对SliceDOs进行分组
        Map<String, List<SliceDO>> sectionCodeToSlices = sliceDOs.stream().collect(Collectors.groupingBy(SliceDO::getSectionCode));

        // 步骤6：获取所有唯一的sectionCodes
        Set<String> sectionCodes = sectionCodeToSlices.keySet();

        // 步骤7：获取这些sectionCodes对应的SectionDOs
        List<SectionDO> sectionDOs = knowledgeDomain.getSectionsBySectionCodes(new ArrayList<>(sectionCodes));

        // 步骤8：创建SectionQuestionsDOs
        List<SectionQuestionsDO> result = new ArrayList<>();
        for (SectionDO sectionDO : sectionDOs) {
            SectionQuestionsDO sectionQuestionsDO = new SectionQuestionsDO();
            sectionQuestionsDO.setSectionCode(sectionDO.getSectionCode());
            sectionQuestionsDO.setSectionName(sectionDO.getSectionName());

            List<Long> sectionQuestionIds = sectionCodeToSlices.get(sectionDO.getSectionCode()).stream().flatMap(sliceDO -> sliceIdToQuestionSlices.get(sliceDO.getId()).stream()).map(QuestionSliceEntity::getQuestionId).distinct().collect(Collectors.toList());

            sectionQuestionsDO.setQuestionIds(sectionQuestionIds);
            result.add(sectionQuestionsDO);
        }

        // 步骤9：按sectionCode对结果进行排序
        result.sort(Comparator.comparing(SectionQuestionsDO::getSectionCode));

        // 步骤10：移除重复的题目ID，保留第一次出现的（即sectionCode最小的）
        Set<Long> processedQuestionIds = new HashSet<>();
        for (SectionQuestionsDO sectionQuestions : result) {
            List<Long> uniqueQuestionIds = sectionQuestions.getQuestionIds().stream().filter(processedQuestionIds::add).collect(Collectors.toList());
            sectionQuestions.setQuestionIds(uniqueQuestionIds);
        }

        return result;
    }

    // @Override
    // @Transactional(readOnly = true)
    // public SectionDO getSectionBySectionCode(String sectionCode) {
    //     // 调用KnowledgeDomain的方法获取节信息
    //     List<SectionDO> sections = knowledgeDomain.getSectionsBySectionCodes(Collections.singletonList(sectionCode));
    //
    //     if (sections.isEmpty()) {
    //         throw new EntityNotFoundException("Section not found with code: " + sectionCode);
    //     }
    //
    //     SectionDO section = sections.get(0);
    //
    //     // 获取该节的所有切片
    //     List<SliceDO> slices = knowledgeDomain.findSlicesBySectionCode(sectionCode);
    //
    //     // 设置切片ID列表
    //     section.setSliceIds(slices.stream().map(SliceDO::getId).collect(Collectors.toList()));
    //
    //     // 获取该节的所有题目ID
    //     List<Long> questionIds = getQuestionIdsBySectionCode(sectionCode);
    //     section.setQuestionIds(questionIds);
    //
    //     return section;
    // }

    @Override
    public List<QuestionSetDO> getQuestionSetsByIds(List<Long> ids) {
        List<QuestionSetEntity> questionSetEntities = questionSetRepository.findByIdInAndDelFlagOrderByIdAsc(ids, false);
        return questionSetEntities.stream().map(this::convertToQuestionSetDO).collect(Collectors.toList());
    }

    /**
     * 将 QuestionSetEntity 转换为 QuestionSetDO
     *
     * @param entity QuestionSetEntity 对象
     * @return QuestionSetDO 对象
     */
    private QuestionSetDO convertToQuestionSetDO(QuestionSetEntity entity) {
        QuestionSetDO questionSetDO = new QuestionSetDO();
        questionSetDO.setId(entity.getId());
        questionSetDO.setCreateTime(entity.getCreateTime());
        questionSetDO.setUpdateTime(entity.getUpdateTime());
        questionSetDO.setDelFlag(entity.getDelFlag());
        questionSetDO.setName(entity.getName());
        questionSetDO.setRemark(entity.getRemark());
        questionSetDO.setCertificateCode(entity.getCertificateCode());
        questionSetDO.setSubjectCode(entity.getSubjectCode());
        questionSetDO.setType(entity.getType());
        questionSetDO.setYear(entity.getYear());
        return questionSetDO;
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionSetDO> getQuestionSetsBySubjectAndType(String subjectCode, QuestionSetType type) {
        List<QuestionSetEntity> questionSets = questionSetRepository.findBySubjectCodeAndTypeAndDelFlagOrderByIdDesc(subjectCode, type, false);
        List<QuestionSetDO> questionSetDOList = questionSetEntityDOMapper.toQuestionSetDOList(questionSets);
        Map<Long, List<Long>> mapQuestionIdsByQuestionSetId = Optional.ofNullable(getMapQuestionIdsByQuestionSetId(questionSets.stream().map(q -> q.getId()).collect(Collectors.toList()))).orElse(new HashMap<>());
        questionSetDOList.forEach(q -> q.setQuestionIds(new HashSet<>(Optional.ofNullable(mapQuestionIdsByQuestionSetId.get(q.getId())).orElse(new ArrayList<>()))));
        return questionSetDOList;
    }

    // @Override
    // @Transactional(readOnly = true)
    // public List<QuestionSetDO> findQuestionSets(Long questionId) {
    //     // 1. 查询题目信息获取关联的题库 Id 集合
    //     Optional<QuestionEntity> questionOpt = questionRepository.findById(questionId);
    //     if (questionOpt.isEmpty()) {
    //         return Collections.emptyList();
    //     }
    //
    //     Set<Long> questionSetIds = questionOpt.get().getQuestionSetIds();
    //     if (CollectionUtils.isEmpty(questionSetIds)) {
    //         return Collections.emptyList();
    //     }
    //
    //     // 2. 批量查询题库信息
    //     List<QuestionSetEntity> questionSetEntities = questionSetRepository.findByIdInAndDelFlagOrderByIdAsc(new ArrayList<>(questionSetIds), false);
    //
    //     // 3. 转换为 DO 对象
    //     return questionSetEntities.stream().map(questionSetEntityDOMapper::toQuestionSetDO).collect(Collectors.toList());
    // }

    @Override
    @Transactional
    public QuestionDO saveQuestion(QuestionDO questionDO, Boolean needCheck) {
        // 转换为实体对象
        QuestionEntity saveEntity = questionEntityDOMapper.toQuestionEntity(questionDO);
        saveEntity.setScore(0D);
        saveEntity.setDelFlag(0);
        saveEntity.setChangedByUser(1);
        saveEntity.setCopyFromId(ObjectUtils.defaultIfNull(saveEntity.getCopyFromId(), 0L));

        // 新增时设置为待提交审核
        if (saveEntity.getStatus() == null) {
            saveEntity.setStatus(QuestionStatus.PENDING_SUBMIT.getValue());
        }

        boolean isUpdate = questionDO.getId() != null;
        if (isUpdate) {
            // 更新时，检查题目状态
            QuestionEntity existingEntity = questionRepository.findById(questionDO.getId()).orElseThrow(() -> new EntityNotFoundException("题目不存在"));

            if (Boolean.TRUE.equals(needCheck)) {
                validateQuestionCanChange(questionDO, existingEntity);
            }

            // 保持一些字段不变
            saveEntity.setStatus(existingEntity.getStatus());
            saveEntity.setCreateTime(existingEntity.getCreateTime());
            saveEntity.setCreateBy(existingEntity.getCreateBy());
            // remark 为空，则不更新
            saveEntity.setRemark(saveEntity.getRemark() == null ? existingEntity.getRemark() : saveEntity.getRemark());
            saveEntity.setDelFlag(existingEntity.getDelFlag());
            saveEntity.setExternalId(existingEntity.getExternalId());
            saveEntity.setSourceChangeDate(existingEntity.getSourceChangeDate());
            saveEntity.setCopyFromId(existingEntity.getCopyFromId());
        }

        // 保存实体
        QuestionEntity savedEntity = questionRepository.save(saveEntity);

        // 如果是新建题目且有复制源题目 Id，则同步复制源题目的题库关联关系
        Long copyFromId = questionDO.getCopyFromId();
        if (!isUpdate && copyFromId != null && copyFromId > 0L) {
            copyQuestionRelations(savedEntity.getId(), copyFromId, questionDO.getCreateBy());
        }

        // 转换回 DO 并返回
        return questionEntityDOMapper.toQuestionDO(savedEntity);
    }

    private void validateQuestionCanChange(QuestionDO questionDO, QuestionEntity existingEntity) {
        if (!Objects.equals(existingEntity.getStatus(), QuestionStatus.PENDING_SUBMIT.getValue())) {
            throw new IllegalStateException("只有待提交审核状态的题目才能修改");
        }

        // 如果更新非待提交审核状态的题目，需要校验数据完整性（但是目前只支持修改待提交审核，所以先注释掉）
//        if (!Objects.equals(existingEntity.getStatus(), QuestionStatus.PENDING_SUBMIT.getValue())) {
//            List<String> errors = validateQuestion(questionDO);
//            if (CollectionUtils.isNotEmpty(errors)) {
//                throw new IllegalArgumentException(String.join("；", errors));
//            }
//        }
    }

    @Override
    @Transactional
    public List<QuestionDO> createQuestions(List<QuestionDO> questionDOs) {
        // 空列表校验
        if (CollectionUtils.isEmpty(questionDOs)) {
            return Collections.emptyList();
        }

        // 转换并初始化题目实体
        List<QuestionEntity> toCreate = questionDOs.stream().map(question -> {
            QuestionEntity createEntity = questionEntityDOMapper.toQuestionEntity(question);
            // 设置题目初始状态和必要字段
            createEntity.setStatus(QuestionStatus.PENDING_SUBMIT.getValue());
            createEntity.setScore(0D);
            createEntity.setDelFlag(0);
            createEntity.setChangedByUser(1);
            createEntity.setCopyFromId(ObjectUtils.defaultIfNull(createEntity.getCopyFromId(), 0L));
            return createEntity;
        }).toList();

        // 批量保存题目
        questionRepository.saveAll(toCreate);

        // 转换并返回创建成功的题目
        return toCreate.stream().map(questionEntityDOMapper::toQuestionDO).toList();
    }

    @Override
    public void copyQuestionRelations(Long questionId, Long copyFromId, String updateBy) {
        if (questionId == null || questionId <= 0L) {
            log.info("copyQuestionRelations questionId is null or <= 0");
            return;
        }
        if (copyFromId == null || copyFromId <= 0L) {
            log.info("copyQuestionRelations copyFromId is null or <= 0");
            return;
        }

        // 查询复制源题库关联关系
        List<QuestionSetQuestionEntity> sourceRelations = questionSetQuestionRepository.findByQuestionIdAndDelFlag(copyFromId, 0);
        if (CollectionUtils.isEmpty(sourceRelations)) {
            log.info("copyQuestionRelations sourceRelations is empty, questionId={}, copyFromId={}", questionId, copyFromId);
            return;
        }

        // 构建新的题库关联关系
        List<QuestionSetQuestionEntity> newRelations = sourceRelations.stream().map(relation -> QuestionSetQuestionEntity.builder().createBy(updateBy).updateBy(updateBy).delFlag(0).questionSetId(relation.getQuestionSetId()).questionId(questionId).status(QuestionStatus.PENDING_SUBMIT.getValue()) // 复制后的新题目默认都是待提交审核状态，所以关联题目状态也设置为待提交审核
                .sortNum(0)  // 默认排序号
                .build()).collect(Collectors.toList());

        // 批量保存题库关联关系
        questionSetQuestionRepository.saveAll(newRelations);
    }

    @Override
    public List<String> validateQuestion(QuestionDO questionDO) {
        List<String> errors = new ArrayList<>();

        // 校验证书、科目、题目类型
        if (StringUtils.isBlank(questionDO.getCertificateCode())) {
            errors.add("证书代码不能为空");
        }
        if (StringUtils.isBlank(questionDO.getSubjectCode())) {
            errors.add("科目代码不能为空");
        }
        if (questionDO.getType() == null) {
            errors.add("题目类型不能为空");
        }

        if (!errors.isEmpty()) {
            return errors;
        }

        // 根据题型进行特殊校验
        SubQuestionInfoDO questionInfo = questionEntityDOMapper.toSubQuestionInfo(questionDO);
        validateByQuestionType(questionDO, questionInfo, errors);

        return errors;
    }

    private void validateByQuestionType(QuestionDO questionDO, SubQuestionInfoDO questionInfo, List<String> errors) {
        try {
            final String subjectCode = questionDO.getSubjectCode();
            switch (questionDO.getType()) {
                case CHOICE -> validateSingleChoiceQuestion(questionInfo, errors);
                case CHOICES -> validateMultipleChoiceQuestion(questionInfo, subjectCode, errors);
                case INDEFINITE_CHOICE -> validateIndefiniteChoiceQuestion(questionInfo, subjectCode, errors);
                case TRUE_FALSE -> validateTrueFalseQuestion(questionInfo, errors);
                case OPEN -> validateEssayQuestion(questionInfo, errors);
                case CASE_ANALYSIS_ESSAY, CASE_ANALYSIS_CHOICES -> validateCaseAnalysisQuestion(questionDO, errors);
            }
        } catch (IllegalArgumentException e) {
            errors.add("无效的题目类型：" + questionDO.getType());
        }
    }

    /**
     * 校验案例分析题
     */
    private void validateCaseAnalysisQuestion(QuestionDO questionDO, List<String> errors) {
        // 校验题目内容
        if (StringUtils.isBlank(questionDO.getContent())) {
            errors.add("题目内容不能为空");
        }
        if (containsSensitiveWords(questionDO.getContent())) {
            errors.add("题目内容包含敏感词");
        }

        // 子题目列表不能为空
        Map<String, SubQuestionInfoDO> caseAnalysisQuestion = questionDO.getCaseAnalysisQuestion();
        if (caseAnalysisQuestion == null || caseAnalysisQuestion.isEmpty()) {
            errors.add("案例分析题必须包含子题目");
            return;
        }

        // 子题目数量校验
        if (caseAnalysisQuestion.size() < 2) {
            errors.add("案例分析题至少需要包含2道子题目");
            return;
        }
        // 子题目数量上限校验
        if (caseAnalysisQuestion.size() > 5) {
            errors.add("案例分析题最多包含5道子题目");
        }

        // 如果基础内容校验不通过，直接返回
        if (!errors.isEmpty()) {
            return;
        }

        // 检查子题目序号的连续性
        validateSubQuestionSequence(caseAnalysisQuestion.keySet(), errors);
        if (!errors.isEmpty()) {
            return;
        }

        // 校验每个子题目
        for (Map.Entry<String, SubQuestionInfoDO> entry : caseAnalysisQuestion.entrySet()) {
            validateCaseAnalysisSubQuestion(entry, questionDO.getSubjectCode(), questionDO.getType(), errors);
        }
    }

    /**
     * 校验子题目序号的连续性
     */
    private void validateSubQuestionSequence(Set<String> orderNumbers, List<String> errors) {
        // 序号必须是 (1)(2)(3)... 的格式
        String expectedPattern = "^\\([1-9]\\)$";

        // 收集所有序号并排序
        List<Integer> numbers = orderNumbers.stream().filter(order -> order.matches(expectedPattern)).map(order -> Integer.parseInt(order.substring(1, order.length() - 1))).sorted().toList();

        // 检查序号数量是否匹配
        if (numbers.size() != orderNumbers.size()) {
            errors.add("子题目序号格式必须为(1)(2)(3)...");
            return;
        }

        // 检查序号是否连续
        for (int i = 0; i < numbers.size(); i++) {
            if (numbers.get(i) != i + 1) {
                errors.add("子题目序号必须连续");
                return;
            }
        }
    }

    private void validateCaseAnalysisSubQuestion(Map.Entry<String, SubQuestionInfoDO> entry, String subjectCode, QuestionType questionType, List<String> errors) {
        SubQuestionInfoDO questionInfoDO = entry.getValue();
        try {
            switch (questionType) {
                case CASE_ANALYSIS_ESSAY:
                    validateEssayQuestion(questionInfoDO, errors);
                    break;
                case CASE_ANALYSIS_CHOICES:
                    validateMultipleChoiceQuestion(questionInfoDO, subjectCode, errors);
                    break;
            }
        } catch (IllegalArgumentException e) {
            errors.add("无效的子题目类型：" + questionType);
        }
    }

    /**
     * 校验单选题答案
     */
    private void validateSingleChoiceQuestion(SubQuestionInfoDO questionInfoDO, List<String> errors) {
        validateBasicChoiceQuestion(questionInfoDO, errors, 4, // 最少 4 个选项
                4, // 最多 4 个选项
                1, // 必须选 1 个答案
                1, // 必须选 1 个答案
                "单选题");
    }

    /**
     * 校验多选题答案
     */
    private void validateMultipleChoiceQuestion(SubQuestionInfoDO questionInfoDO, String subjectCode, List<String> errors) {
        // 多选择题选项需要 5 个；
        // 建设工程技术与计量（安装）选项需要 4 个；
        List<String> specialSubjectCodes = Lists.newArrayList("YZ-JSGCJSYJL-AZ");
        int minOptions = specialSubjectCodes.contains(subjectCode) ? 4 : 5;
        int maxOptions = specialSubjectCodes.contains(subjectCode) ? 4 : 5;
        validateBasicChoiceQuestion(questionInfoDO, errors, minOptions, // 根据科目确定最少选项数
                maxOptions, // 根据科目确定最多选项数
                2, // 最少 2 个答案
                null, // 不限制最多答案数
                "多选题");
    }

    /**
     * 校验不定项选择题答案
     */
    private void validateIndefiniteChoiceQuestion(SubQuestionInfoDO questionInfoDO, String subjectCode, List<String> errors) {
        // 不定项选择题选项需要 4 个；
        // 安全生产专业实务-建筑施工安全、安全生产专业实务-化工安全、安全生产专业实务-其他安全(不包括消防安全）选项需要 5 个；
        List<String> specialSubjectCodes = Lists.newArrayList("ZZA-AQSCZYSW-JZSGAQ", "ZZA-AQSCZYSW-HGAQ", "ZZA-AQSCZYSW-QTAQBBKXFAQ");
        int minOptions = specialSubjectCodes.contains(subjectCode) ? 5 : 4;
        int maxOptions = specialSubjectCodes.contains(subjectCode) ? 5 : 4;
        validateBasicChoiceQuestion(questionInfoDO, errors, minOptions, // 根据科目确定最少选项数
                maxOptions, // 根据科目确定最多选项数
                1, // 最少 1 个答案
                null, // 不限制最多答案数
                "不定项选择题");
    }

    /**
     * 统一校验选择题
     */
    @SuppressWarnings("SameParameterValue")
    private void validateBasicChoiceQuestion(SubQuestionInfoDO questionInfoDO, List<String> errors, int minOptions, Integer maxOptions,  // 可选参数
                                             int minAnswers, Integer maxAnswers,  // 可选参数
                                             String questionTypeName) {
        // 基础校验
        validateBasicQuestion(questionInfoDO, errors);
        // 判断题必须有解析
        if (StringUtils.isBlank(questionInfoDO.getExplanation())) {
            errors.add("题目解析不能为空");
        }
        if (!errors.isEmpty()) {
            return;
        }

        // 选项校验
        Map<String, String> options = questionInfoDO.getOptions();
        if (options == null || options.isEmpty()) {
            errors.add(questionTypeName + "必须包含选项");
            return;
        }

        // 选项数量校验
        if (options.size() < minOptions) {
            errors.add(questionTypeName + "最少需要" + minOptions + "个选项");
            return;
        }
        if (maxOptions != null && options.size() > maxOptions) {
            errors.add(questionTypeName + "最多" + maxOptions + "个选项");
            return;
        }

        // 校验选项内容
        for (Map.Entry<String, String> option : options.entrySet()) {
            String optionValue = option.getValue();
            String optionKey = option.getKey();

            if (StringUtils.isBlank(optionValue)) {
                errors.add("选项内容不能为空");
                continue;
            }

            if (containsSensitiveWords(optionValue)) {
                errors.add("选项内容包含敏感词");
                continue;
            }

            // 检查选项内容是否包含 A-I 加点号的格式
            if (optionValue.matches(".*[A-I]\\..*")) {
                errors.add(String.format("选项%s的内容不能包含A-I的选项字母标识", optionKey));
            }
        }

        // 答案校验
        String answer = StringUtils.trimToEmpty(questionInfoDO.getAnswer());
        if (answer.length() < minAnswers) {
            errors.add(questionTypeName + "最少需要" + minAnswers + "个答案");
            return;
        }
        if (maxAnswers != null && answer.length() > maxAnswers) {
            errors.add(questionTypeName + "最多" + maxAnswers + "个答案");
            return;
        }

        // 答案有效性校验
        Set<String> uniqueAnswers = new HashSet<>();
        for (char ans : answer.toCharArray()) {
            String option = String.valueOf(ans);

            // 检查重复选项
            if (!uniqueAnswers.add(option)) {
                errors.add(questionTypeName + "答案不能包含重复选项");
                return;
            }

            // 检查选项是否存在
            if (!options.containsKey(option)) {
                errors.add(questionTypeName + "答案必须是已有选项");
                return;
            }
        }
    }

    /**
     * 校验简答题
     */
    private void validateEssayQuestion(SubQuestionInfoDO questionInfoDO, List<String> errors) {
        // 基础校验
        validateBasicQuestion(questionInfoDO, errors);
    }

    /**
     * 校验判断题
     */
    private void validateTrueFalseQuestion(SubQuestionInfoDO questionInfoDO, List<String> errors) {
        // 基础校验
        validateBasicQuestion(questionInfoDO, errors);
        // 判断题必须有解析
        if (StringUtils.isBlank(questionInfoDO.getExplanation())) {
            errors.add("题目解析不能为空");
        }
        if (!errors.isEmpty()) {
            return;
        }

        String answer = questionInfoDO.getAnswer().trim();
        // 答案必须是 T 或 F
        if (!answer.equals("T") && !answer.equals("F")) {
            errors.add("判断题答案必须是T或F");
        }
    }

    /**
     * 校验基础信息，包含敏感词
     */
    private void validateBasicQuestion(SubQuestionInfoDO questionInfoDO, List<String> errors) {
        if (StringUtils.isBlank(questionInfoDO.getContent())) {
            errors.add("题目内容不能为空");
        }
        if (containsSensitiveWords(questionInfoDO.getContent())) {
            errors.add("题目内容包含敏感词");
        }
        if (StringUtils.isBlank(questionInfoDO.getAnswer())) {
            errors.add("题目答案不能为空");
        }
        if (containsSensitiveWords(questionInfoDO.getAnswer())) {
            errors.add("题目答案包含敏感词");
        }
        if (containsSensitiveWords(questionInfoDO.getExplanation())) {
            errors.add("题目解析包含敏感词");
        }
    }

    /**
     * 检查文本是否包含敏感词
     */
    private boolean containsSensitiveWords(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }

        String lowerText = text.toLowerCase();
        return SENSITIVE_WORDS.stream().anyMatch(word -> lowerText.contains(word.toLowerCase()));
    }

    @Override
    @Transactional
    public QuestionAutoApproveResultDO autoApproveQuestions(List<Long> ids, String updateBy, Boolean needRepeatCheck) {
        if (CollectionUtils.isEmpty(ids)) {
            return new QuestionAutoApproveResultDO();
        }

        // 结果对象
        QuestionAutoApproveResultDO result = new QuestionAutoApproveResultDO();
        List<Long> approvedIds = new ArrayList<>();
        Map<Long, String> failedIds = new HashMap<>();

        // 1. 批量查询待审核的题目
        List<QuestionEntity> entities = questionRepository.findAllById(ids);
        if (entities.isEmpty()) {
            return result;
        }

        // 2. 获取查重索引
        Map<String, QuestionEntity> duplicateCheckMap = buildDuplicateCheckMap(entities);

        // 3. 分组校验
        List<QuestionEntity> toUpdateEntities = new ArrayList<>();
        for (QuestionEntity entity : entities) {
            try {
                // 校验（包含重复校验）
                List<String> validationErrors = validateAutoApprove(entity, duplicateCheckMap, needRepeatCheck);
                if (!validationErrors.isEmpty()) {
                    failedIds.put(entity.getId(), String.join(";", validationErrors));
                    continue;
                }

                // 校验通过,加入待更新列表
                entity.setStatus(QuestionStatus.APPROVED.getValue());
                entity.setUpdateBy(StringUtils.defaultIfBlank(updateBy, "unknownOperator"));
                toUpdateEntities.add(entity);
                approvedIds.add(entity.getId());
            } catch (Exception e) {
                failedIds.put(entity.getId(), "审核异常:" + e.getMessage());
            }
        }

        // 4. 批量更新状态
        if (!toUpdateEntities.isEmpty()) {
            questionRepository.saveAll(toUpdateEntities);
        }

        // 5. 更新题目和题库关联关系状态，审核通过
        if (!CollectionUtils.isEmpty(approvedIds)) {
            updateQuestionRelationsStatus(approvedIds, QuestionStatus.APPROVED.getValue(), updateBy);
        }

        result.setApprovedQuestionIds(approvedIds);
        result.setFailedQuestionIds(failedIds);

        return result;
    }

    /**
     * 构建查重索引
     *
     * @param entities 待审核的题目列表
     * @return 已审核题目的查重索引
     */
    private Map<String, QuestionEntity> buildDuplicateCheckMap(List<QuestionEntity> entities) {
        // 1. 获取待审核题目的特征信息
        List<String> certificateCodes = new ArrayList<>();
        List<String> subjectCodes = new ArrayList<>();
        List<QuestionType> types = new ArrayList<>();

        for (QuestionEntity entity : entities) {
            certificateCodes.add(entity.getCertificateCode());
            subjectCodes.add(entity.getSubjectCode());
            types.add(entity.getType());
        }

        // 2. 构建查询条件，查询可能重复的已审核题目
        Specification<QuestionEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 已审核状态
            predicates.add(cb.equal(root.get("status"), QuestionStatus.APPROVED.getValue()));
            // 未删除
            predicates.add(cb.equal(root.get("delFlag"), 0));
            // 证书代码
            predicates.add(root.get("certificateCode").in(certificateCodes));
            // 科目代码
            predicates.add(root.get("subjectCode").in(subjectCodes));
            // 题目类型
            predicates.add(root.get("type").in(types));

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 3. 执行查询并建立查重索引
        List<QuestionEntity> possibleDuplicates = questionRepository.findAll(spec);
        return possibleDuplicates.stream().collect(Collectors.toMap(q -> String.format("%s_%s_%s_%s", q.getCertificateCode(), q.getSubjectCode(), q.getType(), q.getContent().replaceAll("\\s+", "")), q -> q, (e1, e2) -> e1));
    }

    /**
     * 校验自动审核条件
     *
     * @param entity            待审核题目
     * @param duplicateCheckMap 查重索引
     * @param needRepeatCheck   是否需要重复校验
     * @return 校验错误信息列表
     */
    private List<String> validateAutoApprove(QuestionEntity entity, Map<String, QuestionEntity> duplicateCheckMap, Boolean needRepeatCheck) {
        List<String> errors = new ArrayList<>();

        // 1. 校验状态
        Set<Integer> canAutoApproveStatuses = Set.of(QuestionStatus.PENDING_SUBMIT.getValue());
        Integer status = entity.getStatus();
        if (!canAutoApproveStatuses.contains(status)) {
            errors.add(String.format("题目(%d)是(%d)状态，不支持自动审核", entity.getId(), status));
            return errors;
        }

        // 2. 校验数据完整性
        QuestionDO questionDO = questionEntityDOMapper.toQuestionDO(entity);
        errors.addAll(validateQuestion(questionDO));
        if (!errors.isEmpty()) {
            return errors;
        }

        // 3. 重复校验
        if (Boolean.TRUE.equals(needRepeatCheck)) {
            String checkKey = String.format("%s_%s_%s_%s", entity.getCertificateCode(), entity.getSubjectCode(), entity.getType(), entity.getContent().replaceAll("\\s+", ""));

            QuestionEntity existingQuestion = duplicateCheckMap.get(checkKey);
            if (existingQuestion != null) {
                errors.add(String.format("与已有题目(ID:%d)重复：相同科目代码、证书代码、题目类型和内容", existingQuestion.getId()));
            }
        }

        return errors;
    }

    @Override
    @Transactional
    public void batchDelete(List<Long> ids, String updateBy) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 1. 批量查询要更新的实体
        List<QuestionEntity> entities = questionRepository.findAllById(ids);
        if (entities.isEmpty()) {
            return;
        }

        // 2. 收集已审核通过的题目并校验
        Map<Long, String> failureInfos = entities.stream().map(question -> Map.entry(question.getId(), Optional.ofNullable(validateQuestionDeletion(question.getId())))).filter(entry -> entry.getValue().isPresent()).collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(), (v1, v2) -> v1, LinkedHashMap::new));

        // 3. 如果有不能删除的题目，抛出异常
        Optional.of(failureInfos).filter(infos -> !infos.isEmpty()).ifPresent(infos -> {
            String errorMsg = ErrorMessageUtils.buildErrorMessage("以下题目无法删除", infos);
            throw new IllegalStateException(errorMsg);
        });

        // 4. 执行批量删除
        questionRepository.saveAll(entities.stream().peek(entity -> {
            entity.setDelFlag(1);
            entity.setUpdateBy(StringUtils.defaultIfBlank(updateBy, "unknownOperator"));
        }).collect(Collectors.toList()));

        // 5. 删除题目和题库的关联关系
        deleteQuestionSetRelations(ids, updateBy);
    }

    /**
     * 校验题目是否可以删除
     *
     * @param questionId 题库 id
     * @return 不可删除原因，如果可以删除则返回 null
     */
    private String validateQuestionDeletion(Long questionId) {
        // 定义不可删除的题库类型列表
        List<QuestionSetType> unDeletableTypes = Arrays.asList(QuestionSetType.REAL_EXAMS, QuestionSetType.MOCK_EXAMS);

        // 查询题目关联题库
        List<QuestionSetQuestionEntity> relations = questionSetQuestionRepository.findByQuestionIdAndDelFlag(questionId, 1);

        Set<Long> questionSetIds = Optional.ofNullable(relations).orElse(Lists.newArrayList()).stream().map(QuestionSetQuestionEntity::getQuestionSetId).collect(Collectors.toSet());

        // 批量查询题库信息
        List<QuestionSetEntity> questionSetEntities = questionSetRepository.findByIdInAndDelFlagOrderByIdAsc(new ArrayList<>(questionSetIds), false);

        // 查找是否有不可删除的题库类型
        Optional<QuestionSetEntity> examSet = questionSetEntities.stream().filter(qs -> unDeletableTypes.contains(qs.getType())).findFirst();

        return examSet.map(qs -> String.format("已关联到考试模式题库，题组类型：%s，题组名称：%s", qs.getType().getDescription(), qs.getName())).orElse(null);
    }

    @Override
    @Transactional
    public void batchDeleteWithoutCheck(List<Long> ids, String updateBy) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 1. 批量查询要更新的实体
        List<QuestionEntity> entities = questionRepository.findAllById(ids);
        if (entities.isEmpty()) {
            return;
        }

        // 2. 执行批量删除
        questionRepository.saveAll(entities.stream().peek(entity -> {
            entity.setDelFlag(1);
            entity.setUpdateBy(updateBy);
        }).collect(Collectors.toList()));

        // 3. 删除题目和题库的关联关系
        deleteQuestionSetRelations(ids, updateBy);
    }

    @Override
    @Transactional
    public void batchUpdateStatus(List<Long> ids, Integer targetStatus, String updateBy) {
        if (CollectionUtils.isEmpty(ids) || targetStatus == null) {
            return;
        }

        // 1. 批量查询要更新的实体
        List<QuestionEntity> entities = questionRepository.findAllById(ids);
        if (entities.isEmpty()) {
            return;
        }

        // 2. 校验状态变更的合法性
        Map<Long, String> transitionErrors = entities.stream().map(question -> Map.entry(question.getId(), Optional.ofNullable(validateStatusTransition(question.getStatus(), targetStatus)))).filter(entry -> entry.getValue().isPresent()).collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(), (v1, v2) -> v1, LinkedHashMap::new));
        Map<Long, String> failureInfos = new LinkedHashMap<>(transitionErrors);

        // 3. 提交审核时校验数据完整性
        if (QuestionStatus.REVIEWING.getValue().equals(targetStatus)) {
            Map<Long, String> completenessErrors = entities.stream().map(question -> {
                QuestionDO questionDO = questionEntityDOMapper.toQuestionDO(question);
                List<String> errors = validateQuestion(questionDO);
                String errorStr = CollectionUtils.isEmpty(errors) ? null : String.join("；", errors);
                return Map.entry(question.getId(), Optional.ofNullable(errorStr));
            }).filter(entry -> entry.getValue().isPresent()).collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(), (v1, v2) -> v1, LinkedHashMap::new));

            failureInfos.putAll(completenessErrors);
        }

        // 4. 如果有状态变更不合法的情况，抛出异常
        Optional.of(failureInfos).filter(infos -> !infos.isEmpty()).ifPresent(infos -> {
            String errorMsg = ErrorMessageUtils.buildErrorMessage("以下题目状态更新失败", infos);
            throw new IllegalStateException(errorMsg);
        });

        // 5. 执行批量更新状态
        questionRepository.saveAll(entities.stream().peek(entity -> {
            entity.setStatus(targetStatus);
            entity.setUpdateBy(StringUtils.defaultIfBlank(updateBy, "unknownOperator"));
        }).collect(Collectors.toList()));

        // 6. 更新题目和题库关联关系状态
        updateQuestionRelationsStatus(entities.stream().map(QuestionEntity::getId).collect(Collectors.toList()), targetStatus, updateBy);
    }

    /**
     * 校验状态变更的合法性
     *
     * @param currentStatus 当前状态
     * @param targetStatus  目标状态
     * @return 校验错误信息，如果通过校验则返回 null
     */
    private String validateStatusTransition(Integer currentStatus, Integer targetStatus) {
        // 状态定义：0-待提交审核、1-审核中、2-审核通过、3-审核不通过
        if (currentStatus.equals(targetStatus)) {
            return null;
        }

        boolean isValid = switch (currentStatus) {
            case 0 -> targetStatus == 1; // 待提交审核 -> 审核中
            case 1 -> targetStatus == 2 || targetStatus == 3; // 审核中 -> 审核通过/不通过
            case 2 -> false; // 审核通过状态不能变更
            case 3 -> targetStatus == 0; // 审核不通过 -> 待提交审核
            default -> false;
        };

        if (!isValid) {
            return String.format("当前状态(%d)不能变更为目标状态(%d)", currentStatus, targetStatus);
        }

        return null;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    @Transactional(readOnly = true)
    public PageRespDO<QuestionDO> pageQuery(MgtQuestionQueryDO queryDO) {
        // 构建查询条件
        Specification<QuestionEntity> spec = (root, query, cb) -> {
            // 添加 distinct 来去重
            query.distinct(true);

            List<Predicate> predicates = new ArrayList<>();

            // 添加默认条件：未删除
            predicates.add(cb.equal(root.get("delFlag"), 0));

            // 根据 Id 列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getIds())) {
                predicates.add(root.get("id").in(queryDO.getIds()));
            }

            // 证书代码列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getCertificateCodes())) {
                predicates.add(root.get("certificateCode").in(queryDO.getCertificateCodes()));
            }

            // 科目代码列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getSubjectCodes())) {
                predicates.add(root.get("subjectCode").in(queryDO.getSubjectCodes()));
            }

            // 题目类型列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getTypes())) {
                predicates.add(root.get("type").in(queryDO.getTypes()));
            }

            // 题目内容模糊查询
            if (StringUtils.isNotBlank(queryDO.getContent())) {
                predicates.add(cb.like(root.get("content"), "%" + queryDO.getContent() + "%"));
            }

            // 状态列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getStatuses())) {
                predicates.add(root.get("status").in(queryDO.getStatuses()));
            }

            // 题库类型列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getQuestionSetTypes())) {
                // 先查询符合条件的题库 ID
                Subquery<Long> questionSetIdSubquery = query.subquery(Long.class);
                Root<QuestionSetEntity> questionSetRoot = questionSetIdSubquery.from(QuestionSetEntity.class);

                questionSetIdSubquery.select(questionSetRoot.get("id")).where(cb.and(questionSetRoot.get("type").in(queryDO.getQuestionSetTypes()), cb.equal(questionSetRoot.get("delFlag"), false)));

                // 再通过题库 ID 查询题目关联
                Subquery<Integer> questionSetQuestionSubquery = query.subquery(Integer.class);
                Root<QuestionSetQuestionEntity> questionSetQuestionRoot = questionSetQuestionSubquery.from(QuestionSetQuestionEntity.class);

                questionSetQuestionSubquery.select(cb.literal(1)).where(cb.and(cb.equal(questionSetQuestionRoot.get("questionId"), root.get("id")), questionSetQuestionRoot.get("questionSetId").in(questionSetIdSubquery), cb.equal(questionSetQuestionRoot.get("delFlag"), 0)));

                predicates.add(cb.exists(questionSetQuestionSubquery));
            }

            // 根据题库 Id 列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getQuestionSetIds())) {
                Subquery<Integer> subquery = query.subquery(Integer.class);
                Root<QuestionSetQuestionEntity> subRoot = subquery.from(QuestionSetQuestionEntity.class);

                subquery.select(cb.literal(1));
                subquery.where(cb.and(cb.equal(subRoot.get("questionId"), root.get("id")), subRoot.get("questionSetId").in(queryDO.getQuestionSetIds()), cb.equal(subRoot.get("delFlag"), 0)));

                predicates.add(cb.exists(subquery));
            }

            // 排除题库 Id 列表查询
            if (CollectionUtils.isNotEmpty(queryDO.getExcludeQuestionSetIds())) {
                Subquery<Integer> subquery = query.subquery(Integer.class);
                Root<QuestionSetQuestionEntity> subRoot = subquery.from(QuestionSetQuestionEntity.class);

                subquery.select(cb.literal(1));
                subquery.where(cb.and(cb.equal(subRoot.get("questionId"), root.get("id")), subRoot.get("questionSetId").in(queryDO.getExcludeQuestionSetIds()), cb.equal(subRoot.get("delFlag"), 0)));

                predicates.add(cb.not(cb.exists(subquery)));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 创建排序对象：按更新时间降序
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");

        // 创建分页请求
        PageRequest pageRequest = PageRequest.of(queryDO.getPageNum() - 1, queryDO.getPageSize(), sort);

        // 分别执行计数查询和分页查询
        long total = questionRepository.count(spec);
        List<QuestionEntity> content = questionRepository.findAll(spec, pageRequest).getContent();

        // 如果指定了目标题库 ID，查询该题库下的所有题目 ID
        Set<Long> targetQuestionSetQuestionIds = Collections.emptySet();
        if (queryDO.getTargetQuestionSetId() != null) {
            List<QuestionSetQuestionEntity> relations = questionSetQuestionRepository.findByQuestionSetIdAndDelFlag(queryDO.getTargetQuestionSetId(), 0);
            if (CollectionUtils.isNotEmpty(relations)) {
                targetQuestionSetQuestionIds = relations.stream().map(QuestionSetQuestionEntity::getQuestionId).collect(Collectors.toSet());
            }
        }

        // 转换结果
        Set<Long> finalTargetQuestionSetQuestionIds = targetQuestionSetQuestionIds;
        List<QuestionDO> questionDOList = content.stream().map(entity -> {
            if (queryDO.getTargetQuestionSetId() != null) {
                return questionEntityDOMapper.toQuestionDOWithTargetFlag(entity, finalTargetQuestionSetQuestionIds.contains(entity.getId()));
            } else {
                return questionEntityDOMapper.toQuestionDO(entity);
            }
        }).collect(Collectors.toList());

        return PageRespDO.of(questionDOList, total, queryDO.getPageNum(), queryDO.getPageSize());
    }

    @Override
    @Transactional(readOnly = true)
    public PageRespDO<QuestionDO> pageQueryQuestionSetQuestions(MgtQuestionSetQuestionQueryDO queryDO) {
        // 如果没有指定题库 ID 列表，返回空结果
        List<Long> questionSetIds = queryDO.getQuestionSetIds();
        if (CollectionUtils.isEmpty(questionSetIds)) {
            return PageRespDO.of(Collections.emptyList(), 0L, queryDO.getPageNum(), queryDO.getPageSize());
        }

        // 构建查询条件
        Specification<QuestionSetQuestionEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 基础条件
            predicates.add(cb.equal(root.get("delFlag"), 0));
            predicates.add(root.get("questionSetId").in(questionSetIds));

            // 子查询 - 查询符合条件的题目 ID
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<QuestionEntity> subRoot = subquery.from(QuestionEntity.class);
            List<Predicate> subPredicates = new ArrayList<>();

            subPredicates.add(cb.equal(subRoot.get("delFlag"), 0));
            subPredicates.add(cb.equal(root.get("questionId"), subRoot.get("id")));

            // 题目 ID 条件
            if (CollectionUtils.isNotEmpty(queryDO.getQuestionIds())) {
                subPredicates.add(subRoot.get("id").in(queryDO.getQuestionIds()));
            }

            // 题目类型条件
            if (StringUtils.isNotBlank(queryDO.getQuestionType())) {
                subPredicates.add(subRoot.get("type").in(queryDO.getQuestionType()));
            }

            // 题目内容条件
            if (StringUtils.isNotBlank(queryDO.getContent())) {
                subPredicates.add(cb.like(subRoot.get("content"), "%" + queryDO.getContent() + "%"));
            }

            subquery.select(subRoot.get("id")).where(cb.and(subPredicates.toArray(new Predicate[0])));

            predicates.add(root.get("questionId").in(subquery));

            // 设置排序：先按 sortNum 降序（空值最后），再按 updateTime 升序
            query.orderBy(cb.desc(cb.coalesce(root.get("sortNum"), Integer.MIN_VALUE)),  // 使用 MIN_VALUE 让空值排在最后
                    cb.asc(root.get("updateTime")));

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行分页查询
        PageRequest pageRequest = PageRequest.of(queryDO.getPageNum() - 1, queryDO.getPageSize());
        Page<QuestionSetQuestionEntity> page = questionSetQuestionRepository.findAll(spec, pageRequest);

        // 如果没有数据，返回空结果
        if (page.isEmpty()) {
            return PageRespDO.of(Collections.emptyList(), 0L, queryDO.getPageNum(), queryDO.getPageSize());
        }

        // 查询题目详情
        List<Long> questionIds = page.getContent().stream().map(QuestionSetQuestionEntity::getQuestionId).collect(Collectors.toList());

        List<QuestionEntity> questions = questionRepository.findByIdInAndDelFlag(questionIds, 0);
        Map<Long, QuestionEntity> questionMap = questions.stream().collect(Collectors.toMap(QuestionEntity::getId, q -> q));

        // 转换结果
        List<QuestionDO> questionDOList = page.getContent().stream().map(relation -> questionEntityDOMapper.toQuestionDOWithSortNum(questionMap.get(relation.getQuestionId()), relation.getSortNum())).collect(Collectors.toList());

        // 返回分页结果
        return PageRespDO.of(questionDOList, page.getTotalElements(), queryDO.getPageNum(), queryDO.getPageSize());
    }

    @Override
    public QuestionSetDO getQuestionSetByQuestionSetId(Long questionSetId) {
        Optional<QuestionSetEntity> byIdAndDelFlag = questionSetRepository.findByIdAndDelFlag(questionSetId, false);
        QuestionSetDO questionSetDO = byIdAndDelFlag.map(questionSetEntityDOMapper::toQuestionSetDO).orElse(new QuestionSetDO());
        List<Long> questionIdsByQuestionSetId = Optional.ofNullable(getQuestionIdsByQuestionSetId(questionSetDO.getId())).orElse(new ArrayList<>());
        questionSetDO.setQuestionIds(new HashSet<>(questionIdsByQuestionSetId));
        return questionSetDO;
    }

    @Override
    public QuestionSetType getQuestionSetTypeById(Long questionSetId) {
        Optional<QuestionSetEntity> questionSet = questionSetRepository.findByIdAndDelFlag(questionSetId, false);
        return questionSet.map(QuestionSetEntity::getType).orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public String validateQuestionDuplicate(QuestionDO questionDO, List<Long> excludeQuestionIds) {
        // 构建查询条件
        Specification<QuestionEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 基础条件
            predicates.add(cb.equal(root.get("delFlag"), 0));
            predicates.add(cb.equal(root.get("certificateCode"), questionDO.getCertificateCode()));
            predicates.add(cb.equal(root.get("subjectCode"), questionDO.getSubjectCode()));
            predicates.add(cb.equal(root.get("type"), questionDO.getType()));

            // 排除指定题目
            if (CollectionUtils.isNotEmpty(excludeQuestionIds)) {
                predicates.add(root.get("id").in(excludeQuestionIds).not());
            }

            // 内容匹配
            String normalizeQuestionContent = QuestionContentUtils.normalizeQuestionContent(questionDO.getContent());
            predicates.add(root.get("content").in(Arrays.asList(normalizeQuestionContent, questionDO.getContent())));

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Optional<QuestionEntity> duplicateQuestion = questionRepository.findOne(spec);

        // 如果找到重复题目,返回错误信息
        return duplicateQuestion.map(question -> String.format("与题目(Id:%d)重复：相同科目代码、证书代码、题目类型和内容", question.getId())).orElse(null);
    }

    @Override
    @Transactional
    public void deleteQuestionSetRelations(List<Long> questionIds, String updateBy) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return;
        }

        // 删除题目和题库关联关系（硬删除）
        questionSetQuestionRepository.deleteByQuestionIdIn(questionIds);
    }

    @Override
    public List<QuestionSetDO> findBySubjectCodeAndYearGreaterThanEqualAndType(String subjectCode, int year, QuestionSetType questionSetType, boolean delFlag) {
        List<QuestionSetEntity> questionSetEntities = questionSetRepository.findBySubjectCodeAndYearGreaterThanEqualAndTypeAndDelFlag(subjectCode, year, questionSetType, delFlag);
        return questionSetEntityDOMapper.toQuestionSetDOList(questionSetEntities);
    }

    @Override
    @Transactional
    public void updateQuestionRelationsStatus(List<Long> questionIds, Integer status, String updateBy) {
        List<QuestionSetQuestionEntity> entities = questionSetQuestionRepository.findByQuestionIdIn(questionIds);

        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        entities.forEach(entity -> {
            entity.setStatus(status);
            entity.setUpdateBy(updateBy);
        });
        questionSetQuestionRepository.saveAll(entities);
    }
}